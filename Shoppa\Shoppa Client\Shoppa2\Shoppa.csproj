﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7E5AAB9C-BF3C-418C-8C18-EBBC200D4E30}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Shoppa.PL.Windows</RootNamespace>
    <AssemblyName>Shoppa</AssemblyName>
    <SignAssembly>false</SignAssembly>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <SignManifests>false</SignManifests>
    <ManifestCertificateThumbprint>C5A6619DF09E758BC941C83E770ED00DA7774B0A</ManifestCertificateThumbprint>
    <ApplicationIcon>Shoppa3.ico</ApplicationIcon>
    <ManifestTimestampUrl>http://timestamp.globalsign.com/scripts/timstamp.dll</ManifestTimestampUrl>
    <TargetZone>LocalIntranet</TargetZone>
    <GenerateManifests>false</GenerateManifests>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>ftp://shoppa-deploy-1.ad.shoppa.com/install/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <InstallUrl>http://shoppa-deploy-1.ad.shoppa.com/install/</InstallUrl>
    <ProductName>Shoppa 2</ProductName>
    <PublisherName>Shoppa AB</PublisherName>
    <CreateWebPageOnPublish>true</CreateWebPageOnPublish>
    <WebPage>publish.htm</WebPage>
    <ApplicationRevision>1</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>Shoppa_1_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;PRE_SPLIT</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>CS1591,CS1573</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE;PRE_SPLIT</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;PRE_SPLIT</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>CS1591,CS1573</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE;PRE_SPLIT</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;PRE_SPLIT</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>CS1591,CS1573</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;PRE_SPLIT</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Ionic.Zip.Partial">
      <HintPath>..\..\LIB\DLL\Ionic.Zip.Partial.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zlib">
      <HintPath>..\..\LIB\DLL\Ionic.Zlib.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=*********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\lib\net452\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=*********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.ApplicationInsights.2.23.0\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Connections.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNetCore.Connections.Abstractions.8.0.0\lib\net462\Microsoft.AspNetCore.Connections.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Connections.Client, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNetCore.Http.Connections.Client.8.0.0\lib\net462\Microsoft.AspNetCore.Http.Connections.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Connections.Common, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNetCore.Http.Connections.Common.8.0.0\lib\net462\Microsoft.AspNetCore.Http.Connections.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.SignalR.Client, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNetCore.SignalR.Client.8.0.0\lib\net462\Microsoft.AspNetCore.SignalR.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.SignalR.Client.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNetCore.SignalR.Client.Core.8.0.0\lib\net462\Microsoft.AspNetCore.SignalR.Client.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.SignalR.Common, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNetCore.SignalR.Common.8.0.0\lib\net462\Microsoft.AspNetCore.SignalR.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.SignalR.Protocols.Json, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNetCore.SignalR.Protocols.Json.8.0.0\lib\net462\Microsoft.AspNetCore.SignalR.Protocols.Json.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Bcl.TimeProvider.8.0.0\lib\net462\Microsoft.Bcl.TimeProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Extensions.DependencyInjection.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Extensions.Features.8.0.0\lib\net462\Microsoft.Extensions.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Extensions.Logging.8.0.0\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Extensions.Logging.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Extensions.Options.8.0.0\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Extensions.Primitives.8.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Web.WebView2.Core, Version=1.0.1264.42, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Web.WebView2.1.0.1264.42\lib\net45\Microsoft.Web.WebView2.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.WinForms, Version=1.0.1264.42, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Web.WebView2.1.0.1264.42\lib\net45\Microsoft.Web.WebView2.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.Wpf, Version=1.0.1264.42, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Web.WebView2.1.0.1264.42\lib\net45\Microsoft.Web.WebView2.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="NaturalSort.Extension, Version=3.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\NaturalSort.Extension.3.0.0\lib\net40\NaturalSort.Extension.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Newtonsoft.Json.10.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="ShoppaAB.ThinRpc, Version=3.1.8.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ShoppaAB.ThinRpc.3.1.8\lib\net20\ShoppaAB.ThinRpc.dll</HintPath>
    </Reference>
    <Reference Include="Svg, Version=2.2.1.39233, Culture=neutral, PublicKeyToken=12a0bac221edeae2, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Svg.2.3.0\lib\net35\Svg.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.112.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Data.SQLite.Core.1.0.112.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Diagnostics.DiagnosticSource.8.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.IO.Pipelines.8.0.0\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Printing" />
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Text.Json.8.0.0\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Threading.Channels.8.0.0\lib\net462\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="TECIT.TBarCode, Version=10.1.0.0, Culture=neutral, PublicKeyToken=1b5f4306b234b83d, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\LIB\DLL\TECIT.TBarCode.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\AssemblyInfoInc.cs">
      <Link>Properties\AssemblyInfoInc.cs</Link>
    </Compile>
    <Compile Include="AppRunner.cs" />
    <Compile Include="Controls\PurposeFilter.cs" />
    <Compile Include="Controls\WebPreviewFanOut.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\WebPreviewFanOut.Designer.cs">
      <DependentUpon>WebPreviewFanOut.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DataGridViewControls\DataGridViewColorCell.cs" />
    <Compile Include="Controls\DataGridViewControls\DataGridViewColorColumn.cs" />
    <Compile Include="Controls\DataGridViewControls\DataGridViewElementTreeCell.cs" />
    <Compile Include="Controls\DataGridViewControls\DataGridViewElementTreeColumn.cs" />
    <Compile Include="Controls\GradientFlowLayout.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\GradientFlowLayout.Designer.cs">
      <DependentUpon>GradientFlowLayout.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\IElementInspectorDesignSurface.cs" />
    <Compile Include="Forms\ElementInspector\RowFilterHandler.cs" />
    <Compile Include="Gui Misc\ElementInspectorHandler.cs" />
    <Compile Include="Forms\ElementInspector\ColumnAbstraction.cs" />
    <Compile Include="Forms\ElementInspector\ColumnHandler.cs" />
    <Compile Include="Forms\ElementInspector\ElementInspectorUtils.cs" />
    <Compile Include="Forms\ElementInspector\TagSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ElementInspector\TagSelectionForm.Designer.cs">
      <DependentUpon>TagSelectionForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ElementInspector\PurposeSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ElementInspector\PurposeSelectionForm.Designer.cs">
      <DependentUpon>PurposeSelectionForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ElementInspector\RowFilter.cs" />
    <Compile Include="Forms\ElementInspector\RowFilterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ElementInspector\RowFilterForm.Designer.cs">
      <DependentUpon>RowFilterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ElementInspector\ElementInspectorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ElementInspector\ElementInspectorForm.Designer.cs">
      <DependentUpon>ElementInspectorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ElementInspector\ColumnPickerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ElementInspector\ColumnPickerForm.Designer.cs">
      <DependentUpon>ColumnPickerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ElementInspector\TagSelectionType.cs" />
    <Compile Include="Forms\FormPrintClientStatus.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPrintClientStatus.Designer.cs">
      <DependentUpon>FormPrintClientStatus.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\EditPrintClientName.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EditPrintClientName.Designer.cs">
      <DependentUpon>EditPrintClientName.cs</DependentUpon>
    </Compile>
    <Compile Include="Gui Misc\GuiValueHelper.cs" />
    <Compile Include="Pda\ElementIdentityDecoder.cs" />
    <Compile Include="Pda\ITemplateInformationHandler.cs" />
    <Compile Include="Pda\JsonObjects\Content.cs" />
    <Compile Include="Pda\JsonObjects\PdaPrintedStatus.cs" />
    <Compile Include="Pda\JsonObjects\UserInput.cs" />
    <Compile Include="Pda\PdaSignagePrintingProcessor.cs" />
    <Compile Include="AutoSignagePrintingProcessor.cs" />
    <Compile Include="CancelObjectEventArgs.cs" />
    <Compile Include="Gui Misc\CMessageBox.cs" />
    <Compile Include="ColorPicker\CMYKColor.cs" />
    <Compile Include="ColorPicker\ColorBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ColorPicker\ColorPickerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ColorPicker\ColorPickerForm.Designer.cs">
      <DependentUpon>ColorPickerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ColorPicker\ColorResult.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ColorPicker\ColorResult.Designer.cs">
      <DependentUpon>ColorResult.cs</DependentUpon>
    </Compile>
    <Compile Include="ColorPicker\ColorTable.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ColorPicker\ColorWheel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ColorPicker\EyedropColorPicker.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ColorPicker\Hook.cs" />
    <Compile Include="ColorPicker\HSLColor.cs" />
    <Compile Include="ColorPicker\RGBCMYKInput.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ColorPicker\RGBCMYKInput.Designer.cs">
      <DependentUpon>RGBCMYKInput.cs</DependentUpon>
    </Compile>
    <Compile Include="ColorPicker\Util.cs" />
    <Compile Include="ColorPicker\WinUtil.cs" />
    <Compile Include="ContextItemClickedArgs.cs" />
    <Compile Include="ContextMenuEventTypes.cs" />
    <Compile Include="Gui Misc\ControlHelper.cs" />
    <Compile Include="Controls\AddressesEnMasse.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\AddressesEnMasse.Designer.cs">
      <DependentUpon>AddressesEnMasse.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\AddressInput.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\AddressInput.Designer.cs">
      <DependentUpon>AddressInput.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\BaseSurface.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\ClearableButton.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ClearableButton.Designer.cs">
      <DependentUpon>ClearableButton.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ConsumablesBars.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\ConsumablesBars.Designer.cs">
      <DependentUpon>ConsumablesBars.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ColorSelectionBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ColorSelectionBox.Designer.cs">
      <DependentUpon>ColorSelectionBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ContentView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\ContentView.Designer.cs">
      <DependentUpon>ContentView.cs</DependentUpon>
    </Compile>
    <Compile Include="Pda\JsonObjects\Sign.cs" />
    <Compile Include="Pda\JsonObjects\SignProduct.cs" />
    <Compile Include="Pda\JsonObjects\Text.cs" />
    <Compile Include="Pda\TemplateInformationHandler.cs" />
    <Compile Include="Forms\LunaQRLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LunaQRLogin.Designer.cs">
      <DependentUpon>LunaQRLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PictureBoxWithRoundedCorners.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\PictureBoxWithRoundedCorners.Designer.cs">
      <DependentUpon>PictureBoxWithRoundedCorners.cs</DependentUpon>
    </Compile>
    <Compile Include="Printing\PrinterDriverScanner.cs" />
    <Compile Include="Pda\JsonObjects\PdaPrintRequest.cs" />
    <Compile Include="Pda\PdaMessageParser.cs" />
    <Compile Include="RealTime\IPrintClient.cs" />
    <Compile Include="ShoppaAB.Objects\AppData.cs" />
    <Compile Include="Controls\RevertTemplateFanOut.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\RevertTemplateFanOut.Designer.cs">
      <DependentUpon>RevertTemplateFanOut.cs</DependentUpon>
    </Compile>
    <Compile Include="RealTime\PrintClient.cs" />
    <Compile Include="PictureThumbnailCacheInstance.cs" />
    <Compile Include="Telemetry\AiTelemetry.cs" />
    <Compile Include="Telemetry\AppTelemetry.cs" />
    <Compile Include="Telemetry\ITelemetry.cs" />
    <Compile Include="Telemetry\NoOpTelemetry.cs" />
    <Compile Include="Telemetry\Models\PrintQueueModel.cs" />
    <Compile Include="Telemetry\PrintQueueTelemetryService.cs" />
    <Compile Include="Telemetry\TelemetryPrimitives.cs" />
    <Compile Include="TemplateCacheInstance.cs" />
    <Compile Include="Controls\WebView2ErrorEventArg.cs" />
    <Compile Include="TreeParts\CampaignNodeType.cs" />
    <Compile Include="TreeParts\CampaignRecordingTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\CampaignRecordingTreeModel.cs" />
    <Compile Include="TreeParts\PictureTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\ProductGroupSearchModel.cs" />
    <Compile Include="Controls\ProductSearch\ProductGroupSearchResults.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\ProductGroupSearchResults.Designer.cs">
      <DependentUpon>ProductGroupSearchResults.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ShoppaWebView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ShoppaWebView.Designer.cs">
      <DependentUpon>ShoppaWebView.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ModifierKeysEventArgs.cs" />
    <Compile Include="Controls\ProductSearch\CampaignFilter.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\CampaignFilter.Designer.cs">
      <DependentUpon>CampaignFilter.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\CustomerGroupPicker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\CustomerGroupPicker.Designer.cs">
      <DependentUpon>CustomerGroupPicker.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\CustomerGroupCheckBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\ControlAndOr.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ControlAndOr.Designer.cs">
      <DependentUpon>ControlAndOr.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\CustomerGroupsHoverPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\CustomerGroupsHoverPlate.Designer.cs">
      <DependentUpon>CustomerGroupsHoverPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\CustomToolStripDropDownMenu.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\DataGridViewControls\DataGridViewCustomerCell.cs" />
    <Compile Include="Controls\DataGridViewControls\DataGridViewCustomerColumn.cs" />
    <Compile Include="Controls\DataGridViewControls\DataGridViewCustomerGroupsCell.cs" />
    <Compile Include="Controls\DataGridViewControls\DataGridViewCustomerGroupsColumn.cs" />
    <Compile Include="Controls\DialogTitleBar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DialogTitleBar.Designer.cs">
      <DependentUpon>DialogTitleBar.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DockerPurpose.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerPurpose.Designer.cs">
      <DependentUpon>DockerPurpose.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Docking\DockableContent.cs" />
    <Compile Include="Controls\Docking\DockingPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Docking\DockingPanel.Designer.cs">
      <DependentUpon>DockingPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Docking\IDockableContent.cs" />
    <Compile Include="Controls\ExitDesignButton.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ExitDesignButton.Designer.cs">
      <DependentUpon>ExitDesignButton.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\AsyncLayoutPlateBuilder.cs" />
    <Compile Include="Controls\LeftPanelControls\CancellationToken.cs" />
    <Compile Include="Controls\LeftPanelControls\ICancellationToken.cs" />
    <Compile Include="Controls\LeftPanelControls\OutputFormatChangedEventArgs.cs" />
    <Compile Include="Controls\MediaTypeDropDownWithDelete.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\MediaTypeDropDownWithDelete.Designer.cs">
      <DependentUpon>MediaTypeDropDownWithDelete.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\MediaTypeSelector.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\MediaTypeSelector.Designer.cs">
      <DependentUpon>MediaTypeSelector.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Media\HiddenMediaBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Media\HiddenMediaBox.Designer.cs">
      <DependentUpon>HiddenMediaBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Media\MediaCombobox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Media\MediaCombobox.Designer.cs">
      <DependentUpon>MediaCombobox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Media\MediaBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Media\MediaBox.Designer.cs">
      <DependentUpon>MediaBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Media\MediaControlsUtil.cs" />
    <Compile Include="Controls\Media\MediaFilter.cs" />
    <Compile Include="Controls\Media\MediaSelectedArgs.cs" />
    <Compile Include="Controls\Media\MediaDropdown.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Media\MediaDropdown.Designer.cs">
      <DependentUpon>MediaDropdown.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\MetaBlobShoppaPictureLink.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\MetaBlobShoppaPictureLink.Designer.cs">
      <DependentUpon>MetaBlobShoppaPictureLink.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\NavigationTabs\NavigationTabItemChangeType.cs" />
    <Compile Include="Controls\OkiOutputFormatInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\OkiOutputFormatInfo.Designer.cs">
      <DependentUpon>OkiOutputFormatInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductSearch\CampaignDataGridView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\PurposeShowHide.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PurposeShowHide.Designer.cs">
      <DependentUpon>PurposeShowHide.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PurposeListPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PurposeListPlate.Designer.cs">
      <DependentUpon>PurposeListPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FlowPlaylistPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\FlowPlaylistPlate.Designer.cs">
      <DependentUpon>FlowPlaylistPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\LayoutPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\LayoutPlate.Designer.cs">
      <DependentUpon>LayoutPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\OutputFormatLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\OutputFormatLabel.Designer.cs">
      <DependentUpon>OutputFormatLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\AsyncThumbnailRenderer.cs" />
    <Compile Include="Controls\NativeMethods.cs" />
    <Compile Include="Controls\NavigationTabs\DesignTab.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\NavigationTabs\NavigationTab.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\NavigationTabs\ShoppaPlusTab.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\PictureSearch.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PictureSearch.Designer.cs">
      <DependentUpon>PictureSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductSearch\DockerProductSearch.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\DockerProductSearch.Designer.cs">
      <DependentUpon>DockerProductSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DockerSearchReplace.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerSearchReplace.Designer.cs">
      <DependentUpon>DockerSearchReplace.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ListViewWithForcedSelect.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\PictureSearchResult.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PictureSearchResult.Designer.cs">
      <DependentUpon>PictureSearchResult.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductSearch\CampaignFilterFanOut.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\CampaignFilterFanOut.Designer.cs">
      <DependentUpon>CampaignFilterFanOut.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ExtendedDateTimePicker.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\ProductGroupsFilter.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\ProductGroupsFilter.Designer.cs">
      <DependentUpon>ProductGroupsFilter.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductSearch\OtherFilters.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\OtherFilters.Designer.cs">
      <DependentUpon>OtherFilters.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductSearchResult.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearchResult.Designer.cs">
      <DependentUpon>ProductSearchResult.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductSearch\PosFilter.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\PosFilter.Designer.cs">
      <DependentUpon>PosFilter.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PurposeFieldStrip.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PurposeFieldStrip.Designer.cs">
      <DependentUpon>PurposeFieldStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PurposeParameterBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PurposeParameterBox.Designer.cs">
      <DependentUpon>PurposeParameterBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PurposeTruthHeader.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PurposeTruthHeader.Designer.cs">
      <DependentUpon>PurposeTruthHeader.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TimeSlotTimes.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TimeSlotTimes.Designer.cs">
      <DependentUpon>TimeSlotTimes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AutoSignage\TriggerNameCache.cs" />
    <Compile Include="Forms\TemplateProductSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\TemplateProductSettingsForm.Designer.cs">
      <DependentUpon>TemplateProductSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Theme\ColorExtensions.cs" />
    <Compile Include="Controls\VisibilityDetailsFanout.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\VisibilityDetailsFanout.Designer.cs">
      <DependentUpon>VisibilityDetailsFanout.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\RecordingHoverToolstrip.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\RecordingHoverToolstrip.Designer.cs">
      <DependentUpon>RecordingHoverToolstrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ResettableStateButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\ResettableStateButton.Designer.cs">
      <DependentUpon>ResettableStateButton.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ShapePicker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ShapePicker.Designer.cs">
      <DependentUpon>ShapePicker.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\StoreInfoPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\StoreInfoPlate.Designer.cs">
      <DependentUpon>StoreInfoPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PurposeHoverToolstrip.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PurposeHoverToolstrip.Designer.cs">
      <DependentUpon>PurposeHoverToolstrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\Arrange.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\Arrange.Designer.cs">
      <DependentUpon>Arrange.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\Animate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\Animate.Designer.cs">
      <DependentUpon>Animate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\General.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\General.Designer.cs">
      <DependentUpon>General.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\Precision.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\Precision.Designer.cs">
      <DependentUpon>Precision.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\RibbonArrangeUiState.cs" />
    <Compile Include="Controls\TemplateRibbonPanel\RibbonAnimateUiState.cs" />
    <Compile Include="Controls\TemplateRibbonPanel\RibbonGeneralUiState.cs" />
    <Compile Include="Controls\TemplateRibbonPanel\RibbonPrecisionUiState.cs" />
    <Compile Include="Controls\TemplateRibbonPanel\RibbonTagsUiState.cs" />
    <Compile Include="Controls\TemplateRibbonPanel\RibbonUiState.cs" />
    <Compile Include="Controls\TemplateRibbonPanel\RibbonPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\RibbonPanel.Designer.cs">
      <DependentUpon>RibbonPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\TemplateLayoutControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\LeftPanelControls\TemplateLayoutControl.designer.cs">
      <DependentUpon>TemplateLayoutControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\DesignModeToolBar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\UndoUiState.cs" />
    <Compile Include="Controls\TemplateRibbonPanel\Tags.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateRibbonPanel\Tags.Designer.cs">
      <DependentUpon>Tags.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateSearch.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateSearch.Designer.cs">
      <DependentUpon>TemplateSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TitleBar\MenuItemClickInfo.cs" />
    <Compile Include="Controls\TitleBar\TitleBarStrip.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TitleBar\TitleBarStrip.Designer.cs">
      <DependentUpon>TitleBarStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\WebBrowserControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\WebBrowserControl.Designer.cs">
      <DependentUpon>WebBrowserControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\WebMessageReceivedEventArg.cs" />
    <Compile Include="Controls\WebNavigationCompletedEventArg.cs" />
    <Compile Include="Controls\WebNavigationStartingEventArg.cs" />
    <Compile Include="CustomerGroupsRenderer.cs" />
    <Compile Include="DockerInfoHelper.cs" />
    <Compile Include="DpiHelper.cs" />
    <Compile Include="Esl\EslRenderer.cs" />
    <Compile Include="Esl\EslRenderJobProcessor.cs" />
    <Compile Include="Esl\RenderJob.cs" />
    <Compile Include="Esl\RenderJobQueue.cs" />
    <Compile Include="InstagramContentSpecification.cs" />
    <Compile Include="TreeParts\TemplateTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UploadResult.cs" />
    <Compile Include="Forms\AutoSignage\AutoSignageLegacyFormatPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\AutoSignage\AutoSignageLegacyFormatPlate.Designer.cs">
      <DependentUpon>AutoSignageLegacyFormatPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AutoSignage\AutoSignageFormatPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\AutoSignage\AutoSignageFormatPlate.Designer.cs">
      <DependentUpon>AutoSignageFormatPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AutoSignage\AutoSignageLayoutPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\AutoSignage\AutoSignageLayoutPlate.Designer.cs">
      <DependentUpon>AutoSignageLayoutPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AutoSignage\BaseAutoSignageFormatPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\AutoSignage\DisplayGroupAddPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\AutoSignage\DisplayGroupAddPanel.Designer.cs">
      <DependentUpon>DisplayGroupAddPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AutoSignage\LegacyDisplayGroupAddPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\AutoSignage\LegacyDisplayGroupAddPanel.Designer.cs">
      <DependentUpon>LegacyDisplayGroupAddPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AutoSignage\Objects\DisplayGroupAddedEventArgs.cs" />
    <Compile Include="Forms\AutoSignage\Objects\DisplayGroupAddEventArgs.cs" />
    <Compile Include="Forms\AutoSignage\Objects\DisplayGroupLookUp.cs" />
    <Compile Include="Forms\AutoSignage\Objects\DisplayGroupRowEventArgs.cs" />
    <Compile Include="Forms\AutoSignage\Objects\DisplayGroupRowViewInfo.cs" />
    <Compile Include="Forms\AutoSignage\Objects\FormatEditedEventArgs.cs" />
    <Compile Include="Forms\AutoSignage\Objects\GroupNameChangedEventArgs.cs" />
    <Compile Include="Forms\AutoSignage\Objects\LayoutChangedEventArgs.cs" />
    <Compile Include="Forms\AutoSignage\Objects\LegacyDisplayGroupEditEventArgs.cs" />
    <Compile Include="Forms\AutoSignage\Objects\LegacyDisplayGroupRowEventArgs.cs" />
    <Compile Include="Forms\FormFilterStoreGroups.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormFilterStoreGroups.Designer.cs">
      <DependentUpon>FormFilterStoreGroups.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormTemplateDesignUiState.cs" />
    <Compile Include="Forms\FormVideoImport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormVideoImport.Designer.cs">
      <DependentUpon>FormVideoImport.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormVideoPlayer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormVideoPlayer.Designer.cs">
      <DependentUpon>FormVideoPlayer.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\GraphicalProfileInspector.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\GraphicalProfileInspector.Designer.cs">
      <DependentUpon>GraphicalProfileInspector.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\LoadingScreenCancelledException.cs" />
    <Compile Include="Forms\SendFlowWizard\FlowAction.cs" />
    <Compile Include="Forms\SendFlowWizard\IStep.cs" />
    <Compile Include="Forms\SendFlowWizard\PlaylistSendResult.cs" />
    <Compile Include="Forms\SendFlowWizard\SettingsPersistator.cs" />
    <Compile Include="Forms\SendFlowWizard\StepsEnum.cs" />
    <Compile Include="Forms\SendFlowWizard\ChooseOutputType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\ChooseOutputType.Designer.cs">
      <DependentUpon>ChooseOutputType.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SocialShareContainer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SocialShareContainer.Designer.cs">
      <DependentUpon>SocialShareContainer.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\StoreListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StoreListForm.Designer.cs">
      <DependentUpon>StoreListForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\VisibilityForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\VisibilityForm.Designer.cs">
      <DependentUpon>VisibilityForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SetShoppaPictureOnMetaValueForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SetShoppaPictureOnMetaValueForm.Designer.cs">
      <DependentUpon>SetShoppaPictureOnMetaValueForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\WelcomeToShoppaGoForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\WelcomeToShoppaGoForm.Designer.cs">
      <DependentUpon>WelcomeToShoppaGoForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ConfigureEslForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ConfigureEslForm.Designer.cs">
      <DependentUpon>ConfigureEslForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AutoSignage\FormAutoSignage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AutoSignage\FormAutoSignage.Designer.cs">
      <DependentUpon>FormAutoSignage.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormConsumablesBuyNowInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormConsumablesBuyNowInfo.Designer.cs">
      <DependentUpon>FormConsumablesBuyNowInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormConsumablesPopup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormConsumablesPopup.Designer.cs">
      <DependentUpon>FormConsumablesPopup.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\VideoFileSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\VideoFileSettingsForm.Designer.cs">
      <DependentUpon>VideoFileSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormShapeFillModeChooser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormShapeFillModeChooser.Designer.cs">
      <DependentUpon>FormShapeFillModeChooser.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BleedDoesNotFitForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BleedDoesNotFitForm.Designer.cs">
      <DependentUpon>BleedDoesNotFitForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ImportTemplateConflictForm .cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ImportTemplateConflictForm .Designer.cs">
      <DependentUpon>ImportTemplateConflictForm .cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileImporter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileImporter.Designer.cs">
      <DependentUpon>FileImporter.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormEslSendFailed.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormEslSendFailed.Designer.cs">
      <DependentUpon>FormEslSendFailed.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAddProductGroup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAddProductGroup.Designer.cs">
      <DependentUpon>FormAddProductGroup.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPromptMediaTypeUsage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPromptMediaTypeUsage.Designer.cs">
      <DependentUpon>FormPromptMediaTypeUsage.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPromptRecomendedMediaTypeUsage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPromptRecomendedMediaTypeUsage.Designer.cs">
      <DependentUpon>FormPromptRecomendedMediaTypeUsage.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPurposeCreator.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPurposeCreator.Designer.cs">
      <DependentUpon>FormPurposeCreator.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormLoadingScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormLoadingScreen.Designer.cs">
      <DependentUpon>FormLoadingScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ImageAdjustmentsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ImageAdjustmentsForm.Designer.cs">
      <DependentUpon>ImageAdjustmentsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InformationChangedByUserEventArgs.cs" />
    <Compile Include="Forms\MainForm.CampaignRecording.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.OutputQueue.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Trees.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ManageDestinations.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ManageDestinations.Designer.cs">
      <DependentUpon>ManageDestinations.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Edit_CustomerGroups.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Edit_CustomerGroups.Designer.cs">
      <DependentUpon>Edit_CustomerGroups.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ExportTemplatesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ExportTemplatesForm.Designer.cs">
      <DependentUpon>ExportTemplatesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAutomaticPrintingNotification.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAutomaticPrintingNotification.Designer.cs">
      <DependentUpon>FormAutomaticPrintingNotification.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormCampaignEditWarning.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormCampaignEditWarning.Designer.cs">
      <DependentUpon>FormCampaignEditWarning.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormExportResolution.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormExportResolution.Designer.cs">
      <DependentUpon>FormExportResolution.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_Director.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_Director.Designer.cs">
      <DependentUpon>Add_Edit_Director.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormMultiCopieQueue.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormMultiCopieQueue.Designer.cs">
      <DependentUpon>FormMultiCopieQueue.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FlowContentView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\FlowContentView.Designer.cs">
      <DependentUpon>FlowContentView.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FlowScenePlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\FlowScenePlate.designer.cs">
      <DependentUpon>FlowScenePlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FlowChannelsView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\FlowChannelsView.Designer.cs">
      <DependentUpon>FlowChannelsView.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\InfoPanelWithSpaceBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\InfoPanelWithTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\DockerInfoExtensions.cs" />
    <Compile Include="Controls\DockerInfoProduction.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerInfoProduction.Designer.cs">
      <DependentUpon>DockerInfoProduction.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\IDockerInfo.cs" />
    <Compile Include="Controls\InfoPanelWithTextElement.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\InfoPanelBase.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetaBlobAddressesField.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\MetaBlobAddressesField.Designer.cs">
      <DependentUpon>MetaBlobAddressesField.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TemplateSearchResult.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateSearchResult.Designer.cs">
      <DependentUpon>TemplateSearchResult.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DockerPrintshopInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerPrintshopInfo.Designer.cs">
      <DependentUpon>DockerPrintshopInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\IMetaBlobField.cs" />
    <Compile Include="Controls\MetaBlobCell.cs" />
    <Compile Include="Controls\MetaBlobColumn.cs" />
    <Compile Include="Controls\LicenseCell.cs" />
    <Compile Include="Controls\LicenseColumn.cs" />
    <Compile Include="Controls\MetaBlobBrowseableField.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\MetaBlobBrowseableField.Designer.cs">
      <DependentUpon>MetaBlobBrowseableField.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\MetaBlobTextField.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\MetaBlobTextField.Designer.cs">
      <DependentUpon>MetaBlobTextField.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TagHoverToolstrip.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TagHoverToolstrip.Designer.cs">
      <DependentUpon>TagHoverToolstrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\WelcomeScreen.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\WelcomeScreen.Designer.cs">
      <DependentUpon>WelcomeScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ChooseDSDestination.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ChooseDSDestination.Designer.cs">
      <DependentUpon>ChooseDSDestination.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ColorBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ColorBox.Designer.cs">
      <DependentUpon>ColorBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ColorStrip.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ColorStrip.Designer.cs">
      <DependentUpon>ColorStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ComplementProductsPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ComplementProductsPlate.Designer.cs">
      <DependentUpon>ComplementProductsPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\AddDeleteButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\AddressListDataGridView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ObjectDataGridView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ObjectDataGridView.Designer.cs">
      <DependentUpon>ObjectDataGridView.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DeleteButton.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DeleteButton.Designer.cs">
      <DependentUpon>DeleteButton.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ExtrasConector.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ExtrasConector.Designer.cs">
      <DependentUpon>ExtrasConector.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ExtrasFormatSelector.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ExtrasFormatSelector.Designer.cs">
      <DependentUpon>ExtrasFormatSelector.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\OutputBar2.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\OutputBar2.Designer.cs">
      <DependentUpon>OutputBar2.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DsContentBar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DsContentBar.designer.cs">
      <DependentUpon>DsContentBar.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DockerDS.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerDS.Designer.cs">
      <DependentUpon>DockerDS.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DateTrackBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\Docker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Docker.Designer.cs">
      <DependentUpon>Docker.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DockerInfoDesign.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerInfoDesign.Designer.cs">
      <DependentUpon>DockerInfoDesign.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DockerStack.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerStack.Designer.cs">
      <DependentUpon>DockerStack.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DockerTags.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DockerTags.Designer.cs">
      <DependentUpon>DockerTags.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FontPicker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\FontPicker.Designer.cs">
      <DependentUpon>FontPicker.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FontPreviewRender.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\FontPreviewRender.Designer.cs">
      <DependentUpon>FontPreviewRender.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FontStylePreview.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\FontStylePreview.Designer.cs">
      <DependentUpon>FontStylePreview.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\HeadersTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\ProductSearch\CampaignsSorter.cs" />
    <Compile Include="Controls\OutputBarInnerStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\OutputBarInnerStrip.Designer.cs">
      <DependentUpon>OutputBarInnerStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PrintShopInformation.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PrintShopInformation.Designer.cs">
      <DependentUpon>PrintShopInformation.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PrintShopOrderItem.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\PrintShopOrderItem.Designer.cs">
      <DependentUpon>PrintShopOrderItem.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductDropPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductDropPlate.Designer.cs">
      <DependentUpon>ProductDropPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\SelectedExtras.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\SelectedExtras.Designer.cs">
      <DependentUpon>SelectedExtras.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\SignProductionSurface.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\SignProductionSurface.Designer.cs">
      <DependentUpon>SignProductionSurface.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\LabelPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\OutputFormatInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\OutputFormatInfo.Designer.cs">
      <DependentUpon>OutputFormatInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ProductPlate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductPlate.Designer.cs">
      <DependentUpon>ProductPlate.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Rulers.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TemplateDesignSurface.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\TemplateDesignSurface.Designer.cs">
      <DependentUpon>TemplateDesignSurface.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ToolStripNumericUpDown.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\UndoList.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\UpDownArrow.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\UpDownArrow.Designer.cs">
      <DependentUpon>UpDownArrow.cs</DependentUpon>
    </Compile>
    <Compile Include="Gui Misc\CursorHelper.cs" />
    <Compile Include="FileCreationWatcher.cs" />
    <Compile Include="Forms\Add_Edit_Animation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_Animation.Designer.cs">
      <DependentUpon>Add_Edit_Animation.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_FlowChannel.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_FlowChannel.Designer.cs">
      <DependentUpon>Add_Edit_FlowChannel.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_Transition.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_Transition.Designer.cs">
      <DependentUpon>Add_Edit_Transition.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FlowPlannerContainer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FlowPlannerContainer.Designer.cs">
      <DependentUpon>FlowPlannerContainer.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FlowPlayerWithoutChannelDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FlowPlayerWithoutChannelDialog.Designer.cs">
      <DependentUpon>FlowPlayerWithoutChannelDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormFlowContainerManagement.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormFlowContainerManagement.Designer.cs">
      <DependentUpon>FormFlowContainerManagement.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormFlowTransitionDefaults.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormFlowTransitionDefaults.Designer.cs">
      <DependentUpon>FormFlowTransitionDefaults.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ManageCustomerGroups.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ManageCustomerGroups.Designer.cs">
      <DependentUpon>ManageCustomerGroups.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\GlowBorderBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\GlowBorderBase.Designer.cs">
      <DependentUpon>GlowBorderBase.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\GlowBorderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\GlowBorderForm.Designer.cs">
      <DependentUpon>GlowBorderForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\GlowBorderFixedSizeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\GlowBorderFixedSizeForm.Designer.cs">
      <DependentUpon>GlowBorderFixedSizeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\ModernForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\ModernForm.Designer.cs">
      <DependentUpon>ModernForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ModernFormComponents\NativeMethods.cs" />
    <Compile Include="Forms\ModernFormComponents\NonClientMouseEventArgs.cs" />
    <Compile Include="Forms\ModernFormComponents\NonClientPaintEventArgs.cs" />
    <Compile Include="Forms\MovePlayerToChannel.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MovePlayerToChannel.Designer.cs">
      <DependentUpon>MovePlayerToChannel.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PdfDPiChoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PdfDPiChoice.Designer.cs">
      <DependentUpon>PdfDPiChoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PictureDeleteDialogForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PictureDeleteDialogForm.Designer.cs">
      <DependentUpon>PictureDeleteDialogForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PictureNameClashDialogForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PictureNameClashDialogForm.Designer.cs">
      <DependentUpon>PictureNameClashDialogForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormVerboseHelp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormVerboseHelp.Designer.cs">
      <DependentUpon>FormVerboseHelp.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormFriendlyError.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormFriendlyError.Designer.cs">
      <DependentUpon>FormFriendlyError.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ExportTemplateForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ExportTemplateForm.Designer.cs">
      <DependentUpon>ExportTemplateForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormFillCustomerMeta.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormFillCustomerMeta.Designer.cs">
      <DependentUpon>FormFillCustomerMeta.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAddressLists.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAddressLists.Designer.cs">
      <DependentUpon>FormAddressLists.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_AdvancedPrinter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_AdvancedPrinter.designer.cs">
      <DependentUpon>Add_Edit_AdvancedPrinter.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_Label.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_OutputFormat.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_OutputFormat.Designer.cs">
      <DependentUpon>Add_Edit_OutputFormat.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_Page.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ChangePasswordForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ChangePasswordForm.designer.cs">
      <DependentUpon>ChangePasswordForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ChoseFinalDestination2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ChoseFinalDestination2.Designer.cs">
      <DependentUpon>ChoseFinalDestination2.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_FlowPlayer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_FlowPlayer.Designer.cs">
      <DependentUpon>Add_Edit_FlowPlayer.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAddressEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAddressEdit.Designer.cs">
      <DependentUpon>FormAddressEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAddressEditEnMasse.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAddressEditEnMasse.Designer.cs">
      <DependentUpon>FormAddressEditEnMasse.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Add_Edit_Address.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Add_Edit_Address.Designer.cs">
      <DependentUpon>Add_Edit_Address.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAnimationList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAnimationList.Designer.cs">
      <DependentUpon>FormAnimationList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CustomerSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomerSelectionForm.Designer.cs">
      <DependentUpon>CustomerSelectionForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPayment.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPayment.Designer.cs">
      <DependentUpon>FormPayment.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPrintshopOrderConfirmation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPrintshopOrderConfirmation.Designer.cs">
      <DependentUpon>FormPrintshopOrderConfirmation.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormTemplateFeedback.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormTemplateFeedback.Designer.cs">
      <DependentUpon>FormTemplateFeedback.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormSignProduction.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormSignProduction.Designer.cs">
      <DependentUpon>FormSignProduction.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormTemplateBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormTemplateDesign.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormTemplateDesign.Designer.cs">
      <DependentUpon>FormTemplateDesign.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormTransitionList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormTransitionList.Designer.cs">
      <DependentUpon>FormTransitionList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\LoginForm_ResetPassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm_ResetPassword.Designer.cs">
      <DependentUpon>LoginForm_ResetPassword.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\NameInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\NameInputForm.Designer.cs">
      <DependentUpon>NameInputForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPrintshopOrder.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPrintshopOrder.Designer.cs">
      <DependentUpon>FormPrintshopOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PictureConvertDialogForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PictureConvertDialogForm.Designer.cs">
      <DependentUpon>PictureConvertDialogForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PleaseWaitSplash.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PleaseWaitSplash.Designer.cs">
      <DependentUpon>PleaseWaitSplash.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPreviewPlaySettings.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormPreviewPlaySettings.Designer.cs">
      <DependentUpon>FormPreviewPlaySettings.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PrintCampaignDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PrintCampaignDialog.Designer.cs">
      <DependentUpon>PrintCampaignDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PrinterWizard\FormPrinterSetup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PrinterWizard\FormPrinterSetup.Designer.cs">
      <DependentUpon>FormPrinterSetup.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\Confirm.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\Confirm.Designer.cs">
      <DependentUpon>Confirm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\SelectPaperSize.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\SelectPaperSize.Designer.cs">
      <DependentUpon>SelectPaperSize.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\SelectPaperSource.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\SelectPaperSource.Designer.cs">
      <DependentUpon>SelectPaperSource.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\SelectPrinter.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\PrinterWizard\Steps\SelectPrinter.Designer.cs">
      <DependentUpon>SelectPrinter.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PrintshopTeaser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PrintshopTeaser.Designer.cs">
      <DependentUpon>PrintshopTeaser.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PublishCampaignDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PublishCampaignDialog.Designer.cs">
      <DependentUpon>PublishCampaignDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\RecoverForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\RecoverForm.Designer.cs">
      <DependentUpon>RecoverForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\RecoverPrintshopOrders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\RecoverPrintshopOrders.Designer.cs">
      <DependentUpon>RecoverPrintshopOrders.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SelectExternalIdAndDepartmentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SelectExternalIdAndDepartmentForm.Designer.cs">
      <DependentUpon>SelectExternalIdAndDepartmentForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SelectPdfPages.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SelectPdfPages.Designer.cs">
      <DependentUpon>SelectPdfPages.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SendFlowToFileDoneForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SendFlowToFileDoneForm.Designer.cs">
      <DependentUpon>SendFlowToFileDoneForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\FormSendFlow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\FormSendFlow.Designer.cs">
      <DependentUpon>FormSendFlow.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\ActionForNew.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\ActionForNew.Designer.cs">
      <DependentUpon>ActionForNew.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\ActionForExisting.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\ActionForExisting.Designer.cs">
      <DependentUpon>ActionForExisting.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\ChoseDestination.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\ChoseDestination.Designer.cs">
      <DependentUpon>ChoseDestination.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\GiveName.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\SendFlowWizard\Steps\GiveName.Designer.cs">
      <DependentUpon>GiveName.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\TemplateImport\ImportOverwriteResult.cs" />
    <Compile Include="Forms\TemplateImport\TemplateDataImporter.cs" />
    <Compile Include="Forms\TemplateImport\TemplateOverwriteEventArgs.cs" />
    <Compile Include="Forms\WebBrowserFanout.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\WebBrowserFanout.Designer.cs">
      <DependentUpon>WebBrowserFanout.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\StartPageBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\StartPageBox.Designer.cs">
      <DependentUpon>StartPageBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\TabBorder.cs" />
    <Compile Include="GraphicsPathHelper.cs" />
    <Compile Include="GridStripRendererp1.cs" />
    <Compile Include="Gui Misc\GuiControllerEventArgs.cs" />
    <Compile Include="Gui Misc\GuiSortOrderModeEnumeration.cs" />
    <Compile Include="HotfolderWatcher.cs" />
    <Compile Include="IdleDetector.cs" />
    <Compile Include="Controls\ContentViewAdaptors.cs" />
    <Compile Include="ImageHelper.cs" />
    <Compile Include="InputBox\InputBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="InputBox\InputBox.designer.cs">
      <DependentUpon>InputBox.cs</DependentUpon>
    </Compile>
    <Compile Include="InputBox\InputFocusDirection.cs" />
    <Compile Include="InputBox\InputFocusEventArgs.cs" />
    <Compile Include="InputBox\InputTag.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="InputBox\InputTagHeader.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="InputBox\InputTagLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="InputBox\InputTagText.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="InputBox\InputTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="InputBox\ProductSearchEventArgs.cs" />
    <Compile Include="InputBox\QueryAutoCompleteCustomSourceEventArgs.cs" />
    <Compile Include="InputBox\TagChangedEventArgs.cs" />
    <Compile Include="InvalidContentErrorException.cs" />
    <Compile Include="InvalidContentWarningException.cs" />
    <Compile Include="LocalDatabaseValidator.cs" />
    <Compile Include="MainWmProcessor.cs" />
    <Compile Include="Gui Misc\MessageBoxes.cs" />
    <Compile Include="Messenger.cs" />
    <Compile Include="MLS\PseudoTranslator.cs" />
    <Compile Include="Gui Misc\OutputFormaButtonHelper.cs" />
    <Compile Include="Controls\NavigationTabs\NavigationTabPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\NavigationTabs\NavigationTabPanel.Designer.cs">
      <DependentUpon>NavigationTabPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="ProductCacheFactory.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="SocialMediaHelper.cs" />
    <Compile Include="TemplateConverter.cs" />
    <Compile Include="Theme\ColorUtil.cs" />
    <Compile Include="Theme\DesignMode.cs" />
    <Compile Include="Theme\ImmutableTheme.cs" />
    <Compile Include="Theme\ITheme.cs" />
    <Compile Include="Theme\Themes.cs" />
    <Compile Include="Theme\ThemeProperties.cs" />
    <Compile Include="Theme\KellyContrastColors.cs" />
    <Compile Include="Theme\ThemeUtil.cs" />
    <Compile Include="Theme\Profiles\Colors\ModernColorProfile.cs" />
    <Compile Include="Theme\Profiles\Colors\DebugColorProfile.cs" />
    <Compile Include="Theme\Profiles\Fonts\FontProfile.cs" />
    <Compile Include="Theme\Profiles\IColorProfile.cs" />
    <Compile Include="Theme\Profiles\IFontProfile.cs" />
    <Compile Include="TreeParts\TemplatePasteHelper.cs" />
    <Compile Include="TreeParts\TreeSelection.cs" />
    <Compile Include="UriScheme\PlaylistPlay\PlaylistUrlRunner.cs" />
    <Compile Include="PreviewModeFilter.cs" />
    <Compile Include="Printing\PrintUtil.cs" />
    <Compile Include="Controls\NavigationTabs\NavigationTabItemInfo.cs" />
    <Compile Include="PreviewPlayerThreadedRenderer.cs" />
    <Compile Include="QueryTemplateInformationEventArgs.cs" />
    <Compile Include="RecoverOutputQueueEventArgs.cs" />
    <Compile Include="SignOrderProcessor.cs" />
    <Compile Include="Snapping\HotSpotCenter.cs" />
    <Compile Include="Snapping\HotSpotLine.cs" />
    <Compile Include="Snapping\HotSpotArea.cs" />
    <Compile Include="Snapping\HotSpotBase.cs" />
    <Compile Include="Snapping\SnapOrientation.cs" />
    <Compile Include="StoreInfoDateComparer.cs" />
    <Compile Include="TreeParts\CampaignNode.cs" />
    <Compile Include="TreeParts\DisabledAsCheckedNodeCheckbox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Snapping\SnapController.cs" />
    <Compile Include="TreeParts\NodeStateIconPrint.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\ProductGroupNodeIcon.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\PictureSearchTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\PictureSearchTree.Designer.cs">
      <DependentUpon>PictureSearchTree.cs</DependentUpon>
    </Compile>
    <Compile Include="TreeParts\PictureSearchTreeModel.cs" />
    <Compile Include="TreeParts\SearchTreeModel.cs" />
    <Compile Include="TreeParts\TemplateSearchTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\NodeStateIconShoppa.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ObjectDataGridViewEventArgs.cs" />
    <Compile Include="ListViewExtensions.cs" />
    <Compile Include="MainForm.DS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MLS\TranslatableTexts.cs" />
    <Compile Include="Perspective\FreeTransform.cs" />
    <Compile Include="Perspective\ImageData.cs" />
    <Compile Include="Perspective\Vector.cs" />
    <Compile Include="Printing\Print.cs" />
    <Compile Include="Printing\PrintshopDocuments.cs" />
    <Compile Include="Printing\PrintshopPrinting.cs" />
    <Compile Include="Printing\RichTextBoxPrintCtrl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Printing\WMFPrintController.cs" />
    <Compile Include="SingleInstanceStartup.cs" />
    <Compile Include="TreeParts\ModelBase.cs" />
    <Compile Include="TreeParts\NodeBase.cs" />
    <Compile Include="TreeParts\PrintModel.cs" />
    <Compile Include="TreeParts\ModelBaseShoppa.cs" />
    <Compile Include="TreeParts\PictureTreeModel.cs" />
    <Compile Include="TreeParts\ITreeModeShoppa.cs" />
    <Compile Include="TreeParts\PrintNode.cs" />
    <Compile Include="TreeParts\PrintTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\ShoppaTreeNode.cs" />
    <Compile Include="TreeParts\TemplateTreeModel.cs" />
    <Compile Include="TreeParts\CustomerModel.cs" />
    <Compile Include="TreeParts\CustomerNode.cs" />
    <Compile Include="TreeParts\CustomerTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\ShoppaTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\ProductGroupModel.cs" />
    <Compile Include="TreeParts\ProductGroupNode.cs" />
    <Compile Include="TreeParts\ProductGroupTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TreeParts\TreeGroupInformation.cs" />
    <Compile Include="TreeParts\TreeUtil.cs" />
    <Compile Include="UriScheme\PlaylistPlay\PlaylistPlayCommand.cs" />
    <Compile Include="UriScheme\PlaylistPlay\PreparingPlaylistSplash.cs" />
    <Compile Include="UriScheme\UriSchemeManager.cs" />
    <Compile Include="Gui Misc\WindowGraphics.cs" />
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.Controller.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MLS\MLS.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="ColorPicker\ColorPickerForm.resx">
      <DependentUpon>ColorPickerForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ColorPicker\RGBCMYKInput.resx">
      <DependentUpon>RGBCMYKInput.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\AddDeleteButton.resx">
      <DependentUpon>AddDeleteButton.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\AddressesEnMasse.resx">
      <DependentUpon>AddressesEnMasse.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\AddressInput.resx">
      <DependentUpon>AddressInput.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\AddressListDataGridView.resx">
      <DependentUpon>AddressListDataGridView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ColorSelectionBox.resx">
      <DependentUpon>ColorSelectionBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ContentView.resx">
      <DependentUpon>ContentView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearch\ProductGroupSearchResults.resx">
      <DependentUpon>ProductGroupSearchResults.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\RevertTemplateFanOut.resx">
      <DependentUpon>RevertTemplateFanOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ShoppaWebView.resx">
      <DependentUpon>ShoppaWebView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearch\CampaignFilter.resx">
      <DependentUpon>CampaignFilter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\CustomerGroupPicker.resx">
      <DependentUpon>CustomerGroupPicker.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ControlAndOr.resx">
      <DependentUpon>ControlAndOr.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\CustomerGroupsHoverPlate.resx">
      <DependentUpon>CustomerGroupsHoverPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DialogTitleBar.resx">
      <DependentUpon>DialogTitleBar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerPurpose.resx">
      <DependentUpon>DockerPurpose.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Docking\DockingPanel.resx">
      <DependentUpon>DockingPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ExitDesignButton.resx">
      <DependentUpon>ExitDesignButton.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\MediaTypeDropDownWithDelete.resx">
      <DependentUpon>MediaTypeDropDownWithDelete.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\MediaTypeSelector.resx">
      <DependentUpon>MediaTypeSelector.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Media\HiddenMediaBox.resx">
      <DependentUpon>HiddenMediaBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Media\MediaCombobox.resx">
      <DependentUpon>MediaCombobox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Media\MediaBox.resx">
      <DependentUpon>MediaBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Media\MediaDropdown.resx">
      <DependentUpon>MediaDropdown.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\MetaBlobShoppaPictureLink.resx">
      <DependentUpon>MetaBlobShoppaPictureLink.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\OkiOutputFormatInfo.resx">
      <DependentUpon>OkiOutputFormatInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PurposeShowHide.resx">
      <DependentUpon>PurposeShowHide.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PurposeListPlate.resx">
      <DependentUpon>PurposeListPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\FlowPlaylistPlate.resx">
      <DependentUpon>FlowPlaylistPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\LeftPanelControls\LayoutPlate.resx">
      <DependentUpon>LayoutPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PictureSearch.resx">
      <DependentUpon>PictureSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearch\DockerProductSearch.resx">
      <DependentUpon>DockerProductSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerSearchReplace.resx">
      <DependentUpon>DockerSearchReplace.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\FlowContentView.resx">
      <DependentUpon>FlowContentView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\FlowScenePlate.resx">
      <DependentUpon>FlowScenePlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerInfoProduction.resx">
      <DependentUpon>DockerInfoProduction.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\FlowChannelsView.resx">
      <DependentUpon>FlowChannelsView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\MetaBlobAddressesField.resx">
      <DependentUpon>MetaBlobAddressesField.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PictureSearchResult.resx">
      <DependentUpon>PictureSearchResult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearch\CampaignFilterFanOut.resx">
      <DependentUpon>CampaignFilterFanOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearch\ProductGroupsFilter.resx">
      <DependentUpon>ProductGroupsFilter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearch\OtherFilters.resx">
      <DependentUpon>OtherFilters.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearchResult.resx">
      <DependentUpon>ProductSearchResult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductSearch\PosFilter.resx">
      <DependentUpon>PosFilter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PurposeFieldStrip.resx">
      <DependentUpon>PurposeFieldStrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PurposeParameterBox.resx">
      <DependentUpon>PurposeParameterBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PurposeTruthHeader.resx">
      <DependentUpon>PurposeTruthHeader.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TimeSlotTimes.resx">
      <DependentUpon>TimeSlotTimes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\VisibilityDetailsFanout.resx">
      <DependentUpon>VisibilityDetailsFanout.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\RecordingHoverToolstrip.resx">
      <DependentUpon>RecordingHoverToolstrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ShapePicker.resx">
      <DependentUpon>ShapePicker.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\StoreInfoPlate.resx">
      <DependentUpon>StoreInfoPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PurposeHoverToolstrip.resx">
      <DependentUpon>PurposeHoverToolstrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateRibbonPanel\Animate.resx">
      <DependentUpon>Animate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateRibbonPanel\Arrange.resx">
      <DependentUpon>Arrange.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateRibbonPanel\General.resx">
      <DependentUpon>General.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateRibbonPanel\Precision.resx">
      <DependentUpon>Precision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateRibbonPanel\RibbonPanel.resx">
      <DependentUpon>RibbonPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\LeftPanelControls\TemplateLayoutControl.resx">
      <DependentUpon>TemplateLayoutControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateRibbonPanel\Tags.resx">
      <DependentUpon>Tags.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateSearch.resx">
      <DependentUpon>TemplateSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateSearchResult.resx">
      <DependentUpon>TemplateSearchResult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TitleBar\TitleBarStrip.resx">
      <DependentUpon>TitleBarStrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerPrintshopInfo.resx">
      <DependentUpon>DockerPrintshopInfo.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ObjectDataGridView.resx">
      <DependentUpon>ObjectDataGridView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\MetaBlobBrowseableField.resx">
      <DependentUpon>MetaBlobBrowseableField.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\MetaBlobTextField.resx">
      <DependentUpon>MetaBlobTextField.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TagHoverToolstrip.resx">
      <DependentUpon>TagHoverToolstrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\WebBrowserControl.resx">
      <DependentUpon>WebBrowserControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\WebPreviewFanOut.resx">
      <DependentUpon>WebPreviewFanOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\WelcomeScreen.resx">
      <DependentUpon>WelcomeScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ChooseDSDestination.resx">
      <DependentUpon>ChooseDSDestination.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ExtrasConector.resx">
      <DependentUpon>ExtrasConector.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ExtrasFormatSelector.resx">
      <DependentUpon>ExtrasFormatSelector.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\OutputBar2.resx">
      <DependentUpon>OutputBar2.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DsContentBar.resx">
      <DependentUpon>DsContentBar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerDS.resx">
      <DependentUpon>DockerDS.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\OutputBarInnerStrip.resx">
      <DependentUpon>OutputBarInnerStrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PrintShopInformation.resx">
      <DependentUpon>PrintShopInformation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PrintShopOrderItem.resx">
      <DependentUpon>PrintShopOrderItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\SelectedExtras.resx">
      <DependentUpon>SelectedExtras.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_Animation.resx">
      <DependentUpon>Add_Edit_Animation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_FlowChannel.resx">
      <DependentUpon>Add_Edit_FlowChannel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_FlowPlayer.resx">
      <DependentUpon>Add_Edit_FlowPlayer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_Transition.resx">
      <DependentUpon>Add_Edit_Transition.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AutoSignage\AutoSignageLegacyFormatPlate.resx">
      <DependentUpon>AutoSignageLegacyFormatPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AutoSignage\AutoSignageFormatPlate.resx">
      <DependentUpon>AutoSignageFormatPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AutoSignage\AutoSignageLayoutPlate.resx">
      <DependentUpon>AutoSignageLayoutPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AutoSignage\DisplayGroupAddPanel.resx">
      <DependentUpon>DisplayGroupAddPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AutoSignage\LegacyDisplayGroupAddPanel.resx">
      <DependentUpon>LegacyDisplayGroupAddPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ElementInspector\TagSelectionForm.resx">
      <DependentUpon>TagSelectionForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ElementInspector\PurposeSelectionForm.resx">
      <DependentUpon>PurposeSelectionForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ElementInspector\RowFilterForm.resx">
      <DependentUpon>RowFilterForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ElementInspector\ElementInspectorForm.resx">
      <DependentUpon>ElementInspectorForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ElementInspector\ColumnPickerForm.resx">
      <DependentUpon>ColumnPickerForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormFilterStoreGroups.resx">
      <DependentUpon>FormFilterStoreGroups.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPrintClientStatus.resx">
      <DependentUpon>FormPrintClientStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormVideoImport.resx">
      <DependentUpon>FormVideoImport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormVideoPlayer.resx">
      <DependentUpon>FormVideoPlayer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\GraphicalProfileInspector.resx">
      <DependentUpon>GraphicalProfileInspector.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EditPrintClientName.resx">
      <DependentUpon>EditPrintClientName.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SendFlowWizard\ChooseOutputType.resx">
      <DependentUpon>ChooseOutputType.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SocialShareContainer.resx">
      <DependentUpon>SocialShareContainer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\StoreListForm.resx">
      <DependentUpon>StoreListForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\TemplateProductSettingsForm.resx">
      <DependentUpon>TemplateProductSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\VisibilityForm.resx">
      <DependentUpon>VisibilityForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SetShoppaPictureOnMetaValueForm.resx">
      <DependentUpon>SetShoppaPictureOnMetaValueForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\LunaQRLogin.resx">
      <DependentUpon>LunaQRLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\WelcomeToShoppaGoForm.resx">
      <DependentUpon>WelcomeToShoppaGoForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ConfigureEslForm.resx">
      <DependentUpon>ConfigureEslForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AutoSignage\FormAutoSignage.resx">
      <DependentUpon>FormAutoSignage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormConsumablesBuyNowInfo.resx">
      <DependentUpon>FormConsumablesBuyNowInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormConsumablesPopup.resx">
      <DependentUpon>FormConsumablesPopup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\VideoFileSettingsForm.resx">
      <DependentUpon>VideoFileSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormShapeFillModeChooser.resx">
      <DependentUpon>FormShapeFillModeChooser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BleedDoesNotFitForm.resx">
      <DependentUpon>BleedDoesNotFitForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ImportTemplateConflictForm .resx">
      <DependentUpon>ImportTemplateConflictForm .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FileImporter.resx">
      <DependentUpon>FileImporter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormEslSendFailed.resx">
      <DependentUpon>FormEslSendFailed.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAddProductGroup.resx">
      <DependentUpon>FormAddProductGroup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormLoadingScreen.resx">
      <DependentUpon>FormLoadingScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPromptMediaTypeUsage.resx">
      <DependentUpon>FormPromptMediaTypeUsage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPromptRecomendedMediaTypeUsage.resx">
      <DependentUpon>FormPromptRecomendedMediaTypeUsage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPurposeCreator.resx">
      <DependentUpon>FormPurposeCreator.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ImageAdjustmentsForm.resx">
      <DependentUpon>ImageAdjustmentsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ManageDestinations.resx">
      <DependentUpon>ManageDestinations.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Edit_CustomerGroups.resx">
      <DependentUpon>Edit_CustomerGroups.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ExportTemplatesForm.resx">
      <DependentUpon>ExportTemplatesForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAutomaticPrintingNotification.resx">
      <DependentUpon>FormAutomaticPrintingNotification.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormCampaignEditWarning.resx">
      <DependentUpon>FormCampaignEditWarning.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormExportResolution.resx">
      <DependentUpon>FormExportResolution.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_Director.resx">
      <DependentUpon>Add_Edit_Director.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormMultiCopieQueue.resx">
      <DependentUpon>FormMultiCopieQueue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FlowPlannerContainer.resx">
      <DependentUpon>FlowPlannerContainer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FlowPlayerWithoutChannelDialog.resx">
      <DependentUpon>FlowPlayerWithoutChannelDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormFlowContainerManagement.resx">
      <DependentUpon>FormFlowContainerManagement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormFlowTransitionDefaults.resx">
      <DependentUpon>FormFlowTransitionDefaults.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ManageCustomerGroups.resx">
      <DependentUpon>ManageCustomerGroups.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ModernFormComponents\GlowBorderForm.resx">
      <DependentUpon>GlowBorderForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ModernFormComponents\GlowBorderFixedSizeForm.resx">
      <DependentUpon>GlowBorderFixedSizeForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MovePlayerToChannel.resx">
      <DependentUpon>MovePlayerToChannel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PdfDPiChoice.resx">
      <DependentUpon>PdfDPiChoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PictureDeleteDialogForm.resx">
      <DependentUpon>PictureDeleteDialogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PictureNameClashDialogForm.resx">
      <DependentUpon>PictureNameClashDialogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormVerboseHelp.resx">
      <DependentUpon>FormVerboseHelp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormFriendlyError.resx">
      <DependentUpon>FormFriendlyError.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ExportTemplateForm.resx">
      <DependentUpon>ExportTemplateForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAddressLists.resx">
      <DependentUpon>FormAddressLists.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormFillCustomerMeta.resx">
      <DependentUpon>FormFillCustomerMeta.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAddressEdit.resx">
      <DependentUpon>FormAddressEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAddressEditEnMasse.resx">
      <DependentUpon>FormAddressEditEnMasse.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_Address.resx">
      <DependentUpon>Add_Edit_Address.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAnimationList.resx">
      <DependentUpon>FormAnimationList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CustomerSelectionForm.resx">
      <DependentUpon>CustomerSelectionForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPayment.resx">
      <DependentUpon>FormPayment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPrintshopOrderConfirmation.resx">
      <DependentUpon>FormPrintshopOrderConfirmation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormTemplateFeedback.resx">
      <DependentUpon>FormTemplateFeedback.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormTransitionList.resx">
      <DependentUpon>FormTransitionList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PictureConvertDialogForm.resx">
      <DependentUpon>PictureConvertDialogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PleaseWaitSplash.resx">
      <DependentUpon>PleaseWaitSplash.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPreviewPlaySettings.resx">
      <DependentUpon>FormPreviewPlaySettings.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PrintCampaignDialog.resx">
      <DependentUpon>PrintCampaignDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PrinterWizard\FormPrinterSetup.resx">
      <DependentUpon>FormPrinterSetup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PrinterWizard\Steps\Confirm.resx">
      <DependentUpon>Confirm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PrinterWizard\Steps\SelectPaperSize.resx">
      <DependentUpon>SelectPaperSize.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PrinterWizard\Steps\SelectPaperSource.resx">
      <DependentUpon>SelectPaperSource.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PrinterWizard\Steps\SelectPrinter.resx">
      <DependentUpon>SelectPrinter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PrintshopTeaser.resx">
      <DependentUpon>PrintshopTeaser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PublishCampaignDialog.resx">
      <DependentUpon>PublishCampaignDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\RecoverPrintshopOrders.resx">
      <DependentUpon>RecoverPrintshopOrders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SelectExternalIdAndDepartmentForm.resx">
      <DependentUpon>SelectExternalIdAndDepartmentForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SelectPdfPages.resx">
      <DependentUpon>SelectPdfPages.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SendFlowToFileDoneForm.resx">
      <DependentUpon>SendFlowToFileDoneForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SendFlowWizard\FormSendFlow.resx">
      <DependentUpon>FormSendFlow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SendFlowWizard\Steps\ActionForNew.resx">
      <DependentUpon>ActionForNew.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SendFlowWizard\Steps\ActionForExisting.resx">
      <DependentUpon>ActionForExisting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SendFlowWizard\Steps\ChoseDestination.resx">
      <DependentUpon>ChoseDestination.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SendFlowWizard\Steps\GiveName.resx">
      <DependentUpon>GiveName.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\NavigationTabs\NavigationTabPanel.resx">
      <DependentUpon>NavigationTabPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\WebBrowserFanout.resx">
      <DependentUpon>WebBrowserFanout.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\StartPageBox.resx">
      <DependentUpon>StartPageBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="app.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="ApplicationInsights.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Controls\ColorBox.resx">
      <SubType>Designer</SubType>
      <DependentUpon>ColorBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ColorStrip.resx">
      <SubType>Designer</SubType>
      <DependentUpon>ColorStrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ComplementProductsPlate.resx">
      <DependentUpon>ComplementProductsPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerInfoDesign.resx">
      <SubType>Designer</SubType>
      <DependentUpon>DockerInfoDesign.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerStack.resx">
      <SubType>Designer</SubType>
      <DependentUpon>DockerStack.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DockerTags.resx">
      <SubType>Designer</SubType>
      <DependentUpon>DockerTags.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\FontPicker.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FontPicker.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\FontStylePreview.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FontStylePreview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductDropPlate.resx">
      <SubType>Designer</SubType>
      <DependentUpon>ProductDropPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\SignProductionSurface.resx">
      <DependentUpon>SignProductionSurface.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\OutputFormatInfo.resx">
      <SubType>Designer</SubType>
      <DependentUpon>OutputFormatInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductPlate.resx">
      <SubType>Designer</SubType>
      <DependentUpon>ProductPlate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Rulers.resx">
      <DependentUpon>Rulers.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TemplateDesignSurface.resx">
      <DependentUpon>TemplateDesignSurface.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\UpDownArrow.resx">
      <SubType>Designer</SubType>
      <DependentUpon>UpDownArrow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_AdvancedPrinter.resx">
      <SubType>Designer</SubType>
      <DependentUpon>Add_Edit_AdvancedPrinter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_OutputFormat.resx">
      <SubType>Designer</SubType>
      <DependentUpon>Add_Edit_OutputFormat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_Page.resx">
      <DependentUpon>Add_Edit_Page.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ChangePasswordForm.resx">
      <DependentUpon>ChangePasswordForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ChoseFinalDestination2.resx">
      <SubType>Designer</SubType>
      <DependentUpon>ChoseFinalDestination2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Add_Edit_Label.resx">
      <DependentUpon>Add_Edit_Label.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPrintshopOrder.resx">
      <DependentUpon>FormPrintshopOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormSignProduction.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormSignProduction.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormTemplateBase.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormTemplateBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormTemplateDesign.resx">
      <DependentUpon>FormTemplateDesign.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\LoginForm_ResetPassword.resx">
      <DependentUpon>LoginForm_ResetPassword.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\NameInputForm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>NameInputForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\RecoverForm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>RecoverForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="InputBox\InputBox.resx">
      <DependentUpon>InputBox.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="printers.config" />
    <None Include="Resources\DTResolutionExclamation.wmf" />
    <None Include="Resources\loadingScreenHeartData" />
    <None Include="Resources\delete.png" />
    <None Include="Resources\circle.png" />
    <None Include="Resources\lock.png" />
    <None Include="Resources\save.png" />
    <EmbeddedResource Include="Printing\DeliveryNote.html" />
    <EmbeddedResource Include="Printing\DeliveryAddress.html" />
    <None Include="Resources\arrowClean.png" />
    <None Include="Resources\selectNone.png" />
    <None Include="Resources\TreeFolder.png" />
    <None Include="Resources\close.png" />
    <None Include="Resources\close_hover.png" />
    <EmbeddedResource Include="Printing\PackingSlip.html" />
    <None Include="Resources\infopanel_field.png" />
    <None Include="Resources\infopanel_space.png" />
    <None Include="Resources\infopanel_text.png" />
    <None Include="Resources\delete_inactive.png" />
    <None Include="Resources\butik.png" />
    <None Include="Resources\change_name.png" />
    <None Include="Resources\addLokalChannel.png" />
    <None Include="Resources\addCentralChannel.png" />
    <None Include="Resources\S3_Campaignplusminus_Duplicate.png" />
    <None Include="Resources\S3_Campaignplusminus_Minus.png" />
    <None Include="Resources\S3_Campaignplusminus_Plus.png" />
    <None Include="Resources\S3_Marvellous_Dropdown.png" />
    <None Include="Resources\S3_Search_Campaigns_New.png" />
    <None Include="Resources\S3_Search_Campaigns_Hover_New.png" />
    <None Include="Resources\S3_Treesmall_CampaignFolderup.png" />
    <None Include="Resources\S3_MovieFlap.png" />
    <None Include="Resources\S3_Treesmall_Folderup.png" />
    <None Include="Resources\S3_ThumbBig.png" />
    <None Include="Resources\S3_ThumbBig_Selected.png" />
    <None Include="Resources\S3_ThumbMedium.png" />
    <None Include="Resources\S3_ThumbMedium_Selected.png" />
    <None Include="Resources\S3_ThumbSmall.png" />
    <None Include="Resources\S3_ThumbSmall_Selected.png" />
    <None Include="Resources\S3_Toolbox_Shadow_Arrow.png" />
    <None Include="Resources\S3_Searchfilteroff.png" />
    <None Include="Resources\S3_Animate_Use.png" />
    <None Include="Resources\S3_Animate_Info.png" />
    <None Include="Resources\S3_Layouts_ds.png" />
    <None Include="Resources\S3_Design_PreviewEsl.png" />
    <None Include="Resources\S3_Arrange_PlaceInPixelGrid.png" />
    <None Include="Resources\S3_Send_Print_Esl.png" />
    <None Include="Resources\S3_Send_Print_Esl_hover.png" />
    <None Include="Resources\S3_Send_Print_Esl_Inactive.png" />
    <None Include="Resources\S3_Send_Print_ESL_only.png" />
    <None Include="Resources\S3_Send_Print_ESL_only_inactive.png" />
    <None Include="Resources\S3_Send_Print_ESL_only_hover.png" />
    <None Include="Resources\S3_Autosinage_type_ds.png" />
    <None Include="Resources\S3_Autosinage_type_esl.png" />
    <None Include="Resources\S3_Autosinage_type_print.png" />
    <None Include="Resources\S3_Autosinage_triggerType_products.png" />
    <None Include="Resources\S3_Autosignage_triggerType_groups.png" />
    <None Include="Resources\s3_prev.png" />
    <None Include="Resources\s3_next.png" />
    <None Include="Resources\S3_Autosignage_DS.png" />
    <None Include="Resources\S3_Autosignagetype_ESL.png" />
    <None Include="Resources\S3_Autosignagetype_Print.png" />
    <None Include="Resources\S3_Autosignagetype_DS.png" />
    <None Include="Resources\S3_Autosignage_Localintegration.png" />
    <None Include="Resources\S3_Autosignage_Group.png" />
    <None Include="Resources\S3_Autosignage_Product.png" />
    <None Include="Resources\S3_General_Smallremove.png" />
    <None Include="Resources\S3_Small_Copy.png" />
    <None Include="Resources\S3_Autosignage_White_DS.png" />
    <None Include="Resources\S3_Autosignage_White_Group.png" />
    <None Include="Resources\S3_Autosignage_White_Localintegration.png" />
    <None Include="Resources\S3_Autosignage_White_Product.png" />
    <None Include="Resources\VideoStopBox.png" />
    <None Include="Resources\VideoPlayArrow.png" />
    <None Include="Resources\playButtonBig.png" />
    <None Include="Resources\playButtonStopBig.png" />
    <None Include="Resources\S3_Toolbox_Line.png" />
    <None Include="Resources\colorPicker.png" />
    <None Include="Resources\pickerBar.png" />
    <None Include="Resources\pickerRing.png" />
    <None Include="Resources\ColorPicker_Bar_Dark.png" />
    <None Include="Resources\ColorPicker_Bar_Bright.png" />
    <None Include="Resources\ColorPicker_Icon.png" />
    <None Include="Resources\ColorPicker_Ring.png" />
    <None Include="Resources\S3_Toolbox_MediaInfo.png" />
    <None Include="Resources\S3_Arrange_Stack_Line.png" />
    <None Include="Resources\S3_Arrange_Tagview_Line.png" />
    <None Include="Resources\S3_Designbig_Imgedit_Open_Modify.png" />
    <None Include="Resources\S3_PictureBoxImageAdjustments.png" />
    <None Include="Resources\S3_General_Imageadjust.png" />
    <None Include="Resources\S3_Designbig_Import.png" />
    <None Include="Resources\S3_Designbig_Imgedit_Open_New.png" />
    <None Include="Resources\logo_splash_bigger.png" />
    <None Include="Resources\EmptyProduct_Icon.png" />
    <None Include="Resources\S3_Campaign_big.png" />
    <Content Include="Resources\S3_Campaign_Newfolder.png" />
    <Content Include="Resources\S3_Imgedit_Mirror_Horizontal.png" />
    <Content Include="Resources\S3_Imgedit_Mirror_Vertical.png" />
    <None Include="Resources\S3_Imgedit_Next.png" />
    <None Include="Resources\S3_Imgedit_Prev.png" />
    <None Include="Resources\S3_Palette_Revert.png" />
    <None Include="Resources\S3_Palette_Trash.png" />
    <None Include="Resources\S3_Palette_AddColor.png" />
    <None Include="Resources\S3_Palette_Picker.png" />
    <None Include="Resources\S3_Plus_Infooutline.png" />
    <None Include="Resources\S3_Send_Sheet.png" />
    <None Include="Resources\S3_Send_Sheet_Hover.png" />
    <None Include="Resources\S3_Send_publishonly_inactive.png" />
    <None Include="Resources\S3_Send_publishonly.png" />
    <None Include="Resources\S3_Send_publishonly_hover.png" />
    <None Include="Resources\S3_Searchfilter_NoArrow.png" />
    <None Include="Resources\S3_Tree_Campaign_Group.png" />
    <None Include="Resources\S3_Tree_Campaignsign_Group.png" />
    <None Include="Resources\S3_Tree_Folder_Group.png" />
    <None Include="Resources\S3_Tree_Folder_Invisible_Group.png" />
    <None Include="Resources\S3_Tree_Folder_Invisibleclock_Group.png" />
    <None Include="Resources\S3_Tree_Template_Group.png" />
    <None Include="Resources\S3_Tree_Template_Invisible_Group.png" />
    <None Include="Resources\S3_Tree_Template_Invisibleclock_Group.png" />
    <None Include="Resources\S3_Treebig_Group.png" />
    <None Include="Resources\Wait.gif" />
    <None Include="Resources\S3_Imgedit_Rotate_Clockwise.png" />
    <None Include="Resources\S3_Imgedit_Rotate_CounterClockwise.png" />
    <None Include="Resources\S3_Tree_CampaignRecording.png" />
    <None Include="Resources\S3_Treebig_CampaignRecording.png" />
    <None Include="Resources\S3_Treebig_Campaign_Invisible.png" />
    <None Include="Resources\S3_Treebig_Campaign_Invisibletime.png" />
    <None Include="Resources\S3_Tree_CampaignRecording_Invisible.png" />
    <None Include="Resources\S3_Tree_CampaignRecording_InvisibleTime.png" />
    <None Include="Resources\S3_Tree_CampaignRecording_Group.png" />
    <None Include="Resources\S3_Tree_OutputFormatFolder_Invisible.png" />
    <None Include="Resources\S3_Treebig_OutputFormatFolder.png" />
    <None Include="Resources\S3_Treebig_OutputFormatFolder_invicible.png" />
    <None Include="Resources\S3_Tree_OutputFormatFolder.png" />
    <None Include="Resources\templateProductSettingsIcon.png" />
    <None Include="Resources\S3_Toolbox_Web.png" />
    <None Include="Resources\S3_Toolbox_DC.png" />
    <Content Include="Shoppa3.ico" />
    <Content Include="Resources\Checked.png" />
    <None Include="Resources\control_img.png" />
    <None Include="Resources\close_mini.png" />
    <None Include="Resources\close_hover_mini.png" />
    <Content Include="Resources\DefaultProductPlate.xml" />
    <None Include="Resources\designmenu_info.png" />
    <Content Include="Resources\DTRotateCursor.png" />
    <None Include="Resources\exclamationSign.bmp" />
    <None Include="Resources\empty.png" />
    <Content Include="Resources\ExlamationSign.png" />
    <None Include="Resources\img_splash.png" />
    <Content Include="Resources\ListViewFolderClosed.png" />
    <None Include="Resources\myproducts.png" />
    <None Include="Resources\newprint.png" />
    <None Include="Resources\newtime.png" />
    <None Include="Resources\new_folder_small.png" />
    <None Include="Resources\loading.html" />
    <None Include="Resources\logo_splash.png" />
    <None Include="Resources\locked_ratio.png" />
    <None Include="Resources\modal_info.png" />
    <None Include="Resources\loadingScreenText.png" />
    <None Include="Resources\printshop_symbolmedpil.png" />
    <None Include="Resources\Stor_Flow_Andra.png" />
    <None Include="Resources\Stor_Adress_Andra.png" />
    <None Include="Resources\Stor_Adress_Ny.png" />
    <None Include="Resources\none_drop.png" />
    <None Include="Resources\SizesArrowDown.png" />
    <None Include="Resources\SizesArrowLeft.png" />
    <None Include="Resources\SizesArrowRight.png" />
    <None Include="Resources\SizesArrowUp.png" />
    <None Include="Resources\tinySettings.png" />
    <None Include="Resources\preview.png" />
    <None Include="Resources\pan_hand.png" />
    <None Include="Resources\pan_hand_closed.png" />
    <None Include="Resources\pdf_loading.gif" />
    <None Include="Resources\question_.png" />
    <None Include="Resources\programLogo_small.png" />
    <None Include="Resources\seen.png" />
    <None Include="Resources\Status-image-loading-icon.png" />
    <None Include="Resources\S3_Flow_Duration.png" />
    <None Include="Resources\S3_Flow_Entry.png" />
    <None Include="Resources\S3_Flow_Exit.png" />
    <None Include="Resources\S3_Flow_Main.png" />
    <None Include="Resources\S3_General_Copy.png" />
    <None Include="Resources\S3_General_Cornerradius.png" />
    <None Include="Resources\S3_General_Cut.png" />
    <None Include="Resources\S3_General_Delete.png" />
    <None Include="Resources\S3_General_EAN128.png" />
    <None Include="Resources\S3_General_EANblank.png" />
    <None Include="Resources\S3_General_Imagefill.png" />
    <None Include="Resources\S3_General_Imagefit.png" />
    <None Include="Resources\S3_General_Leading.png" />
    <None Include="Resources\S3_General_Limitrows.png" />
    <None Include="Resources\S3_General_Paste.png" />
    <None Include="Resources\S3_General_Preview.png" />
    <None Include="Resources\S3_General_Replace.png" />
    <None Include="Resources\S3_General_Resettrans.png" />
    <None Include="Resources\S3_General_Selectall.png" />
    <None Include="Resources\S3_General_Shadowfeather.png" />
    <None Include="Resources\S3_General_Shadowhide.png" />
    <None Include="Resources\S3_General_Shadowopacity.png" />
    <None Include="Resources\S3_General_Shadowshow.png" />
    <None Include="Resources\S3_General_Textmulti.png" />
    <None Include="Resources\S3_General_Textsingle.png" />
    <None Include="Resources\S3_General_Tracking.png" />
    <None Include="Resources\S3_Design_Enter.png" />
    <None Include="Resources\S3_Design_Exit.png" />
    <None Include="Resources\S3_Plus_Templateinfo.png" />
    <None Include="Resources\S3_Toolbox_Info.png" />
    <None Include="Resources\S3_Send_Plus.png" />
    <None Include="Resources\S3_Send_Plus-Multi.png" />
    <None Include="Resources\S3_Send_Remove.png" />
    <None Include="Resources\S3_Send_Plus_Hover %281%29.png" />
    <None Include="Resources\S3_Send_Plus_Inactive.png" />
    <None Include="Resources\S3_Send_Plus-Multi_Hover.png" />
    <None Include="Resources\S3_Send_Plus-Multi_Inactive.png" />
    <None Include="Resources\S3_Send_Remove_Hover.png" />
    <None Include="Resources\S3_Send_Cart.png" />
    <None Include="Resources\S3_Send_Cart_Inactive.png" />
    <None Include="Resources\S3_Send_DS.png" />
    <None Include="Resources\S3_Send_DS_Inactive.png" />
    <None Include="Resources\S3_Send_Print.png" />
    <None Include="Resources\S3_Send_Print_Inactive.png" />
    <None Include="Resources\S3_Send_Cart_hove_l.png" />
    <None Include="Resources\S3_Send_DS_hover_l.png" />
    <None Include="Resources\S3_Send_Print_hover_l.png" />
    <None Include="Resources\S3_Send_Printonly.png" />
    <None Include="Resources\S3_Send_Printonly_hover.png" />
    <None Include="Resources\S3_Send_Printonly_Inactive.png" />
    <None Include="Resources\S3_Address_Addressedit.png" />
    <None Include="Resources\S3_Address_Addressnew.png" />
    <None Include="Resources\S3_Address_Listnew.png" />
    <None Include="Resources\S3_Address_Trash.png" />
    <None Include="Resources\S3_Feedback_Marker.png" />
    <None Include="Resources\S3_Feedback_Pen.png" />
    <None Include="Resources\S3_Feedback_Undo.png" />
    <None Include="Resources\S3_Managechannels_Addplayer.png" />
    <None Include="Resources\S3_Managechannels_Channelglobal.png" />
    <None Include="Resources\S3_Managechannels_Channellocal.png" />
    <None Include="Resources\S3_Managechannels_Newglobal.png" />
    <None Include="Resources\S3_Managechannels_Newlocal.png" />
    <None Include="Resources\S3_Managechannels_Registerplayer.png" />
    <None Include="Resources\S3_Managechannels_Bigscreenlandscape.png" />
    <None Include="Resources\S3_Managechannels_Bigscreenportrait.png" />
    <None Include="Resources\S3_Address_Smalladdress.png" />
    <None Include="Resources\S3_Address_Smalllist.png" />
    <None Include="Resources\S3_Address_Warning.png" />
    <None Include="Resources\S3_Arrange_Align_Bottoms.png" />
    <None Include="Resources\S3_Arrange_Align_Centers.png" />
    <None Include="Resources\S3_Arrange_Align_Lefts.png" />
    <None Include="Resources\S3_Arrange_Align_Middles.png" />
    <None Include="Resources\S3_Arrange_Align_Rights.png" />
    <None Include="Resources\S3_Arrange_Align_Tops.png" />
    <None Include="Resources\S3_Arrange_Alignbottom.png" />
    <None Include="Resources\S3_Arrange_Aligncenter.png" />
    <None Include="Resources\S3_Arrange_Alignleft.png" />
    <None Include="Resources\S3_Arrange_Alignmiddle.png" />
    <None Include="Resources\S3_Arrange_Alignright.png" />
    <None Include="Resources\S3_Arrange_Aligntop.png" />
    <None Include="Resources\S3_Arrange_Backfromstack.png" />
    <None Include="Resources\S3_Arrange_Filter_Barcode.png" />
    <None Include="Resources\S3_Arrange_Filter_Ellipse.png" />
    <None Include="Resources\S3_Arrange_Filter_Image.png" />
    <None Include="Resources\S3_Arrange_Filter_Price.png" />
    <None Include="Resources\S3_Arrange_Filter_Rectangle.png" />
    <None Include="Resources\S3_Arrange_Filter_Shape.png" />
    <None Include="Resources\S3_Arrange_Filter_Text.png" />
    <None Include="Resources\S3_Arrange_Group.png" />
    <None Include="Resources\S3_Arrange_Groupungroup.png" />
    <None Include="Resources\S3_Arrange_Groupungroupall.png" />
    <None Include="Resources\S3_Arrange_Lock.png" />
    <None Include="Resources\S3_Arrange_Lockun.png" />
    <None Include="Resources\S3_Arrange_Makesameall.png" />
    <None Include="Resources\S3_Arrange_Outputhide.png" />
    <None Include="Resources\S3_Arrange_Outputshow.png" />
    <None Include="Resources\S3_Arrange_Pagecenterhorizons.png" />
    <None Include="Resources\S3_Arrange_Pagecenterverticals.png" />
    <None Include="Resources\S3_Arrange_Sameheight.png" />
    <None Include="Resources\S3_Arrange_Samewidth.png" />
    <None Include="Resources\S3_Arrange_Spaceshorizontal.png" />
    <None Include="Resources\S3_Arrange_Spacesvertical.png" />
    <None Include="Resources\S3_Arrange_Stack_Barcode.png" />
    <None Include="Resources\S3_Arrange_Stack_Ellipse.png" />
    <None Include="Resources\S3_Arrange_Stack_Image.png" />
    <None Include="Resources\S3_Arrange_Stack_Rectangle.png" />
    <None Include="Resources\S3_Arrange_Stack_Shape.png" />
    <None Include="Resources\S3_Arrange_Stack_Text.png" />
    <None Include="Resources\S3_Arrange_Tagview_Barcode.png" />
    <None Include="Resources\S3_Arrange_Tagview_Ellipse.png" />
    <None Include="Resources\S3_Arrange_Tagview_Image.png" />
    <None Include="Resources\S3_Arrange_Tagview_Rectangle.png" />
    <None Include="Resources\S3_Arrange_Tagview_Shape.png" />
    <None Include="Resources\S3_Arrange_Tagview_Text.png" />
    <None Include="Resources\S3_Arrange_Zbottom.png" />
    <None Include="Resources\S3_Arrange_Zdown.png" />
    <None Include="Resources\S3_Arrange_Ztop.png" />
    <None Include="Resources\S3_Arrange_Zup.png" />
    <None Include="Resources\S3_Campaign.png" />
    <None Include="Resources\S3_Campaign_Cancel.png" />
    <None Include="Resources\S3_Campaign_Cancel_Hover.png" />
    <None Include="Resources\S3_Campaign_Edit.png" />
    <None Include="Resources\S3_Campaign_New.png" />
    <None Include="Resources\S3_Campaign_Plus.png" />
    <None Include="Resources\S3_Campaign_Plus_Hover.png" />
    <None Include="Resources\S3_Campaign_Plus-multi.png" />
    <None Include="Resources\S3_Campaign_Plus-multi_Hover.png" />
    <None Include="Resources\S3_Campaign_Publish.png" />
    <None Include="Resources\S3_Campaign_Revert.png" />
    <None Include="Resources\S3_Campaign_Trash.png" />
    <None Include="Resources\S3_Campaignpopup_Checkbox.png" />
    <None Include="Resources\S3_Campaignpopup_Checkboxchecked.png" />
    <None Include="Resources\S3_Campaignpopup_Checkboxunchecked.png" />
    <None Include="Resources\S3_Campaignpopup_New.png" />
    <None Include="Resources\S3_Campaignpopup_Print.png" />
    <None Include="Resources\S3_Design_Colornone.png" />
    <None Include="Resources\S3_Design_Coloroutline.png" />
    <None Include="Resources\S3_Design_Colorpal.png" />
    <None Include="Resources\S3_Designbig_Newfolder.png" />
    <None Include="Resources\S3_Designbig_Newimage.png" />
    <None Include="Resources\S3_Designbig_Newtemplate.png" />
    <None Include="Resources\S3_Designbig_Trash.png" />
    <None Include="Resources\S3_General_Bullets.png" />
    <None Include="Resources\S3_Layer_Balledit.png.png" />
    <None Include="Resources\S3_Layer_Ballnew.png" />
    <None Include="Resources\S3_Layer_Bighide.png" />
    <None Include="Resources\S3_Layer_Bigshow.png" />
    <None Include="Resources\S3_Layer_Smallhide.png" />
    <None Include="Resources\S3_Layer_Smallshow.png" />
    <None Include="Resources\S3_LeftTab-DS_Manage.png" />
    <None Include="Resources\S3_LeftTab-DS_Scheduler.png" />
    <None Include="Resources\S3_LeftTab-DS_thumb-bg.png" />
    <None Include="Resources\S3_LeftTab-DS_thumb-overlayfade.png" />
    <None Include="Resources\S3_LeftTab-DS_thumb-overlayfade-selected.png" />
    <None Include="Resources\S3_LeftTabTabs_DS.png" />
    <None Include="Resources\S3_LeftTabTabs_Images.png" />
    <None Include="Resources\S3_LeftTabTabs_Printqueue.png" />
    <None Include="Resources\S3_LeftTabTabs_Templates.png" />
    <None Include="Resources\S3_Managechannels_Screenlandscape.png" />
    <None Include="Resources\S3_Managechannels_Screenportrait.png" />
    <None Include="Resources\S3_Plus_Lowreswarning.png" />
    <None Include="Resources\S3_Plus_New.png" />
    <None Include="Resources\S3_Plus_PDF.png" />
    <None Include="Resources\S3_Plus_Revert.png" />
    <None Include="Resources\S3_Precision_Height.png" />
    <None Include="Resources\S3_Precision_Locked.png" />
    <None Include="Resources\S3_Precision_Rotate.png" />
    <None Include="Resources\S3_Precision_Width.png" />
    <None Include="Resources\S3_Productlist_campaign.png" />
    <None Include="Resources\S3_Productlist_complement-product.png" />
    <None Include="Resources\S3_Productlist_complement-product-icon.png" />
    <None Include="Resources\S3_Search.png" />
    <None Include="Resources\S3_Search_Campaigns.png" />
    <None Include="Resources\S3_Search_Date.png" />
    <None Include="Resources\S3_Search_groups.png" />
    <None Include="Resources\S3_Search_Multi.png" />
    <None Include="Resources\S3_Search_pos.png" />
    <None Include="Resources\S3_Search_Remove.png" />
    <None Include="Resources\S3_Search_Single.png" />
    <None Include="Resources\S3_Searchfilter.png" />
    <None Include="Resources\S3_Starterror_Connect.png" />
    <None Include="Resources\S3_Starterror_Connection.png" />
    <None Include="Resources\S3_Starterror_Folder.png" />
    <None Include="Resources\S3_Starterror_Installer.png" />
    <None Include="Resources\S3_Starterror_RAM.png" />
    <None Include="Resources\S3_Starterror_Support.png" />
    <None Include="Resources\S3_Starterror_Timechanged.png" />
    <None Include="Resources\S3_Tags_Image.png" />
    <None Include="Resources\S3_Tags_Showonlyactive.png" />
    <None Include="Resources\S3_Templatelayouts_Landscape.png" />
    <None Include="Resources\S3_Templatelayouts_Portrait.png" />
    <None Include="Resources\S3_Toolbox_Arrow.png" />
    <None Include="Resources\S3_Toolbox_Barcode.png" />
    <None Include="Resources\S3_Toolbox_Ellipse.png" />
    <None Include="Resources\S3_Toolbox_Hand.png" />
    <None Include="Resources\S3_Toolbox_Image.png" />
    <None Include="Resources\S3_Toolbox_Price.png" />
    <None Include="Resources\S3_Toolbox_Rectangle.png" />
    <None Include="Resources\S3_Toolbox_Shadow.png" />
    <None Include="Resources\S3_Toolbox_Shape.png" />
    <None Include="Resources\S3_Toolbox_Text.png" />
    <None Include="Resources\S3_Toolbox_Zoom.png" />
    <None Include="Resources\S3_Tree_Campaign.png" />
    <None Include="Resources\S3_Tree_Campaignsign.png" />
    <None Include="Resources\S3_Tree_Folder.png" />
    <None Include="Resources\S3_Tree_Folder_Invisible.png" />
    <None Include="Resources\S3_Tree_Folder_Invisibleclock.png" />
    <None Include="Resources\S3_Tree_Folderclose.png" />
    <None Include="Resources\S3_Tree_Template.png" />
    <None Include="Resources\S3_Tree_Template_Invisible.png" />
    <None Include="Resources\S3_Tree_Template_Invisibleclock.png" />
    <None Include="Resources\S3_Treebig_Campaign.png" />
    <None Include="Resources\S3_Treebig_CampaignUp.png" />
    <None Include="Resources\S3_Treebig_Folder.png" />
    <None Include="Resources\S3_Treebig_Folder_Invisible.png" />
    <None Include="Resources\S3_Treebig_Folderup.png" />
    <None Include="Resources\S3_Treebig_Time.png" />
    <None Include="Resources\S3_Window_Close.png" />
    <None Include="Resources\S3_Window_Maximize.png" />
    <None Include="Resources\S3_Window_Minimize.png" />
    <None Include="Resources\S3_Window_Restore.png" />
    <None Include="Resources\S3_Design_Onlyvisible.png" />
    <None Include="Resources\S3_Design_Preview.png" />
    <None Include="Resources\S3_Design_Redo.png" />
    <None Include="Resources\S3_Design_Save.png" />
    <None Include="Resources\S3_Design_Undo.png" />
    <None Include="Resources\S3_Layer_Bigshow_Inverted.png" />
    <None Include="Resources\S3_Layer_Smallhide_Inverted.png" />
    <None Include="Resources\S3_Layer_Smallshow_Inverted.png" />
    <None Include="Resources\S3_Toolbox_Layoutsedit.png" />
    <None Include="Resources\S3_Toolbox_Layoutslink.png" />
    <None Include="Resources\S3_Toolbox_Layoutssuperlink.png" />
    <None Include="Resources\S3_Layer_Bighide_Inverted.png" />
    <None Include="Resources\S3_Dropdown.png" />
    <None Include="Resources\S3_Toolbox_Layoutssuperlinked.png" />
    <None Include="Resources\S3_Toolbox_Layoutslinked.png" />
    <None Include="Resources\S3_Treebig_Folder_Invisibletime.png" />
    <None Include="Resources\S3_Search_groups_disabled.png" />
    <None Include="Resources\S3_Search_groups_hover.png" />
    <None Include="Resources\S3_Search_Campaigns_disabled.png" />
    <None Include="Resources\S3_Search_Campaigns_hover.png" />
    <None Include="Resources\S3_Search_pos_disabled.png" />
    <None Include="Resources\S3_Search_pos_hover.png" />
    <None Include="Resources\S3_Search_Remove_hover.png" />
    <None Include="Resources\S3_Campaign_Publish_hover.png" />
    <None Include="Resources\S3_Campaign_Publish_inactive.png" />
    <None Include="Resources\tree template invisible.png" />
    <None Include="Resources\tree folder.png" />
    <None Include="Resources\S3_Cross_Tiny.png" />
    <None Include="Resources\S3_Plus_Sync_Offline.png" />
    <None Include="Resources\S3_Campaign_Abort.png" />
    <None Include="Resources\S3_Account_Sync.gif" />
    <EmbeddedResource Include="Resources\S3_Account_Sync_ani.gif" />
    <None Include="Resources\S3_Account_Syncoffline.gif" />
    <EmbeddedResource Include="Resources\S3_Account_Syncoffline_ani.gif" />
    <None Include="Resources\Shoppa3.ico" />
    <None Include="Resources\S3_Check.png" />
    <Content Include="Resources\TreeHomeFolder.png" />
    <Content Include="Resources\TreeMicroTemplate.png" />
    <BaseApplicationManifest Include="Properties\app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="SHDocVw">
      <Guid>{EAB22AC0-30C1-11CF-A7EB-0000C05BAE0B}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>1</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.SQL.Server.Compact.3.5">
      <Visible>False</Visible>
      <ProductName>SQL Server Compact 3.5 SP2</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Visual.C++.10.0.x64">
      <Visible>False</Visible>
      <ProductName>Visual C++ 2010 Runtime Libraries %28x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Visual.C++.10.0.x86">
      <Visible>False</Visible>
      <ProductName>Visual C++ 2010 Runtime Libraries %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\FlowCinema\PreviewPlayer\PreviewPlayer.csproj">
      <Project>{21c01bb5-c0cc-4b11-8e61-d7f76bcd1aec}</Project>
      <Name>PreviewPlayer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.OpenID.Client\ShoppaAB.OpenID.Client.csproj">
      <Project>{06b82d5a-f419-4158-be39-740ceb876866}</Project>
      <Name>ShoppaAB.OpenID.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\Shoppa.WinApi\Shoppa.WinApi.csproj">
      <Project>{A285B77C-E09E-4913-B54A-6A4EBF280FAC}</Project>
      <Name>Shoppa.WinApi</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\Shoppa.Rendering\Shoppa.Rendering.csproj">
      <Project>{93652d1c-a83d-469c-9a62-5dbad68cdeab}</Project>
      <Name>Shoppa.Rendering</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\CustomScrollShoppa\CustomScrollBar.csproj">
      <Project>{80c7dc34-e1f6-4da8-affb-904c06a3623b}</Project>
      <Name>CustomScrollBar</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Mediablob\Public\Mediablob.Objects.Public\Mediablob.Objects.Public.csproj">
      <Project>{9F7F8D75-B8E7-4EA4-B6E3-8B5747A0C93E}</Project>
      <Name>Mediablob.Objects.Public</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Externals\Aga.Controls\Aga.Controls.csproj">
      <Project>{E73BB233-D88B-44A7-A98F-D71EE158381D}</Project>
      <Name>Aga.Controls</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Externals\ImageListView2\ImageListView.csproj">
      <Project>{0c295fb8-c6c6-4a40-9f19-05a43f353a04}</Project>
      <Name>ImageListView</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.ActiveDirectory\ShoppaAB.ActiveDirectory.csproj">
      <Project>{fc427adc-ce51-47da-9b67-eb89956e4a5b}</Project>
      <Name>ShoppaAB.ActiveDirectory</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.AutoDiscovery\ShoppaAB.Autodiscovery.csproj">
      <Project>{a9565345-f52a-466b-9ba8-cf4c97a10fd6}</Project>
      <Name>ShoppaAB.Autodiscovery</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.Diagnostics\ShoppaAB.Diagnostics.csproj">
      <Project>{5f542a17-7882-4475-b1f3-4bdfcf973703}</Project>
      <Name>ShoppaAB.Diagnostics</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.Flow\ShoppaAB.Flow.csproj">
      <Project>{9d232a86-9b79-4bdd-b462-4f1115c6c35b}</Project>
      <Name>ShoppaAB.Flow</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.Forms\ShoppaAB.Forms.csproj">
      <Project>{22B9995A-1336-49DB-996F-8D56F2EE6900}</Project>
      <Name>ShoppaAB.Forms</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.Imaging\ShoppaAB.Imaging.csproj">
      <Project>{077b604d-02dc-4bed-80d9-8c0ca2aa9498}</Project>
      <Name>ShoppaAB.Imaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.IO\ShoppaAB.IO.csproj">
      <Project>{dec78f90-3b8b-4241-87cc-0e31ae1c0d65}</Project>
      <Name>ShoppaAB.IO</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.Objects\ShoppaAB.Objects.csproj">
      <Project>{0ADCF7F2-7C0F-4715-8017-B05ABFF29039}</Project>
      <Name>ShoppaAB.Objects</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\Shoppa.ConnectionTest\Shoppa.ConnectionTest.csproj">
      <Project>{F0C29969-092C-4738-BAAA-AA5A1E6AE4C6}</Project>
      <Name>Shoppa.ConnectionTest</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\Shoppa.Plugins\Shoppa.Plugins.csproj">
      <Project>{765FD34E-8A00-4394-994E-F1F8D79C78A7}</Project>
      <Name>Shoppa.Plugins</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Public\Shoppa.Objects.Common\Shoppa.Objects.Common.csproj">
      <Project>{0F81DC68-7716-4253-9AFA-9BD26FCFB3A2}</Project>
      <Name>Shoppa.Objects.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Tools\PrinterConfigTool\PrinterConfigTool.csproj">
      <Project>{a14522bb-2fe0-4600-a9aa-ec14b5407b2d}</Project>
      <Name>PrinterConfigTool</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.Controllers\Shoppa.Controllers.csproj">
      <Project>{F2A02680-0806-463C-AA21-A5AF2F56EC6A}</Project>
      <Name>Shoppa.Controllers</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.DataLayer.Embedded.SQLite\Shoppa.DataLayer.Embedded.SQLite.csproj">
      <Project>{695777EA-C9FA-4D97-AC0C-0681AFE20A08}</Project>
      <Name>Shoppa.DataLayer.Embedded.SQLite</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.DataLayer.Embedded\Shoppa.DataLayer.Embedded.csproj">
      <Project>{A0864D29-8CC0-4DD5-9664-5AD1C0189206}</Project>
      <Name>Shoppa.DataLayer.Embedded</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.DataLayer.FlowDirector\Shoppa.DataLayer.FlowDirector.csproj">
      <Project>{291e59d2-9102-4db9-b6a2-65faeb2aaf46}</Project>
      <Name>Shoppa.DataLayer.FlowDirector</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.DataLayer.Server.Remoting\Shoppa.DataLayer.Server.Remoting.csproj">
      <Project>{661089DA-83FC-4C6B-A5FB-D5FE1A2F3D44}</Project>
      <Name>Shoppa.DataLayer.Server.Remoting</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.DataLayer.Server\Shoppa.DataLayer.Server.csproj">
      <Project>{F93D9F21-52A0-4875-B675-3EAA3ECDE722}</Project>
      <Name>Shoppa.DataLayer.Server</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Public\Shoppa.Objects.XmlInterfaces\Shoppa.Objects.XmlInterfaces.csproj">
      <Project>{E30C156B-64C9-4BE9-A484-776F057CBD4F}</Project>
      <Name>Shoppa.Objects.XmlInterfaces</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.FileImporter\Shoppa.FileImporter.csproj">
      <Project>{77777777-11C9-430F-9BDD-D1FFA09128DE}</Project>
      <Name>Shoppa.FileImporter</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.Objects\Shoppa.Objects.csproj">
      <Project>{FE4CDC22-2527-4B15-90FD-1A75E31AD50B}</Project>
      <Name>Shoppa.Objects</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.PrinterDriver\Shoppa.PrinterDriver.csproj">
      <Project>{4990bd81-6e0a-405b-b2e6-55f49229cf2c}</Project>
      <Name>Shoppa.PrinterDriver</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.ProductCache.Legacy\Shoppa.ProductCache.Legacy.csproj">
      <Project>{72c4f78d-6886-495f-9722-aa340558774f}</Project>
      <Name>Shoppa.ProductCache.Legacy</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.ProductCache.SQLite\Shoppa.ProductCache.SQLite.csproj">
      <Project>{667a0306-7fe3-490d-ae22-c43be618bf03}</Project>
      <Name>Shoppa.ProductCache.SQLite</Name>
    </ProjectReference>
    <ProjectReference Include="..\Shoppa.TitleBarComponents\TitleBarComponents.csproj">
      <Project>{95fbe52b-eb15-453e-b98e-25ee452995f8}</Project>
      <Name>TitleBarComponents</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="Devart.Data">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Devart.Data.MySql">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="stdole">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <!-- Redefine this list without .dll.config; see https://github.com/Microsoft/msbuild/issues/1307 -->
    <AllowedReferenceRelatedFileExtensions>.pdb</AllowedReferenceRelatedFileExtensions>
    <PostBuildEvent>xcopy "$(ProjectDir)..\..\LIB\Files2Copy\*.*" $(OutputDir) /E /D /Y</PostBuildEvent>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <Import Project="..\..\..\packages\GitInfo.2.0.20\build\GitInfo.targets" Condition="Exists('..\..\..\packages\GitInfo.2.0.20\build\GitInfo.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\packages\GitInfo.2.0.20\build\GitInfo.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\GitInfo.2.0.20\build\GitInfo.targets'))" />
    <Error Condition="!Exists('..\..\..\packages\System.Data.SQLite.Core.1.0.112.0\build\net46\System.Data.SQLite.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\System.Data.SQLite.Core.1.0.112.0\build\net46\System.Data.SQLite.Core.targets'))" />
    <Error Condition="!Exists('..\..\..\packages\Microsoft.Web.WebView2.1.0.1264.42\build\Microsoft.Web.WebView2.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\Microsoft.Web.WebView2.1.0.1264.42\build\Microsoft.Web.WebView2.targets'))" />
    <Error Condition="!Exists('..\..\..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets'))" />
  </Target>
  <Import Project="..\..\..\packages\System.Data.SQLite.Core.1.0.112.0\build\net46\System.Data.SQLite.Core.targets" Condition="Exists('..\..\..\packages\System.Data.SQLite.Core.1.0.112.0\build\net46\System.Data.SQLite.Core.targets')" />
  <Import Project="..\..\..\packages\Microsoft.Web.WebView2.1.0.1264.42\build\Microsoft.Web.WebView2.targets" Condition="Exists('..\..\..\packages\Microsoft.Web.WebView2.1.0.1264.42\build\Microsoft.Web.WebView2.targets')" />
  <PropertyGroup>
    <GitIgnoreBranchVersion>true</GitIgnoreBranchVersion>
    <GitIgnoreTagVersion>true</GitIgnoreTagVersion>
    <GitInfoReportImportance Condition="'$(Configuration)' == 'Release'">high</GitInfoReportImportance>
  </PropertyGroup>
  <Import Project="..\..\..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets" Condition="Exists('..\..\..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" />
</Project>