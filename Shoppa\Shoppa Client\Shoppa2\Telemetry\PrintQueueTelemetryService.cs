using Shoppa.Objects.Common.Capsules;
using Shoppa.Objects.Common.Enumerations;
using Shoppa.PL.Windows.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace Shoppa.PL.Windows.Telemetry
{
    /// <summary>
    /// Centralized service for tracking print queue telemetry events.
    /// Handles both single template (hotfolder) and multiple template (batch) scenarios.
    /// </summary>
    public class PrintQueueTelemetryService
    {
        /// <summary>
        /// Tracks a single template print event (typically from hotfolder operations).
        /// </summary>
        /// <param name="eventName">The telemetry event name</param>
        /// <param name="templateName">Name of the template</param>
        /// <param name="templateGuid">Template GUID</param>
        /// <param name="copies">Number of copies</param>
        /// <param name="outputFormat">Output format information</param>
        public static void TrackSingleTemplateEvent(
            string eventName,
            string templateName,
            Guid? templateGuid,
            ushort copies,
            OutputFormat outputFormat)
        {
            try
            {
                var templateVersion = GetTemplateVersion(templateGuid);
                
                var templateWithFormat = new TemplateWithOutputFormat
                {
                    TemplateName = templateName,
                    TemplateId = templateGuid,
                    TemplateVersion = templateVersion,
                    OutputFormats = CreateTelemetryOutputFormat(copies, outputFormat)
                };

                var model = CreatePrintQueueModel(new List<TemplateWithOutputFormat> { templateWithFormat });
                TrackTelemetryEvent(eventName, model);
            }
            catch (Exception ex)
            {
                // Log error but don't throw - telemetry should not break application flow
                System.Diagnostics.Trace.WriteLine($"PrintQueueTelemetryService error: {ex.Message}");
            }
        }

        /// <summary>
        /// Tracks multiple templates print event (typically from batch operations).
        /// </summary>
        /// <param name="eventName">The telemetry event name</param>
        /// <param name="templates">List of templates to print</param>
        /// <param name="queueList">Queue information for the templates</param>
        public static void TrackMultipleTemplatesEvent(
            string eventName,
            IEnumerable<Template> templates,
            IReadOnlyList<EncapsulatedQueuedSign> queueList)
        {
            try
            {
                var templateVersions = GetTemplateVersions(queueList);
                
                var templatesWithOutputFormat = (from t in templates
                    join q in queueList on t.TemplateGuid equals q.TemplateGuid
                    let c = t.GetCurrentOutputFormat()?.OutputFormatCapsule
                    select new TemplateWithOutputFormat
                    {
                        TemplateName = q.Name,
                        TemplateId = t.TemplateGuid,
                        TemplateVersion = templateVersions.ContainsKey(t.TemplateGuid.GetValueOrDefault()) 
                            ? templateVersions[t.TemplateGuid.GetValueOrDefault()] : 0,
                        OutputFormats = new TelemetryPrintOutputFormat
                        {
                            NumberOfCopies = q.Copies,
                            Height = c?.OutputHeight ?? 0,
                            Width = c?.OutputWidth ?? 0,
                            Unit = c?.Unit.ToString() ?? string.Empty,
                            IsLabel = c?.IsLabel ?? false,
                            Name = c?.Name ?? "Unknown"
                        }
                    }).ToList();

                var model = CreatePrintQueueModel(templatesWithOutputFormat);
                TrackTelemetryEvent(eventName, model);
            }
            catch (Exception ex)
            {
                // Log error but don't throw - telemetry should not break application flow
                System.Diagnostics.Trace.WriteLine($"PrintQueueTelemetryService error: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the version of a single template.
        /// </summary>
        private static int GetTemplateVersion(Guid? templateGuid)
        {
            if (!templateGuid.HasValue) return 0;

            try
            {
                using (var templateDal = Embedded.Factory.Template)
                {
                    var signCapsule = (EncapsulatedTemplate)templateDal.GetItem(templateGuid.Value);
                    return signCapsule?.Version ?? 0;
                }
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Gets versions for multiple templates to avoid multiple DAL calls.
        /// </summary>
        private static Dictionary<Guid, int> GetTemplateVersions(IReadOnlyList<EncapsulatedQueuedSign> queueList)
        {
            var templateVersions = new Dictionary<Guid, int>();
            
            using (var templateDal = Embedded.Factory.Template)
            {
                foreach (var q in queueList)
                {
                    try
                    {
                        var signCapsule = (EncapsulatedTemplate)templateDal.GetItem(q.TemplateGuid);
                        templateVersions[q.TemplateGuid] = signCapsule?.Version ?? 0;
                    }
                    catch
                    {
                        templateVersions[q.TemplateGuid] = 0;
                    }
                }
            }
            
            return templateVersions;
        }

        /// <summary>
        /// Creates a TelemetryPrintOutputFormat from OutputFormat and copy count.
        /// </summary>
        private static TelemetryPrintOutputFormat CreateTelemetryOutputFormat(ushort copies, OutputFormat outputFormat)
        {
            var capsule = outputFormat?.OutputFormatCapsule;
            return new TelemetryPrintOutputFormat
            {
                NumberOfCopies = copies,
                Height = capsule?.OutputHeight ?? 0,
                Width = capsule?.OutputWidth ?? 0,
                Unit = capsule?.Unit.ToString() ?? string.Empty,
                IsLabel = capsule?.IsLabel ?? false,
                Name = capsule?.Name ?? "Unknown"
            };
        }

        /// <summary>
        /// Creates a PrintQueueModel with user and customer information.
        /// </summary>
        private static PrintQueueModel CreatePrintQueueModel(List<TemplateWithOutputFormat> templatesWithOutputFormat)
        {
            var hqId = AppData.Instance.CurrentUser.GetAvailableCustomers(CustomerType.MyOrganisation).Last();
            var customerId = AppData.Instance.CurrentUser.CustomerId;

            string customerName, hqName;
            using (var lcDAL = Embedded.Factory.Customer)
            {
                (customerName, hqName) = lcDAL.GetHqAndCustomerName(AppData.Instance.CurrentUser.UserName);
            }

            return new PrintQueueModel
            {
                HQName = !string.IsNullOrEmpty(customerName) ? customerName : "",
                HQId = hqId,
                CustomerId = customerId,
                CustomerName = !string.IsNullOrEmpty(hqName) ? hqName : "",
                UserName = AppData.Instance.CurrentUser.UserName,
                UserId = AppData.Instance.CurrentUser.Id,
                ShoppaClientVersion = Application.ProductVersion,
                TemplatesWithOutputFormats = templatesWithOutputFormat
            };
        }

        /// <summary>
        /// Tracks the telemetry event with dependency scope.
        /// </summary>
        private static void TrackTelemetryEvent(string eventName, PrintQueueModel model)
        {
            using (var op = AppTelemetry.Current.StartDependency(name: "PrintQueue", type: "Internal"))
            {
                AppTelemetry.Current.TrackEvent(
                    eventName,
                    new Dictionary<string, string> { ["PrintQueue"] = model.ToString() }
                );

                op.SetSuccess(true);
            }
        }
    }
}
