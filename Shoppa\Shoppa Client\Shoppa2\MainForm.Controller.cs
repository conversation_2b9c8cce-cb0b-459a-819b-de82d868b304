using Aga.Controls.Tree;
using Mediablob.Objects.Public;
using Shoppa.Controllers;
using Shoppa.Controllers.LocalPriceEngine;
using Shoppa.Controllers.Sync;
using Shoppa.Controllers.Validators;
using Shoppa.DataLayer.Embedded;
using Shoppa.DataLayer.Server;
using Shoppa.InputBox;
using Shoppa.Objects;
using Shoppa.Objects.Common;
using Shoppa.Objects.Common.Capsules;
using Shoppa.Objects.Common.Printshop;
using Shoppa.Objects.Extensions;
using Shoppa.Objects.Media;
using Shoppa.Objects.ProductSearch;
using Shoppa.Objects.TagObjects;
using Shoppa.Objects.TemplateObjects;
using Shoppa.PL.Windows.Controls;
using Shoppa.PL.Windows.Controls.Docking;
using Shoppa.PL.Windows.Controls.ProductSearch;
using Shoppa.PL.Windows.Controls.TitleBar;
using Shoppa.PL.Windows.Esl;
using Shoppa.PL.Windows.Printing;
using Shoppa.PL.Windows.Properties;
using Shoppa.PL.Windows.Telemetry;
using Shoppa.PrinterDriver;
using Shoppa.ProductCache.Legacy;
using ShoppaAB.Objects;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using TreeParts;

namespace Shoppa.PL.Windows.Forms
{
    partial class MainForm
    {
        protected static TraceSwitch traceSwitch = new TraceSwitch("Shoppa.PL.Windows.Forms", "");
        static readonly CapsuleType[] CAPSULE_TYPES_THAT_TRIGGER_TREE_REBUILD = new CapsuleType[] {
            CapsuleType.CampaignRecording,
            CapsuleType.CustomerGroup,
            CapsuleType.Folder,
            CapsuleType.OutputQueue,
            CapsuleType.Picture,
            CapsuleType.Template };

        #region Fields
        SyncManager _syncmanager;

        private LPEController _lpeController;
        LPEController lpeController
        {
            get
            {
                if (_lpeController == null)
                {
                    _lpeController = new LPEController(Settings.Default.LocalPriceEngineFolder, AppData.Instance);
                    if (_lpeController != null)
                    {
                        _lpeController.GotLPEFiles += new EventHandler<EventArgs>(_lpeController_GotLPEFiles);
                        _lpeController.LoadPlugins();
                    }
                }
                return _lpeController;
            }
        }

        private FilterUIState _productSearchStateFilter;
        private EslRenderJobProcessor _eslRenderJobProcessor;
        private Control lastFocusedControl;
        string _oneCopyText;
        string _manyCopiesText;
        bool _allowedQueuSync = false;
        bool _allowedMultiplePicImport = false;
        TreeIDEnumaration _previousTreeId = TreeIDEnumaration.Unknown;
        object productSearchThreadLock = new object();
        HotfolderWatcher hotfolderWatcher = null;
        IdleDetector idleSpy;
        /// <summary>
        /// Dictionary of picture guids to template guids which has current picture
        /// variable is initializa with sync start, and cleared after the all templates Updated
        /// </summary>
        Dictionary<Guid, List<Guid>> picGuidsToTemplatesToRegenerate = new Dictionary<Guid, List<Guid>>();
        object picGuidsToTemplatesToRegenerateLock = new object();

        Dictionary<Guid, List<EncapsulatedQueuedSign>> autoPrintSignsOfIntrestPerRecording;
        SyncData<Capsule> autoPrintRecordingSyncData;
        Dictionary<Guid, int> autoPrintCopiesPerDestinationGuid;

        private IProductCache _productCache;
        public IProductCache ProductCache => _productCache ?? (_productCache = ProductCacheFactory.Create());

        #endregion

        #region Initial Sync

        private delegate SyncResult BeginInitialSyncronizeDelegate(object value);

        public SyncResult DoInitialSync()
        {

            var action = new BeginInitialSyncronizeDelegate(_syncmanager.BeginInitialSyncronizeCallback);
            var result = action.BeginInvoke(SyncManager.DEFAULT_MAX_CACHE_AGE_MINUTES, null, null);
            while (!result.IsCompleted)
            {
                Application.DoEvents();
                System.Threading.Thread.Sleep(50);
            }
            return action.EndInvoke(result);
        }

        /// <summary>
        /// Occurs during initial syncronization to update the splash screen
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        /*
        void DoInitialSync_ProgressChanged(object sender, SyncProgressEventArgs e)
        {
            if (_splashBack != null)
            {
                loadingScreen.StepForvard();
                _splashBack.Message = string.Format("{0}: {1}", e.DataType, e.Message);
                _splashBack.UpdateProgress(e.ProgressPercentage);
            }
        }
        */

        #endregion Initial Sync

        public void InitializeMyValues()
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");

            _oneCopyText = MLS.Instance.TranslateText("MF_printerQueue_OneCopyText");
            _manyCopiesText = MLS.Instance.TranslateText("MF_printerQueue_ManyCopiesText");

            //First intitialize user rights by addint the controls to lists where editable, deletabel, and addable alloowed
            InitializeUserRights();
            InitializeDS();
            lock (_activeDocumentChangedSubscriptionLock)
            {
                dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
            }
            FontController.Instance.GoodMorning();
            UpdateQueueCountBoxes();

            // Setup hotfolder watcher
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LocalIntegration))
            {
                try
                {
                    hotfolderWatcher = new HotfolderWatcher(PosFilter.ExpandedHotFolder, ProductCache);
                }
                catch (Exception ex)
                {
                    MessageBoxes.HotfolderInitializationFailure(null);
                    Trace.WriteLineIf(traceSwitch.TraceError, $"Exception while trying to construct HotFolderWatch {ex.ToString()}", "E");
                }

                if (hotfolderWatcher != null)
                {
                    hotfolderWatcher.PrintingStarted += new EventHandler(hotfolderWatcher_PrintingStarted);
                    hotfolderWatcher.SyncronizeStoreInfoRequested += new EventHandler<EventArgs>(SyncronizeStoreInfoRequested);
                    hotfolderWatcher.QueuePrintSign += new EventHandler<PrintSignOrderEventArgs>(hotfolderWatcher_PrintSigns);
                    hotfolderWatcher.QueueFlowSign += HotfolderWatcher_SendFlowSign;
                    hotfolderWatcher.QueueProducts += new EventHandler<QueueProductsEventArgs>(hotfolderWatcher_QueueProducts);
                    hotfolderWatcher.ImportProducts += new EventHandler<ProductsEventArgs>(hotfolderWatcher_ImportProducts);
                    hotfolderWatcher.HotFolderUnavailable += new EventHandler(hotfolderWatcher_HotFolderUnavailable);
                    hotfolderWatcher.HotFolderResurfaced += new EventHandler(hotfolderWatcher_HotFolderResurfaced);
                    hotfolderWatcher.HotFolderPrintingPDF += hotfolderWatcher_HotFolderPrintingPDF;
                    hotfolderWatcher.Start();
                }
            }

            // Load units and convertion factors
            using (var localDal = Embedded.Factory.Unit)
            {
                foreach (EncapsulatedUnit capsule in localDal.GetAllUnits())
                {
                    ConversionUnit cu = new ConversionUnit(capsule.Name, capsule.BaseUnit, capsule.Factor);
                    UnitConvertor.AddUnit(cu);
                }
            }
        }

        private delegate string GetPDFSaveDelegate();

        void hotfolderWatcher_HotFolderPrintingPDF(object sender, Shoppa.PL.Windows.Printing.Print.PrintingPDFEventArgs e)
        {
            if (InvokeRequired)
            {
                var action = new GetPDFSaveDelegate(GetPDFSaveFile);
                e.SavePath = (string)Invoke(action);
            }
            else
                e.SavePath = GetPDFSaveFile();
        }


        public static string GetPDFSaveFile()
        {
            using (FileDialog fbd = new SaveFileDialog())
            {
                fbd.DefaultExt = "pdf";
                fbd.Filter = "PDF|*.pdf";
                fbd.FilterIndex = 0;

                var localFileExportPath = PrintshopController.GetLocalFileExportPath();
                if (!string.IsNullOrEmpty(localFileExportPath) && System.IO.Directory.Exists(localFileExportPath))
                    fbd.InitialDirectory = localFileExportPath;
                if (fbd.ShowDialog() == DialogResult.OK)
                {
                    PrintshopController.SetLocalFileExportPath(Path.GetDirectoryName(fbd.FileName));
                    return fbd.FileName;
                }
            }
            return "";
        }

        /// <summary>
        /// Enable or Disable buttons and menus based on TreeNode
        /// Access control for buttons on GUI  
        /// </summary>
        private void EnsureGuiVisibilityBasedOnNode(TreeViewAdv tree, TreeNodeAdv node)
        {
            // No tree selected, disable everything
            if (tree == null)
            {
                DisableTemplateRelatedMenuItems();

                buttonEditCampaign.Enabled = false;
                buttonRevertCampaignRecording.Enabled = false;
                return;
            }

            Messenger.Send(Messenger.Events.ActivateCampaignbuttons, CampaignModel.InEditMode);

            if (tree.Model is ModelBaseShoppa shoppaTreeModel)
            {
                var shoppaTree = tree as ShoppaTree2;
                if (node == null)
                    node = shoppaTree.GetRoot(TreeGroupInformation.GroupType.MyMaterial);

                if (!(node.Tag is CampaignNode))
                {
                    //Template tree node or nothing selected

                    ToggleTemplateAndCampaignButtons(CampaignModel.InEditMode);

                    // Nodes under MyMaterial
                    if (shoppaTreeModel.NodeIsEditable(node))
                    {
                        ChangeActiveStateForTemplateRelatedMenuItems(shoppaTree.TreeId, node, true);
                        buttonDeleteTemplate.Enabled = true;
                        buttonDeletePicture.Enabled = true;
                        UpdateModifyPictureButton(enabled: pictureSearch.PicturesImageListView.SelectedItems.Any());
                    }
                    // MyMaterial node itself.
                    else if (shoppaTreeModel.NodeIsInEditableTree(node))
                    {
                        ChangeActiveStateForTemplateRelatedMenuItems(shoppaTree.TreeId, node, true);
                        buttonDeleteTemplate.Enabled = false;
                        buttonDeletePicture.Enabled = false;
                        UpdateModifyPictureButton(enabled: pictureSearch.PicturesImageListView.SelectedItems.Any());
                    }
                    // Other Nodes: MyOrganisation, ExtraOrganisation, Supplier, Public and their subnodes
                    else
                    {
                        ChangeActiveStateForTemplateRelatedMenuItems(shoppaTree.TreeId, node, false);
                        buttonDeleteTemplate.Enabled = false;
                        buttonDeletePicture.Enabled = false;
                        UpdateModifyPictureButton(enabled: false);
                    }
                }
                else
                {
                    // Campaign tree selected
                    ToggleTemplateAndCampaignButtons(true);
                    DisableTemplateRelatedMenuItems();

                    outputBar.EnablePublishCampaignRecording = CampaignModel.InEditMode;
                }
            }
            else if (tree.Model is PrintModel || node.Tag is PrintNode)
            {
                ToggleTemplateAndCampaignButtons(true);
                DisableTemplateRelatedMenuItems();
            }
            else
            {
                throw new NotSupportedException("Unknown tree.Model");
            }
        }

        private void DisableTemplateRelatedMenuItems()
        {
            buttonAddTemplateFolder.Enabled = false;
            buttonAddPictureFolder.Enabled = false;
            buttonNewTemplate.Enabled = false;
            buttonAddPicture.Enabled = false;
            buttonDeleteTemplate.Enabled = false;
            buttonDeletePicture.Enabled = false;
            UpdateModifyPictureButton(enabled: false);

            titleBarStrip1.ChangeActiveState(MenuItemNames.saveAs, false);
            titleBarStrip1.ChangeActiveState(MenuItemNames.saveAsPicture, false);
            titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplate, false);
            titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplateLocally, false);
            titleBarStrip1.ChangeActiveState(MenuItemNames.selectPrinter, false);
        }

        private void ChangeActiveStateForTemplateRelatedMenuItems(TreeIDEnumaration treeId, TreeNodeAdv node, bool isInsideMyMaterial)
        {
            buttonAddTemplateFolder.Enabled = isInsideMyMaterial;
            buttonAddPictureFolder.Enabled = isInsideMyMaterial;
            buttonNewTemplate.Enabled = isInsideMyMaterial;
            buttonAddPicture.Enabled = isInsideMyMaterial;

            if (treeId == TreeIDEnumaration.Template)
            {
                if (node.IsLeaf)
                {
                    var canSaveAs = isInsideMyMaterial 
                        ? AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design) 
                        : IsSaveAsEnabledFor(node.Tag as ShoppaTreeNode);

                    titleBarStrip1.ChangeActiveState(MenuItemNames.saveAs, canSaveAs);
                    titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplate, AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LoginAsAnyCustomer));
                    titleBarStrip1.ChangeActiveState(MenuItemNames.saveAsPicture, AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.SaveAsPicture));
                    titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplateLocally, true);
                    titleBarStrip1.ChangeActiveState(MenuItemNames.selectPrinter, true);
                }
                else
                {
                    titleBarStrip1.ChangeActiveState(MenuItemNames.saveAs, false);
                    titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplate, false);
                    titleBarStrip1.ChangeActiveState(MenuItemNames.saveAsPicture, false);
                    titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplateLocally, false);
                    titleBarStrip1.ChangeActiveState(MenuItemNames.selectPrinter, false);
                }
            }
            else if (treeId == TreeIDEnumaration.Picture)
            {
                // Let both saveAs be same as last tree, only Template tree will set them enabled
                titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplate, false);
                titleBarStrip1.ChangeActiveState(MenuItemNames.exportTemplateLocally, false);
            }
        }

        private static bool IsSaveAsEnabledFor(ShoppaTreeNode shoppaTreeNode)
        {
            if (shoppaTreeNode == null)
                return false;

            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design))
                return false;

            switch (shoppaTreeNode.NodeGroup)
            {
                case TreeGroupInformation.GroupType.MyOrganisation:
                case TreeGroupInformation.GroupType.ExtraOrganisation:
                    return AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.AllowedCopyChainTemplates);

                case TreeGroupInformation.GroupType.Supplier:
                case TreeGroupInformation.GroupType.Public:
                    return true;

                default:
                    return false;
            }
        }

        private void DisableCampaignMode(object sender, EventArgs e)
        {
            ToggleCampaignRecordingEditMode();
        }

        private void ToggleTemplateAndCampaignButtons(bool showCampaignButtons)
        {
            bool isShoppaDesignUser = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design);
            bool isCampaignPublishUser = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PublishCampaignRecording);
            if (isShoppaDesignUser && isCampaignPublishUser)
            {
                buttonNewCampaign.Visible =
                buttonNewCampaignFolder.Visible =
                buttonEditCampaign.Visible =
                buttonDeleteCampaign.Visible =
                buttonRevertCampaignRecording.Visible = showCampaignButtons;

                buttonNewTemplate.Visible =
                buttonDeleteTemplate.Visible =
                buttonAddTemplateFolder.Visible = !showCampaignButtons;
            }
            else if (isShoppaDesignUser)
            {
                if (showCampaignButtons)
                {
                    buttonNewTemplate.Enabled =
                    buttonDeleteTemplate.Enabled =
                    buttonAddTemplateFolder.Enabled = false;
                }
            }
            else if (isCampaignPublishUser)
            {
                buttonNewCampaign.Enabled = showCampaignButtons;
                if (!showCampaignButtons)
                    DisableCampaignRecordingButtons();
            }
            else
            {
                buttonNewCampaign.Visible =
                buttonEditCampaign.Visible = 
                buttonNewTemplate.Visible =
                buttonNewCampaignFolder.Visible =
                buttonAddTemplateFolder.Visible = false;

                if (!showCampaignButtons)
                    DisableCampaignRecordingButtons();
            }
        }

        /// <summary>
        /// Translate GUI. 
        /// </summary>
        private void Translate()
        {
            // Translate the main window
            MLS.Instance.FillControlsText(this, toolTip);

            // Translate all child windows
            foreach (Form childForm in this.OwnedForms)
                MLS.Instance.FillControlsText(childForm, null);

            // Translate Title text: "Shoppa - {0} on {1}"
            updateTitleText();

            // Force empty text on previous translated tabs
            tabPageTemplates.Text = "";
            tabPagePictures.Text = "";
            tabPageDS.Text = "";
            tabPageOutputs.Text = "";

            titleBarStrip1.Translate();
            // Tabs tooltips (not working with FillControlsText apparently)
            tabPageTemplates.ToolTipText = MLS.Instance.TranslateText("MF_Tabs1_templates");
            tabPagePictures.ToolTipText = MLS.Instance.GetResourceBasedTranslation("MF_Tabs1_pictures", "MF_Tabs1_Media_Files");
            tabPageDS.ToolTipText = MLS.Instance.TranslateText("MF_Tabs1_flow");
            tabPageOutputs.ToolTipText = MLS.Instance.TranslateText("MF_Tabs1_printerqueue");
            
            this.toolTip.SetToolTip(this.buttonNewCampaign, MLS.Instance.TranslateText("MF_NewCampaign"));
            this.toolTip.SetToolTip(this.buttonEditCampaign, MLS.Instance.TranslateText("MF_EditCampaign"));
            this.toolTip.SetToolTip(this.buttonDeleteCampaign, MLS.Instance.TranslateText("MF_Delete"));
            this.toolTip.SetToolTip(this.buttonRevertCampaignRecording, "MF_RevertCampaign");

            this.toolTip.SetToolTip(this.buttonAddTemplateFolder, MLS.Instance.TranslateText("MF_NewFolder"));
            this.toolTip.SetToolTip(this.buttonNewTemplate, MLS.Instance.TranslateText("MF_NewTemplate"));
            this.toolTip.SetToolTip(this.buttonDeleteTemplate, MLS.Instance.TranslateText("MF_Delete"));

            pictureSearch.PictureSearchResult.Translate();
            string toolTipStr = MLS.Instance.GetResourceBasedTranslation("MF_newPictureDefaultName", "MF_New_Media_File");
            this.toolTip.SetToolTip(this.buttonAddPicture, toolTipStr);
            this.toolTip.SetToolTip(this.buttonAddPictureFolder, MLS.Instance.TranslateText("MF_NewFolder"));
            this.toolTip.SetToolTip(this.buttonDeletePicture, MLS.Instance.TranslateText("MF_Delete"));

            this.toolTip.SetToolTip(this.buttonFlowContainers, MLS.Instance.TranslateText("MF_ManageChannelsAndScreens"));
            this.toolTip.SetToolTip(this.buttonFlowThings, MLS.Instance.TranslateText("MF_FlowPlanner"));
            this.toolTip.SetToolTip(this.buttonDeletePlaylists, MLS.Instance.TranslateText("MF_Delete"));
            this.toolTip.SetToolTip(this.buttonDeleteOutput, MLS.Instance.TranslateText("MF_Delete"));

            // Dockers
            infoDocker?.Translate();
            purposes?.Translate();
            stack?.Translate();
            dsDocker?.Translate();
            tags?.Translate();
            dockerProductSearch?.Translate();
            searchAndReplaceDocker?.Translate();

            // Translate all open Design documents
            foreach (var content in dockingPanel.DockedContent)
                if (content is FormTemplateDesign)
                    (content as FormTemplateDesign).Translate();

            // Translate printbar tooltips
            outputBar.Translate();

            // Translate Trees
            templateSearch.Translate();
            pictureSearch.PictureTree.Translate();
            printTree.Translate();

            toolTip.SetToolTip(buttonEditCampaign, MLS.Instance.TranslateText("CRT_Edit"));
            toolTip.SetToolTip(buttonRevertCampaignRecording, MLS.Instance.TranslateText("CRT_Restore"));

            // Translate contextMenu (right click) for pictures alternatives
            pictureSearch.Translate();

            // Pass translation to element factory, which in turn passes it to GDI_Barcodes
            SetNewGdiElementFactory();
        }

        void navigateToContent(TreeIDEnumaration treeId, Guid guid)
        {
            if (treeId == TreeIDEnumaration.Picture)
            {
                if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
                    tabControlContent.SelectTab(tabPagePictures.Name);
                else
                    return; // if a printshopServer tabPagePictures will not be initialized. Cancel.

            }
            var path = TreeController2.GetPathToItem(treeId, guid);
            if (path == null || path.Count == 0)
                return;

            var tree = GetTreeFromTreeId(treeId);
            // Deselect previous
            var selectedNode = tree.SelectedNode;
            if (selectedNode != null)
                selectedNode.IsSelected = false;

            // Picture
            if (treeId == TreeIDEnumaration.Picture)
            {
                // Show correct folder
                tree.BrowseToContent(path);

                // Deselect any previous selections. This is only needed if navigating to same folder
                var selectedListItems = new List<Manina.Windows.Forms.ImageListViewItem>();
                for (int i = 0; i < pictureSearch.PicturesImageListView.SelectedItems.Count; i++)
                    selectedListItems.Add(pictureSearch.PicturesImageListView.SelectedItems[i]);

                foreach (var selected in selectedListItems)
                    selected.Selected = false;

                // Select picture
                foreach (var pic in pictureSearch.PicturesImageListView.Items)
                {
                    if (((Guid)pic.VirtualItemKey) == guid)
                    {
                        pictureSearch.PicturesImageListView.EnsureVisible(pic.Index);
                        pic.Selected = true;
                        break;
                    }
                }

                pictureSearch.PicturesImageListView.Focus();
            }
            // Template
            else if (treeId == TreeIDEnumaration.Template)
            {
                path.Add(guid); // Add own node last
                tree.BrowseToContent(path);
            }
        }

        void _lpeController_GotLPEFiles(object sender, EventArgs e)
        {
            _syncmanager.SyncronizeProducts();
            _syncmanager.SyncronizeStoreInfo();
        }

        void hotfolderWatcher_ImportProducts(object sender, ProductsEventArgs e)
        {
            using (var dal = Server.Factory.Product)
                dal.SaveProducts(AppData.Instance.CurrentUser, e.Products);

            if (e.Products.Count > 0)
                _syncmanager.SyncronizeProducts();
        }

        private void SyncronizeStoreInfoRequested(object sender, EventArgs e)
        {
            _syncmanager.SyncronizeStoreInfo();
        }

        private void hotfolderWatcher_PrintSigns(object sender, PrintSignOrderEventArgs e)
        {
            var queueController = new QueueController(AppData.Instance);

            // Application insights event - using centralized service
            PrintQueueTelemetryService.TrackSingleTemplateEvent(
                "ShoppaDesktop_SignOrder_PrintQueue",
                e.Name,
                e.Sign.TemplateGuid,
                e.Copies,
                e.Sign.OutputFormat
            );

            var queuedSign = queueController.AddToQueue(e.Copies, e.Sign, e.Name, QueuedOutputStateEnumaration.PrinterArchive, e.Timestamp);
            printTree.AddSignsToArchive(queuedSign);
            if (hotfolderPrintingStarted)
            {
                //Move to the printer queue after the first print to display how the printer queue is being filled
                hotfolderPrintingStarted = false;
                displayPrinterArchive();
            }
            else
            {
                focusPrinterArchive();
            }
        }

        private void HotfolderWatcher_SendFlowSign(object sender, FlowSignOrderEventArgs e)
        {
            var splashMessage = MLS.Instance.TranslateText("MFD_SplashSendingToFlow");
            using (var splash = new ShoppaAB.Forms.AsyncSplash(splashMessage, Resources.logo_splash))
            {
                splash.TranslationPleaseWait = MLS.Instance.TranslateText("MF_SplashPleaseWait");
                splash.StartPosition = FormStartPosition.CenterParent;
                foreach (var kvp in e.PlaylistsOfPlayers)
                    SendFlowSignFromSignOrder(kvp.Key, kvp.Value, splash);
            };
        }

        /// <summary>
        /// Switches the left tab to the printer archive folder
        /// </summary>
        private void displayPrinterArchive()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(displayPrinterArchive));
            }
            else
            {
                EnsureTreeVisibility(TreeIDEnumaration.PrinterQueue);
                printTree.Focus();
                printTree.Refresh();
            }
        }

        /// <summary>
        /// Focus printTree to allow refresh
        /// </summary>
        private void focusPrinterArchive()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(focusPrinterArchive));
            }
            else
            {
                printTree.Focus();
                printTree.Refresh();
            }
        }

        bool hotfolderPrintingStarted = false;
        void hotfolderWatcher_PrintingStarted(object sender, EventArgs e)
        {
            hotfolderPrintingStarted = true;
        }

        private void hotfolderWatcher_QueueProducts(object sender, QueueProductsEventArgs e)
        {
            Messenger.Send(Messenger.Events.ProductsQueueChanged, e.FocusOnNewest);
        }

        private void hotfolderWatcher_HotFolderUnavailable(object sender, EventArgs e)
        {
            Messenger.Send(Messenger.Events.ProductsQueueUnavailable);
        }

        private void hotfolderWatcher_HotFolderResurfaced(object sender, EventArgs e)
        {
            Messenger.Send(Messenger.Events.ProductsQueueResurfaced);
        }

        #region USER rights
        private void InitializeUserRights()
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");

            // DESIGN
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design))
            {
                FontController.Instance.StartLoadInstalledFonts();
                if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PublishCampaignRecording))
                {
                    buttonNewCampaign.Visible =
                    buttonEditCampaign.Visible =
                    buttonDeleteCampaign.Visible =
                    buttonNewCampaignFolder.Visible =
                    buttonRevertCampaignRecording.Visible = false;
                }
            }
            else
            {
                titleBarStrip1.ChangeVisibility(MenuItemNames.recoverMaterial, false);

                panelTemplateManagementButtons.Visible = 
                    AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PublishCampaignRecording) ||
                    CampaignRecordingController.HasExpandedCampaignRecordings();

                if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.MediaEditor))
                {
                    HidePictureManagementButtons();
                }

                titleBarStrip1.ChangeActiveState(MenuItemNames.showBindings, false);
            }
            // allowed Multiple Picture Import
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.AllowedMultiplePictureImport))
            {
                _allowedMultiplePicImport = true;
            }

            // Only show Manage Addresses for Customers that has children in the Customer Tree.
            var hasCustomers = CustomerController.GetCustomerIds(AppData.Instance.CurrentUser).Count > 1;
            titleBarStrip1.ChangeVisibility(MenuItemNames.manageMetaValues, hasCustomers || AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ESL));
            titleBarStrip1.ChangeVisibility(MenuItemNames.manageCustomerGroups, hasCustomers || true);

            bool hasFlow = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Flow);
            bool canCreatePlaylist = hasFlow
                || AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.DSSaveToFile)
                || AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.SocialMedia);
            outputBar.CanCreatePlaylist = canCreatePlaylist;

            if (!hasFlow)
                tabControlContent.Controls.Remove(tabPageDS);

            // Allways allowed to add
            outputBar.AddEnabled = true;

            // Can user order from printshop
            outputBar.HasOrder = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopClient);

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
            {
                buttonDeleteOutput.Visible = false;
                outputBar.Visible = false;
                queueTemplateLayoutSizes.Visible = false;
                titleBarStrip1.SetMenuItemText(MenuItemNames.recoverMaterial, "Recover Orders");
                titleBarStrip1.ChangeVisibility(MenuItemNames.recoverMaterial, true);
                titleBarStrip1.ChangeActiveState(MenuItemNames.recoverMaterial, true);
                titleBarStrip1.ChangeVisibility(MenuItemNames.saveAs, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.saveAsPicture, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.exportTemplate, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.showBindings, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.manageAddresses, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.manageCustomerGroups, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.manageMetaValues, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.previewPlaySettings, false);
            }

            titleBarStrip1.ChangeVisibility(MenuItemNames.autoSignage, AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ESL) || AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.AutoPrinting));
            titleBarStrip1.ChangeVisibility(MenuItemNames.saveToFileSettings, AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.DSSaveToFile));

            // Only show menuItem for users with TemplateExport. Enable/Disable is done in EnsureGuiVisibilityBasedOnNode
            titleBarStrip1.ChangeVisibility(MenuItemNames.exportTemplate, AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LoginAsAnyCustomer));

            titleBarStrip1.ChangeVisibility(MenuItemNames.printClientStatus, AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Luna) && Settings.Default.PrintClientStatus);

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ShoppaOkey))
            {
                titleBarStrip1.ChangeVisibility(MenuItemNames.recoverMaterial, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.manageAddresses, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.manageAddressLists, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.exportTemplate, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.showBindings, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.bindPrinters, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.autoSignage, false);
            }
            else
            {
                titleBarStrip1.ChangeVisibility(MenuItemNames.exportTemplateLocally, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.importTemplateLocally, false);                
            }

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.OEM_OKI))
                titleBarStrip1.ChangeVisibility(MenuItemNames.managePrinters, false);
            else
                titleBarStrip1.ChangeVisibility(MenuItemNames.selectPrinter, false);

            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.StoreGroups)
                || AppData.Instance.CurrentUser.GetAvailableCustomers(CustomerType.MyOrganisation).Count != 1)
            {
                titleBarStrip1.ChangeVisibility(MenuItemNames.filterStoreGroups, false);
            }

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ShoppaOkey))
            {
                titleBarStrip1.ChangeVisibility(MenuItemNames.printshop, false);
                titleBarStrip1.ChangeVisibility(MenuItemNames.sync, false);
            }

            titleBarStrip1.ChangeVisibility(MenuItemNames.saveAsPicture, AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.SaveAsPicture));

            var currentUserIsInDesignRole = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design);
            titleBarStrip1.ChangeVisibility(MenuItemNames.saveAs, currentUserIsInDesignRole);
            titleBarStrip1.ChangeVisibility(MenuItemNames.showBindings, currentUserIsInDesignRole);
            titleBarStrip1.ChangeVisibility(MenuItemNames.previewPlaySettings, currentUserIsInDesignRole && AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Flow));

            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.FlowPlanner))
            {
                buttonFlowThings.Enabled =
                buttonFlowThings.Visible = false;
            }

            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.StoreGroups))
                titleBarStrip1.ChangeVisibility(MenuItemNames.manageCustomerGroups, false);           
        }
        #endregion

        private void HidePictureManagementButtons()
        {
            panelpictureManagementButtons.Visible = false;
            panelpictureManagementButtons.Width = 0;
            panelpictureManagementButtons.Height = 0;
        }

        private void initializeOpenConnectionProblemHandler()
        {
            Embedded.Factory.HandleOpenConnectionProblem += _embedded_HandleOpenConnectionProblem;
        }

        #region SYNCHRONIZATION EVENTS MANAGEMENT

        /// <summary>
        /// Initialize the Events for synchronization management
        /// </summary>
        private void InitializeSyncManager()
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            _syncmanager = new SyncManager(AppData.Instance);
            _syncmanager.Syncronized += new EventHandler<SyncronizedEventArgs>(_syncmanager_Syncronized);
            _syncmanager.Syncronizing += new System.ComponentModel.CancelEventHandler(_syncmanager_Syncronizing);
            _syncmanager.SyncronizeFailing += new EventHandler<SyncronizeFailingEventArgs>(_syncmanager_SyncronizeFailing);
            _syncmanager.SyncronizeFailed += new EventHandler<SyncronizeFailedEventArgs>(_syncmanager_SyncronizeFailed);
            _syncmanager.DownloadedItem += new EventHandler<CancelCapsuleEventArgs>(_syncmanager_DownloadedItem);
            _syncmanager.SavedItem += new EventHandler<CancelCapsuleEventArgs>(_syncmanager_SavedItem);            
            _syncmanager.Progressed += _syncmanager_Progressed;
        }
                
        void updateLoadingScreen(string message, string submessage)
        {
            try
            {
                if (loadingScreen != null && !loadingScreen.IsDisposed && loadingScreen.IsHandleCreated)
                {
                    if (loadingScreen.InvokeRequired)
                        loadingScreen.Invoke(new Action<string, string>(updateLoadingScreen), message, submessage);
                    else
                    {
                        if (submessage == "")
                            loadingScreen.StepForward(message);
                        else
                            loadingScreen.SetMessage(message + ", " + submessage);
                    }
                }
            }
            // Race condition with multiple threads can cause ObjectDisposedException for _splashBack inside Invoke
            // Just ignore it as splash is done and not required any more
            catch (ObjectDisposedException) { }
        }

        private void _syncmanager_Progressed(object sender, SyncProgressEventArgs e)
        {
            updateLoadingScreen(e.DataType, e.Message);
        }

        [System.Runtime.ExceptionServices.HandleProcessCorruptedStateExceptionsAttribute]
        void _syncmanager_DownloadedItem(object sender, CancelCapsuleEventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "SyncManager Downloaded Item", "V");
            try
            {
                var availabilityController = new AvailabilityController(AppData.Instance);

                SetCancelByWhiteOrBlacklist((sender as SyncManager).whiteBlackList, e);

                // Decide whether or not this user has access to the capsule right now.
                // TODO: If cancelling, don't do all the unneccessary work below?
                if (!availabilityController.ItemIsAvailableNowOrInFuture(e.item))
                    e.Cancel = true;

                if (e.Cancel)
                    return;

                // Create thumbnail for material to be used by list viue
                if (e.item is EncapsulatedFontUsage encapsulatedFont && encapsulatedFont.Data != null)
                {
                    FontController.Instance.AddMemoryFontToCollection(encapsulatedFont.Data, encapsulatedFont.FontHash);
                    //TODO Validate each family as a font is added instead of next row
                    //FontController.Instance.Validate();
                }
                else if (e.item is EncapsulatedTemplate && e.item.DataChanged)
                {
                    bool alreadyOpen, openInDesign;
                    FormTemplateBase baseForm;
                    TryGetOpenTemplate(e.item.Guid, out baseForm, out alreadyOpen, out openInDesign);
                    // Don't clear open template from cache, may cause problems for our dear customers if we do!
                    var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);
                    if (!alreadyOpen)
                    {
                        templateController.RemoveFromTemplateCash(e.item.Guid);
                        var resources = TemplateController.GetTemplateResources(e.item as EncapsulatedTemplate, out List<string> usedMetaValues);
                        if (resources.Count > 0)
                        {
                            ((EncapsulatedTemplate)e.item).UsedPictures = resources.First(xx => xx.Key == CapsuleType.Picture).Value;
                            ((EncapsulatedTemplate)e.item).UsedColors = resources.First(xx => xx.Key == CapsuleType.Colors).Value;
                            ((EncapsulatedTemplate)e.item).UsedMetaValues = usedMetaValues;
                        }
                    }
                    else
                        templateController.DeferTemplateRemoval(e.item.Guid);
                }
                else if (e.item is EncapsulatedQueuedSign sign)
                {
                    if (sign.HasState(QueuedOutputStateEnumaration.PrinterArchive)
                        || sign.HasState(QueuedOutputStateEnumaration.PrintshopArchive))
                    {
                        //TODO: Also ignore Displayed signs?
                        e.Cancel = true;
                    }
                    else if (sign.HasState(QueuedOutputStateEnumaration.PrintshopOrder)
                        || sign.HasState(QueuedOutputStateEnumaration.PrintshopPrinted))
                    {
                        PrintshopController.ValidateSignSizes(sign);
                    }
                    else if (sign.DataChanged)
                    {
                        //Clear thumbnail to force a regeneration if needed later
                        ((FolderStructureCapsule)e.item).Thumb = null;
                    }
                }
                else if (e.item is EncapsulatedTag tag)
                {                    
                    TagController.AddUpdateTag(tag);
                }
                else if (e.item is EncapsulatedPicture pictureCapsule)
                {
                    AddOnsEnumeration[] flowAddOns = { AddOnsEnumeration.Flow, AddOnsEnumeration.FlowDesign };
                    if (pictureCapsule.IsVideo
                        && !AppData.Instance.CurrentUser.IsInAnyRole(flowAddOns))
                    {
                        e.Cancel = true;
                    }
                    else if (e.item.DataChanged)
                    {
                        lock (picGuidsToTemplatesToRegenerateLock)
                        {
                            if (!picGuidsToTemplatesToRegenerate.ContainsKey(e.item.Guid))
                            {
                                List<Guid> templateGuids;
                                using (var dal = Embedded.Factory.Template)
                                    templateGuids = dal.GetTemplatesWithSpecificPicture(e.item.Guid);

                                if (templateGuids.Count() > 0)
                                    picGuidsToTemplatesToRegenerate.Add(e.item.Guid, templateGuids);
                            }
                        }
                    }

                    var pictureController = new PictureController(AppData.Instance, PictureThumbnailCacheInstance.Instance);
                    pictureController.RemoveFromThumbnailCache(e.item.Guid);
                }
                else if (e.item is EncapsulatedFolder remoteFolder)
                {
                    //DO NOT DELETE THIS CODE 
                    //remove the Printerqueue or Order queue folders which does not belong to the current user
                    if ((remoteFolder.TreeID == (ushort)TreeIDEnumaration.PrinterQueue
                        || remoteFolder.TreeID == (ushort)TreeIDEnumaration.PrintshopCustomer)
                        && remoteFolder.UserID != AppData.Instance.CurrentUser.Id && remoteFolder.FolderGuid != Guid.Empty)
                    {
                        //Skip this object without saving it below
                        e.Cancel = true;
                    }
                }
                else if (e.item is EncapsulatedUnit unitCap)
                {
                    ConversionUnit cu = new ConversionUnit(unitCap.Name, unitCap.BaseUnit, unitCap.Factor);
                    UnitConvertor.AddUnit(cu);
                }
                else if (e.item is EncapsulatedCampaignRecording campaignRecording)
                {
                    CampaignRecordingController.UpdateChangedQueueSignsToChanged(campaignRecording);
                }
            }
            catch (System.Exception ex)
            {
                Trace.WriteLine(string.Format("An unhandled exception occurred while processing '{0}' [{1}]!: {2}", e.item, e.item.Guid, ex), "E");
                e.Cancel = true;
            }
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "SyncManager Downloaded Item Done", "V");
        }
        
        void _syncmanager_SavedItem(object sender, CancelCapsuleEventArgs e)
        {
            // as the capsule is now saved, it is now ok to remove any data we no longer need
            // to save meomory

            if (e.item.Data != null)
                e.item.Data = new byte[0];

            if (e.item is EncapsulatedPicture)
                (e.item as EncapsulatedPicture).Thumb = new byte[0];
        }

        void _syncmanager_SyncronizeFailing(object sender, SyncronizeFailingEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<object, SyncronizeFailingEventArgs>(_syncmanager_SyncronizeFailing), new[] { sender, e });
            }
            else
            {
                Trace.WriteLineIf(traceSwitch.TraceVerbose, "SyncManager Failing", "V");
                
                if (e.Reason == FailureReason.ValidationGuid)
                {
                    using (LoginForm loginForm = new LoginForm())
                    {
                        if (HasMainFormGUI) //Stop syncmanager during relogin if running
                            _syncmanager.Stop();

                        loginForm.OpenIdLoginRequired = CustomerController.GetCurrentLoginMethodFromRegistry() == LoginMethod.OpenID;
                        var result = loginForm.ShowDialog();

                        e.Cancel = (result == DialogResult.OK);
                        if (result == DialogResult.Cancel)
                        {
                            e.Reason = FailureReason.UserCancelled;
                            _syncmanager.Stop();
                        }
                        if (HasMainFormGUI)
                        {
                            //Restore sync after successful relogin if running
                            if (result == DialogResult.OK)
                                _syncmanager.Start();
                        }
                    }
                }
            }
        }

        void _syncmanager_SyncronizeFailed(object sender, SyncronizeFailedEventArgs e)
        {
            if (_formIsClosing)
                return;

            if (this.InvokeRequired)
            {
                System.Threading.Thread.Sleep(1300);
                this.Invoke(new Action<object, SyncronizeFailedEventArgs>(_syncmanager_SyncronizeFailed), new[] { sender, e });
            }
            else
            {
                Trace.WriteLineIf(traceSwitch.TraceInfo, "Sync Failed", "I");
                titleBarStrip1?.UpdateSyncStatus(false);
                //Handle some specific failures
                switch (e.Reason)
                {
                    case FailureReason.UserCancelled:
                        this.Tag = FailureReason.UserCancelled;
                        this.Close();
                        return;
                    case FailureReason.ValidationGuid:
                        throw new InvalidOperationException("ValidationGuid is expected to be handled in _syncmanager_SyncronizeFailing.");
                    case FailureReason.RestartRequired:
                        DialogResult res = CMessageBox.Show(
                            MLS.Instance.TranslateText("MF_restartRequired"),
                            MLS.Instance.TranslateText("MF_restartRequiredHeader"),
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                        if (res == DialogResult.Yes)
                        {
                            this.Tag = FailureReason.RestartRequired;
                            this.Close();
                        }
                        return;
                    case FailureReason.UserLockUnavailable:
                        CMessageBox.Show(
                        MLS.Instance.TranslateText("MF_userUnavailable"),
                        MLS.Instance.TranslateText("MF_userUnavailableHeader"),
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                        this.Tag = FailureReason.UserLockUnavailable;
                        this.Close();
                        return;
                    case FailureReason.OutOfMemory:
                        new FormFriendlyError(
                            MLS.Instance.TranslateText("MF_OutOfMemory"),
                            MLS.Instance.TranslateText("MF_OutOfMemoryHeader"),
                            Resources.S3_Starterror_RAM).ShowDialog();
                        this.Tag = null;
                        this.Close();
                        return;
                    case FailureReason.RemoteDatabase:
                        
                        if (!ProductCache.IsBuilt)
                        {
                            var metaController = new MetaController(AppData.Instance);
                            ProductCache.Rebuild(metaController.GetProductSearchFieldIds().ToHashSet());
                        }
                        
                        _syncmanager.SetOfflineSince();
                        titleBarStrip1?.StartSyncButtonOfflineTimer();
                        break;
                    case FailureReason.LocalPriceEnginePath:
                        new FormFriendlyError(
                            MLS.Instance.TranslateTextNoDB("MF_InvalidLPEPath"),
                            MLS.Instance.TranslateTextNoDB("MF_InvalidLPEPathHeader"),
                            Resources.S3_Starterror_Folder).ShowDialog();
                        this.Tag = FailureReason.LocalPriceEnginePath;
                        this.Close();
                        return;
                    default:
                        break;
                }
                //Test if the failure is too severe to let the user continue
                if (DateTime.Now < _syncmanager.SyncRequestTimestamp)
                {
                    //The user has a clock that is turned back in time
                    new FormFriendlyError(
                        MLS.Instance.TranslateText("MF_detectedTimeChange2"),
                        MLS.Instance.TranslateText("MF_detectedTimeChangeHeader"),
                        Resources.S3_Starterror_Timechanged).ShowDialog();

                    this.Tag = FailureReason.CyncTimeIncorect;
                    this.Close();
                    return;
                }
                else if (e.Reason != FailureReason.RemoteDatabase &&
                    DateTime.Now > _syncmanager.SyncSuccessTimestamp.AddDays(SyncManager.SYNC_BROKEN_GRACE_PERIOD))
                {
                    bool whouldBenefitFromProgramUpdate = CustomerController.CouldUpdateProgram();

                    new FormFriendlyError(
                        string.Format((whouldBenefitFromProgramUpdate ? MLS.Instance.TranslateText("MF_successfulSyncRequired2Update") : MLS.Instance.TranslateText("MF_successfulSyncRequired2")), SyncManager.SYNC_BROKEN_GRACE_PERIOD),
                        MLS.Instance.TranslateText("MF_successfulSyncRequiredHeader2"),
                        Resources.S3_Starterror_Support).ShowDialog();

                    this.Tag = FailureReason.CyncBrokenToLong;
                    this.Close();
                    return;
                }
                else if (DateTime.Now > _syncmanager.GetOfflineSince().AddDays(SyncManager.SYNC_REQUIRED_GRACE_PERIOD))
                {
                    new FormFriendlyError(
                        string.Format(MLS.Instance.TranslateText("MF_onlineRequired"), SyncManager.SYNC_REQUIRED_GRACE_PERIOD),
                        MLS.Instance.TranslateText("MF_onlineRequiredHeader"),
                        Resources.S3_Starterror_Connect).ShowDialog();
                    this.Tag = FailureReason.CyncToOld;
                    this.Close();
                    return;
                }                
            }
        }

        void _syncmanager_Syncronizing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceInfo, "Sync Starting", "I");
            titleBarStrip1?.Synchronizing();
        }


        void _embedded_HandleOpenConnectionProblem(object sender, ErrorEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.invoke(new Action<object, ErrorEventArgs>(_embedded_HandleOpenConnectionProblem), new[] { sender, e });
            }
            else
            {
                MessageBoxes.UnableToContinueAfterFailedOpenConnection();
                Application.Exit();
            }
        }


        void _syncmanager_Syncronized(object sender, SyncronizedEventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceInfo, "Sync Finished", "I");
            updateLoadingScreen("Synchronization Complete", "");
            titleBarStrip1?.UpdateSyncStatus(true);

            // Flush telemetry data that was buffered during offline mode
            try
            {
                Program.FlushTelemetry(100); // Use shorter delay for sync flush
            }
            catch (Exception ex)
            {
                Trace.WriteLineIf(traceSwitch.TraceError, $"Failed to flush telemetry on sync: {ex.Message}", "E");
            }

            var availabilityController = new AvailabilityController(AppData.Instance);
            var storeInfoController = new StoreInfoController(AppData.Instance);
            var metaController = new MetaController(AppData.Instance);
            var campaignRecordingController = new CampaignRecordingController(AppData.Instance);
            var productController = new ProductController(AppData.Instance);
            var outputController = new OutputController(AppData.Instance);

            // A list of changes to reflect in the tree after a sync
            Dictionary<TreeIDEnumaration, List<ItemChangeInfo>> changesToReflect = new Dictionary<TreeIDEnumaration, List<ItemChangeInfo>>();
            // Update availability of content (Folder, Template, Pictures)
            // Add any changes to e.items for GUI updates with tree_ChangeTreeAfterSync

            bool storeInfoUpdatedInSync = false;
            bool packagesAreOk = true;
            try
            {
                StoreInfoController.CheckPackageConsitency();
            }
            catch
            {
                packagesAreOk = false;
                // töm storeInfo och packages
                // sätt om synchnycklar
                StoreInfoController.PurgeStoreinfoAfterInconsistency();
            }

            if (packagesAreOk)
            {
                availabilityController.PerformAvailabilityUpdates(e);

                Trace.WriteLineIf(traceSwitch.TraceVerbose, "AvailabilityController.PerformAvailabilityUpdates Done", "V");

                // remove empty list to get better performance
                for (int i = e.items.Count - 1; i >= 0; i--)
                    if (e.items[i].Value.Items.Count == 0)
                        e.items.RemoveAt(i);

                storeInfoUpdatedInSync = e.items.Exists(kvp => kvp.Key == CapsuleType.StoreInfoPackage);
                if (storeInfoUpdatedInSync)
                {
                    storeInfoController.UpdateAvailabilities();
                }
            }
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "StoreInfoController.UpdateAvailabilities Done", "V");

            bool storeInfoUpdatedInLocalPriceEngine = false;
            bool hasLocalPriceEngine = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LocalPriceEngine);
            if (hasLocalPriceEngine && lpeController.HasPlugins && !lpeController.HasWorkInProgress)
            {
                // At this point the products and store info from the server is downloaded, saved, and expanded.
                // All information needed should be present
                var status = lpeController.ProcessPendingFiles(storeInfoUpdatedInSync, !HasMainFormGUI);

                // Virtual products have change, rebuild product cache
                if (status.RequireProductCacheReBuild)
                    ProductCache.Rebuild(metaController.GetProductSearchFieldIds().ToHashSet());

                // do yet ANOTHER expand if storeinfo is updated in local price engine.
                if (status.UpdatedStoreInfos.Count > 0)
                {
                    storeInfoUpdatedInLocalPriceEngine = true;
                    storeInfoController.UpdateAvailabilities();
                }

                if (status.UpdatedPricelist)
                    storeInfoUpdatedInLocalPriceEngine = true;

                lpeController.CleanDirectories();
                Trace.WriteLineIf(traceSwitch.TraceVerbose, "LPE processing Done", "V");
            }

            //Make sure all storeInfo packages are correctly expanded

            if (storeInfoUpdatedInSync || storeInfoUpdatedInLocalPriceEngine)
            {
                List<EncapsulatedStoreInfo> storeInfos;
                using (var siDal = Embedded.Factory.StoreInfo)
                    storeInfos = siDal.GetPackages(true);

                Messenger.Send(Messenger.Events.StoreInfosChanged, storeInfos);

                var affectedCampaignRecordingIds = campaignRecordingController.UpdateAvailabilitiesAfterStoreInfo();

                if (HasMainFormGUI && affectedCampaignRecordingIds.Any())
                    UpdateNodeContentOfCamapignRecording(affectedCampaignRecordingIds);

                Trace.WriteLineIf(traceSwitch.TraceVerbose, "StoreInfo update DONE", "V");
            }

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.FilterProductsByStoreInfo))
            {
                if (storeInfoUpdatedInSync || e.GotProducts)
                {
                    Trace.WriteLineIf(traceSwitch.TraceVerbose, "Removing products without storeinfo references", "V");
                    e.GotProducts |= productController.AssertAllProductsHaveStoreInfoReferences();
                }
            }
            
            if (e.GotProducts || !ProductCache.IsBuilt)
            {
                ProductCache.Rebuild(metaController.GetProductSearchFieldIds().ToHashSet());
            }
            
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "ProductCache.Build Done", "V");

            if (HasMainFormGUI)
            {
                SendChangedProductsToDestinations();
            }

            // There is nothing more to uppdate if we only synced storeinfo.
            if (e.OnlyStoreInfo)
                return;

            AvailabilityController.FillUpChanges(e, changesToReflect);
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "AvailabilityController.FillUpChanges Done", "V");

            // remove any empty lists added by fillUpChanges. 
            for (int i = e.items.Count - 1; i >= 0; i--)
                if (e.items[i].Value.Items.Count == 0)
                    e.items.RemoveAt(i);

            bool firstSync = SyncManager.InitialSyncRequired;

            if (!firstSync && HasMainFormGUI)
            {
                dockerProductSearch?.EvaluateAutomaticSearchAndUpdateGui();
                dockerProductSearch?.EvaluateMultiModeSearchUpdateGui();
            }
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "EvaluateAutomaticSearchAndUpdateGui Done", "V");

            #region CampaignRecording

            bool campaignRecordingUpdatedInSync = e.items.Count > 0 && e.items.Exists(kvp => kvp.Key == CapsuleType.CampaignRecording);
            if (campaignRecordingUpdatedInSync)
            {
                Dictionary<Guid, List<EncapsulatedQueuedSign>> signsOfIntrestPerRecording = null;
                var changedSignsDict = campaignRecordingController.SaveNewChangedSignsToOutputQueue();

                var unexpandedIds = new List<int>();
                var activeChanged = new List<EncapsulatedCampaignRecording>();
                signsOfIntrestPerRecording = campaignRecordingController.UpdateAvailabilities(out unexpandedIds, out activeChanged);
                var deletedSigns = new Dictionary<Guid, List<EncapsulatedQueuedSign>>();

                using (var crDal = Embedded.Factory.CampaignRecording)
                foreach (var changedActive in activeChanged)
                {
                    // Need to invert the value as we fetched this from the database where it is wrong!!
                    changedActive.IsActive = !changedActive.IsActive;
                    crDal.SaveItem(changedActive, false);
                }

                // Clear changes for items that have been deleted
                using (var cmDal = Embedded.Factory.ChangedMaterial)
                {
                    foreach (var kvp in changedSignsDict)
                    {
                        Guid recordingGuid = CampaignRecordingController.GetGuidFromId(kvp.Key).Value;
                        deletedSigns.Add(recordingGuid, kvp.Value.Where(s => s.IsDeleted).ToList());
                        foreach (var sign in kvp.Value.Where(s => s.IsDeleted))
                            cmDal.ClearChange(sign.Guid);

                        var queue = QueueController.GetOutputQueue(QueuedOutputStateEnumaration.Campaign, kvp.Key).ConvertAll(s => s.Guid);
                        if (!queue.Any(g => cmDal.IsChangedItem(g)))
                            cmDal.ClearChange(recordingGuid);
                    }

                    var recordingSyncData = e.items.First(k => k.Key == CapsuleType.CampaignRecording).Value;
                    if (recordingSyncData.Items.Where(x => x.IsDeleted).Count() > 0)
                    {
                        foreach (var item in recordingSyncData.Items.Where(x => x.IsDeleted))
                            cmDal.ClearChange(item.Guid);
                    }
                }


                if (!firstSync)
                {
                    // Rather than making use of signsOfIntrestPerRecording, check if the changedSignsDict contains any signs that are actually in the database
                    CampaignRecordingController.MergeChangeDictionaries(changedSignsDict, signsOfIntrestPerRecording);
                    using (var cmDal = Embedded.Factory.ChangedMaterial)
                    {
                        foreach (var kvp in changedSignsDict)
                        {
                            Guid recordingGuid = CampaignRecordingController.GetGuidFromId(kvp.Key).Value;
                            foreach (var changedSign in kvp.Value)
                            {
                                // Add changes to ChangedMaterial
                                if (!changedSign.IsDeleted && changedSign.State == QueuedOutputStateEnumaration.Campaign)
                                {
                                    cmDal.SetChanged(CapsuleType.OutputQueue, changedSign.Guid, DateTime.Now);
                                    if (!cmDal.IsChangedItem(recordingGuid))
                                        cmDal.SetChanged(CapsuleType.CampaignRecording, recordingGuid, DateTime.Now);
                                }
                            }
                        }
                    }
                }

                // Automatisk utskrift
                if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.AutoPrintCampaignRecording))
                {
                    // Merge the information from updated and new recordings
                    // so both can be autoprinted.                
                    CampaignRecordingController.MergeChangeDictionaries(changedSignsDict, signsOfIntrestPerRecording, true);

                    // Kolla efter kampanjer som just satts som autoprint
                    using (var settingsDal = Embedded.Factory.Setting)
                    {
                        var recordingSyncData = e.items.First(k => k.Key == CapsuleType.CampaignRecording).Value;
                        foreach (EncapsulatedCampaignRecording ecr in recordingSyncData.Items)
                        {
                            if (ecr.AutoPrint)
                            {
                                string settings_key = "ap_" + ecr.Guid.ToString();
                                bool? wasJustSat = settingsDal.GetBoolean(settings_key);
                                settingsDal.RemoveValue(settings_key);

                                if (wasJustSat.HasValue)
                                {
                                    int recordingId = CampaignRecordingController.GetIdFromGuid(ecr.Guid);
                                    var allSigns = QueueController.GetOutputQueue(QueuedOutputStateEnumaration.Campaign, recordingId);

                                    if (!signsOfIntrestPerRecording.ContainsKey(ecr.Guid))
                                        signsOfIntrestPerRecording[ecr.Guid] = allSigns;
                                    else
                                    {
                                        allSigns.RemoveAll(s => signsOfIntrestPerRecording[ecr.Guid].Exists(s2 => s2.Guid == s.Guid));
                                        signsOfIntrestPerRecording[ecr.Guid].AddRange(allSigns);
                                    }
                                }
                            }
                        }
                    }

                    if (signsOfIntrestPerRecording != null && !firstSync)
                    {
                        var recordingSyncData = e.items.First(k => k.Key == CapsuleType.CampaignRecording).Value;

                        List<EncapsulatedQueuedSign> allIntrestingSigns = new List<EncapsulatedQueuedSign>();
                        foreach (var kvp in signsOfIntrestPerRecording)
                        {
                            EncapsulatedCampaignRecording rec = recordingSyncData.Items.Find(c => c.Guid == kvp.Key) as EncapsulatedCampaignRecording;

                            // Plocka med skyltarna som ska skrivas ut
                            // Sorterabord raderade eller dolda skyltar
                            if (rec.AutoPrint && AppData.Instance.CurrentUser.CustomerId != rec.CustomerID)
                            {
                                campaignRecordingController.KeepOnlyIfSelectedFormatIsCompatible(kvp.Value);
                                allIntrestingSigns.AddRange(kvp.Value.Where(si => !si.IsDeleted && si.State != QueuedOutputStateEnumaration.CampaignTempoRare));
                            }
                        }

                        // Hitta antalet utskrifter per format
                        Dictionary<Guid, int> copiesPerFormatGuid = new Dictionary<Guid, int>();
                        foreach (var sign in allIntrestingSigns)
                        {
                            if (!copiesPerFormatGuid.ContainsKey(sign.OutputFormatQueued))
                                copiesPerFormatGuid[sign.OutputFormatQueued] = 0;

                            copiesPerFormatGuid[sign.OutputFormatQueued] += sign.Copies;
                        }

                        // Hitta antalet utskrifter per skrivare
                        var formatToDestinationDictionary = outputController.GetFormatDestinationBindings(copiesPerFormatGuid.Keys.ToList());
                        Dictionary<Guid, int> copiesPerDestinationGuid = new Dictionary<Guid, int>();

                        foreach (var kvp in copiesPerFormatGuid)
                        {
                            Guid destination = Guid.Empty;
                            if (formatToDestinationDictionary.ContainsKey(kvp.Key))
                                destination = formatToDestinationDictionary[kvp.Key];

                            if (!copiesPerDestinationGuid.ContainsKey(destination))
                                copiesPerDestinationGuid[destination] = 0;

                            copiesPerDestinationGuid[destination] += kvp.Value;
                        }

                        if (allIntrestingSigns.Count > 0)
                        {
                            autoPrintSignsOfIntrestPerRecording = signsOfIntrestPerRecording;
                            autoPrintRecordingSyncData = recordingSyncData;
                            autoPrintCopiesPerDestinationGuid = copiesPerDestinationGuid;

                            AutoPrintIfApplicable();
                        }
                    }
                }
            }

            #endregion

            Trace.WriteLineIf(traceSwitch.TraceVerbose, "CampaignRecording Done", "V");
            
            // Refresh tree with new capsule changes

            #region Printshop Automatic printing
            //Print packing slips and signs for received printshop orders
            if (Properties.Settings.Default.PrintshopAutoPrint)
            {
                var printshopSyncData = e.items.Find(kvp => kvp.Key == CapsuleType.PrintshopOrder).Value;
                if (printshopSyncData != null && printshopSyncData.Items.Count > 0)
                {
                    bool restartSync = false;
                    foreach (EncapsulatedPrintshopOrder order in printshopSyncData.Items)
                    {
                        //Don't process changed printshop orders where we not the printshop (we are customer)
                        if (order.PrintshopId != AppData.Instance.CurrentUser.CustomerId)
                            continue;
                        //Print all pending signs
                        //TODO: Do we need to check the status for this too?
                        List<EncapsulatedQueuedSign> orderSigns;
                        using (var queueDal = Embedded.Factory.Printshop)
                            orderSigns = queueDal.GetSigns((int)order.Id);

                        if (PrintshopController.VerifyHealthyPrintshopOrder(order, orderSigns))
                        {
                            var printedSigns = PrintshopPrinting.Print(orderSigns, false);
                            QueueController.UpdateSignState(printedSigns.ConvertAll(eqs => eqs.Guid), QueuedOutputStateEnumaration.Printed);
                            if (printTree != null)
                                printTree.MoveNotPrintedToPrinted(printedSigns);

                            //Print the packing slip if it hasn't been already
                            //This is done after printsign to ensure all missing resources are downloaded.
                            if (!order.HasStatus(OrderStatus.PrintedPackingSlip))
                            {
                                printPackingSlip((int)order.Id);
                                restartSync = true;
                            }
                        }
                        else
                        {
                            MessageBox.Show(string.Format("Skipping automatic print of Printshop Order {0}. It is corrupted", order.Id));
                        }
                    }
                    if (restartSync)
                        _syncmanager.BeginSynchronize();
                }
            }
            #endregion Printshop Automatic printing

            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Printshop automatic printing Done", "V");

            // Re-check printers
            bool gotFinalDestination = e.items.Count > 0 && e.items.Exists(kvp => kvp.Key == CapsuleType.FinalDestination);
            if (gotFinalDestination)
            {
                var printerSettingsController = new PrinterSettingsController(AppData.Instance);
                printerSettingsController.FilterPrinters();
            }

            Trace.WriteLineIf(traceSwitch.TraceVerbose, "PrinterSettingsController.FilterPrinters Done", "V");

            #region Tags
            bool gotTags = false;
            List<Guid> depricatedTags = new List<Guid>();
            foreach (var kvp in e.items)
            {
                if (kvp.Key == CapsuleType.Tags)
                {
                    gotTags = true;
                    foreach (var item in kvp.Value.Items.FindAll(c => c.IsDeleted))
                        depricatedTags.Add(item.Guid);
                }
            }

            if (gotTags)
            {
                if (depricatedTags.Count > 0)
                {
                    TagController.RemoveTags(depricatedTags);
                }
                if (tags != null)
                    tags.UpdateTagList();
            }
            #endregion Tags

            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Tags Done", "V");

            // Clear saved thumbnails of changed templates to force them to be remade.
            if (TemplateController.TemplateHasThumbs())
            {
                var syncData = e.items.Find(kvp => kvp.Key == CapsuleType.Picture).Value;
                if (syncData != null)
                {
                    var itemsToRemake = syncData.Items.FindAll(c => (c.IsDeleted || c.DataChanged));
                    foreach (var item in itemsToRemake)
                        TemplateController.ClearTemplateThumbsContainingPicture(item.Guid);
                }

                syncData = e.items.Find(kvp => kvp.Key == CapsuleType.Colors).Value;
                if (syncData != null)
                {
                    var itemsToRemake = syncData.Items.FindAll(c => (!c.IsDeleted));
                    TemplateController.ClearTemplateThumbsContainingColor(itemsToRemake.Select(c => c.Guid).ToList());
                }


                syncData = e.items.Find(kvp => kvp.Key == CapsuleType.MetaValue).Value;
                if (syncData != null)
                {
                    var itemsToRemake = syncData.Items.FindAll(c => (!c.IsDeleted));
                    RefreshMetaProductOnOpenTemplates();
                    TemplateController.ClearTemplateThumbsContainingMetaValue(itemsToRemake.Select(c => (c as EncapsulatedMetaValue).FieldIdentifier).ToList());
                }

                Trace.WriteLineIf(traceSwitch.TraceVerbose, "ClearTemplateThumbsContainingPicture Done", "V");
            }

            // Any open templates need to be updated if they contains pictures that has changed during this sync
            if (HasMainFormGUI)
            {
                lock (picGuidsToTemplatesToRegenerateLock)
                {
                    foreach (KeyValuePair<Guid, List<Guid>> picGuidToTemplateGuids in picGuidsToTemplatesToRegenerate)
                        RefreshPictureOnTemplates(picGuidToTemplateGuids.Value, picGuidToTemplateGuids.Key);
                }

                if (e.items.Exists(kvp => kvp.Key == CapsuleType.Colors))
                    new TemplateController(AppData.Instance, TemplateCacheInstance.Instance).ReplaceColorInCashedTemplates(e.items.First(kvp => kvp.Key == CapsuleType.Colors).Value.Items.Select(capsule => capsule as EncapsulatedColor));


                if (e.items.Exists(kvp => CAPSULE_TYPES_THAT_TRIGGER_TREE_REBUILD.Contains(kvp.Key)))
                    Messenger.Send(this, Messenger.Events.ReloadTrees);

                UpdatePrintTreeAfterSync(e.items);
                foreach (var key in changesToReflect.Keys)
                {
                    var tree = GetTreeFromTreeId(key);
                    foreach (var changeInfo in changesToReflect[key])
                        tree.AddContent(changeInfo);
                }

                // Refresh content view for templates if visible
                if (dockingPanel.ActiveContent != null
                    && (!(dockingPanel.ActiveContent is FormTemplateBase)
                    && !(dockingPanel.ActiveContent is FormSignProduction)))
                {
                    refreshTemplateContentView();
                }
            }

            // Clear changes for next sync
            lock (picGuidsToTemplatesToRegenerateLock)
                picGuidsToTemplatesToRegenerate.Clear();

            if (HasMainFormGUI)
            {
                //Update the queuecount boxes in case the queue has changed on the server (not very common though)
                this.invoke(new Action(UpdateQueueCountBoxes));

                if(e.items.Exists(kvp => kvp.Key == CapsuleType.FontUsage))
                    this.invoke(new Action(UpdateFontPicker));

                #region DisplayGroups
                if (e.items.Exists(kvp => kvp.Key == CapsuleType.DisplayGroup))
                {
                    if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LocalIntegration))
                    {
                        var hotfolderController = new HotfolderController(AppData.Instance);
                        hotfolderController.WriteDisplayGroupsXmlFile(PosFilter.ExpandedHotFolder);
                    }
                }
                #endregion

                if (e.items.Exists(kvp => kvp.Key == CapsuleType.FlowPlaylist))
                {
                    this.invoke(new Action(ReloadPlaylists));
                }

                //Refresh ProductPlate
                dockerProductSearch.LoadFriendlyPlate();

                storeInfoController.TryUpdateAvailabilities();
                TryRunTerminator();
            }

            if (HasMainFormGUI)
            {
                if(e.items.Exists(kvp => kvp.Key == CapsuleType.Purpose))
                {
                    this.invoke(new Action(ReloadPurposes)); 
                }
            }

            if (HasMainFormGUI)
            {
                if (e.items.Exists(kvp => kvp.Key == CapsuleType.ProductGroup))
                {
                    Messenger.Send(Messenger.Events.ProductGroupsChanged);
                }
            }
            _ = UploadPrinterStatisticsAsync();
        }

        public void AutoPrintIfApplicable()
        {
            if (!HasMainFormGUI)
                return;

            if (autoPrintSignsOfIntrestPerRecording == null
                || autoPrintRecordingSyncData == null
                || autoPrintCopiesPerDestinationGuid == null)
                return;

            ShowFormAndAutoPrint(autoPrintSignsOfIntrestPerRecording, autoPrintRecordingSyncData, autoPrintCopiesPerDestinationGuid);
            autoPrintSignsOfIntrestPerRecording = null;
            autoPrintRecordingSyncData = null;
            autoPrintCopiesPerDestinationGuid = null;
        }

        private void ShowFormAndAutoPrint(Dictionary<Guid, List<EncapsulatedQueuedSign>> signsOfIntrestPerRecording, SyncData<Capsule> recordingSyncData, Dictionary<Guid, int> copiesPerDestinationGuid)
        {
            using (FormAutomaticPrintingNotification fapn = new FormAutomaticPrintingNotification())
            {
                fapn.SetDestinationsAndCopies(copiesPerDestinationGuid);
                fapn.BringToFront();
                fapn.TopMost = true;
                DialogResult autoResult = fapn.ShowDialog();

                var queueController = new QueueController(AppData.Instance);
                var storeInfoController = new StoreInfoController(AppData.Instance);

                if (autoResult == System.Windows.Forms.DialogResult.OK)
                {
                    foreach (var kvp in signsOfIntrestPerRecording)
                    {
                        Guid recordingGuid = kvp.Key;

                        EncapsulatedCampaignRecording rec = recordingSyncData.Items.Find(c => c.Guid == recordingGuid) as EncapsulatedCampaignRecording;

                        if (rec.AutoPrint && AppData.Instance.CurrentUser.CustomerId != rec.CustomerID)
                        {
                            List<EncapsulatedQueuedSign> changedSigns = kvp.Value.Where(s => !s.IsDeleted).ToList();

                            QueueController.HidePrintQueue();

                            foreach (var sign in changedSigns)
                            {
                                var template = queueController.GetTemplate(sign.Guid);

                                var products = template.Products.GetProductInstances();
                                var usedCampaignGuids = StoreInfoController.GetPackagesForRecording(recordingGuid).ConvertAll(si => si.Guid);
                                storeInfoController.UpdateProductPrices(products, DateTime.Today, usedCampaignGuids, false);

                                queueController.AddToQueue(sign.Copies, template, "AutoPrintedCampaign", QueuedOutputStateEnumaration.PrinterQueue);
                            }

                            printQueue(QueuedOutputStateEnumaration.PrinterQueue, true);
                            QueueController.UnHidePrintQueue();
                        }
                    }
                }
            }
        }

        private async Task UploadPrinterStatisticsAsync()
        {
            var cts = new CancellationTokenSource(30000);

            var driverTasks = PrinterDriverFactory.GetInstance(AppData.Instance).FindAllAsTasks(cts.Token);

            var statisticsTasks = driverTasks
                .Select(t => GatherStatisticsAsync(t, cts.Token))
                .ToArray();

            try
            {
                await Task.WhenAll(statisticsTasks);

                foreach (var task in statisticsTasks)
                {
                    if (task.Result != null)
                    {
                        //This will upload the statistics to HappyHappy, from which statistics is gathered
                        var serializedStatistics = Convert.ToBase64String(task.Result.Serialize());
                        Trace.WriteLine(serializedStatistics, "P");//task.Result.ToJson()
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.WriteLineIf(traceSwitch.TraceError, $"Failed to gather statistics for printers: {ex.Message}", "E");
            }
        }

        private async Task<PrinterStatistics> GatherStatisticsAsync(Task<IPrinterDriver> driverTask, CancellationToken ct)
        {
            var driver = await driverTask;
            if (driver == null)
                return null;

            return await driver.GatherStatisticsAsync(ct);
        }
        
        #endregion
        public void ReloadPurposes()
        {
            var activeForm = GetActiveFormTemplateBase();
            if (activeForm is FormTemplateDesign ftd)
            {
                var template = ftd.GetTemplate();
                purposes.SetTemplateAndFillPurposes(template);
            }
        }

        
        private void SetCancelByWhiteOrBlacklist(WhiteLister whiteBlackList, CancelCapsuleEventArgs e)
        {
            if (whiteBlackList != null)
            {
                if (whiteBlackList.GotWhitelist(e.capsuleType))
                    e.Cancel = !whiteBlackList.IsWhitelisted(e.capsuleType, e.item.Guid);
                else if (whiteBlackList.GotBlacklist(e.capsuleType))
                    e.Cancel = whiteBlackList.IsBlacklisted(e.capsuleType, e.item.Guid);
            }
        }


        private void SendChangedProductsToDestinations()
        {
            System.Threading.Interlocked.Exchange(ref _eslRenderJobProcessor.ConsecutiveFailedCount, 0);
            Action sendToDestinationAction = OnSendChangedProductsToDestinationsCalled;
            sendToDestinationAction.BeginInvoke(null, null);
        }

        private void OnSendChangedProductsToDestinationsCalled()
        {
            var printingRequiredProducts = ProductController.GetPrintingRequiredProducts(AppData.Instance.CurrentUser);
            if (printingRequiredProducts.Any())
            {
                _autoSignagePrintingProcessor.AddJobs(printingRequiredProducts);
            }

            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ESL)) return;

            var renderingRequiredProducts = ProductController.GetEslRenderingRequiredProducts(AppData.Instance.CurrentUser);
            if (renderingRequiredProducts.Any())
            {
                _eslRenderJobProcessor.AddJobs(renderingRequiredProducts);
            }
        }

        private void refreshTemplateContentView()
        {
            if (InvokeRequired)
                Invoke(new Action(refreshTemplateContentView));
            else
            {
                if (tabControlContent.SelectedTab == tabPageTemplates)
                {
                    // Only refresh content view if we are not currently renaming the selected node
                    if (currentTree == templateSearch.TemplateTree || currentTree == templateSearch.CampaignTree)
                    {
                        var currentTreeShoppa = currentTree as ShoppaTree2;
                        if (!currentTreeShoppa.NodeIsEditing)
                            reselectCurrentTreeNode(currentTreeShoppa);
                    }
                }
            }
        }

        private static void TryRunTerminator()
        {
            if (!Properties.Settings.Default.DisableTerminator)
            {
                var intervalsInDays = Properties.Settings.Default.TerminatorRunIntervalInDays;
                var terminatorDaysAgo = Properties.Settings.Default.TerminatorDaysAgo;
                new StoreInfoController(AppData.Instance).TryRunStoreInfoTerminator(intervalsInDays, terminatorDaysAgo);
            }
        }

        /// <summary>
        /// Mainform is ready and exists and initial sync is done (no splashBack exist)
        /// </summary>
        public bool HasMainFormGUI
        {
            get { return !this.IsDisposed && this.IsHandleCreated && loadingScreen == null; }
        }

        void _syncmanager_Stopped(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceInfo, "Sync Stopped", "I");
        }

        void invoke(Action method)
        {
            try
            {
                if (!this.IsDisposed && this.IsHandleCreated)
                    this.Invoke(method);
            }
            catch (Exception ex)
            {
                if (method != null && method.Method != null)
                    Trace.WriteLineIf(traceSwitch.TraceWarning, "Sync invoke failed for " + method.Method.Name + "\n" + ex.ToString(), "W");
                else
                    Trace.WriteLineIf(traceSwitch.TraceWarning, ex.ToString(), "W");
            }
        }

        object invoke(Delegate method, object[] args)
        {
            try
            {
                if (!this.IsDisposed && this.IsHandleCreated)
                    return this.Invoke(method, args);
            }
            catch (Exception ex)
            {
                if (method != null && method.Method != null)
                    Trace.WriteLineIf(traceSwitch.TraceWarning, "Sync invoke failed for " + method.Method.Name + "\n" + ex.ToString(), "W");
                else
                    Trace.WriteLineIf(traceSwitch.TraceWarning, ex.ToString(), "W");
            }

            return null;
        }

        

        /// <summary>
        /// Print a packing slip from the GUI thread
        /// </summary>
        /// <param name="orderId"></param>
        void printPackingSlip(int orderId)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<int>(printPackingSlip), orderId);
            }
            else
            {
                Shoppa.PL.Windows.Printing.PrintshopDocuments pd = new Shoppa.PL.Windows.Printing.PrintshopDocuments();
                pd.PrintPackingSlip(orderId);
            }
        }

        #region GUI EVENT LISTENERS

        /// <summary>
        /// Save lastFocusedControl to know which control to affect on button press
        /// </summary>
        void control_Leave(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            lastFocusedControl = sender as Control;
        }

        void dockingPanel_ActiveContentChanged(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "dockPanel_ActiveDocumentChanged Entering", "V");
            // Prevent cascading of GUI changes while disposing. 
            if (this.Disposing || this.IsDisposed)
                return;

            TreeIDEnumaration treeId = TreeIDEnumaration.Unknown;

            if (dockingPanel.ActiveContent.GetControlOrForm() is FormTemplateBase)
            {
                var form = dockingPanel.ActiveContent.GetControlOrForm() as FormTemplateBase;
                var template = form.GetTemplate();
                bool isInDesign = false;
                if (template != null)
                {
                    isInDesign = form is FormTemplateDesign;
                    if (isInDesign)
                        currentTemplateLayoutSizes.SetThumbSizeForComingTemplate((form as FormTemplateDesign).WantedThumbSize);
                    currentTemplateLayoutSizes.SetFormats(template, isInDesign);
                }

                if (form.GetTemplateCapsule() != null)
                {
                    treeId = form.TreeIDEnumaration;
                    if (treeId == TreeIDEnumaration.PrinterQueue ||
                        treeId == TreeIDEnumaration.PrintshopServer)
                    {
                        var tree = printTree as PrintTree;
                        tree.BrowseToContent(form.GetTemplateCapsule().Guid);
                    }
                    else if (treeId != TreeIDEnumaration.Unknown && treeId != TreeIDEnumaration.Flow)
                    {
                        var tree = GetTreeFromTreeId(treeId);
                        if (treeId == TreeIDEnumaration.CampaignRecording)
                        {
                            tree.BrowseToCampaignSign(form.GetTemplateCapsule().Guid);
                        }
                        else
                        {
                            tree.BrowseToContent(form.GetTemplateCapsule().Guid);
                        }
                    }

                    // if tree id is unknown and ActiveDocument is FormTemplateBase it
                    // Should mean we're viewing a flow sequence template. if so do
                    // nothing with tree.
                    if (isInDesign)
                    {
                        var formTemplateDesign = form as FormTemplateDesign;
                        var ribbonUiDockerState = MapDesignDockerStateToRibbonUiStateDesignMode(formTemplateDesign.DockerState);                       
                        workModeSwitched(WorkMode.Design, ribbonUiDockerState);
                    }
                    else
                        workModeSwitched(WorkMode.Production);

                    toggleCanQueue(true);
                }
            }
            else if (dockingPanel.ActiveContent == browseFoldersAndTemplatesDock)
            {
                treeId = templatesImageListView.TreeId;
                if (treeId == TreeIDEnumaration.PrinterQueue 
                    || treeId == TreeIDEnumaration.PrintshopServer
                    || treeId == TreeIDEnumaration.CampaignRecording)
                {
                    // PrinterQueue do not switch between template and folder as no Edit-mode exist. 
                    // Last selected should still be selected when returning
                }
                else
                {
                    var tree = GetTreeFromTreeId(treeId);
                    tree.BrowseToFolder(templatesImageListView.FolderGuid);
                    toggleCanQueue(true);
                }

                workModeSwitched(WorkMode.Production);
            }
            else if (currentWorkMode == WorkMode.Design)
                workModeSwitched(WorkMode.Production);
            EnsureTreeVisibility(treeId);
        }

        void designChildWindow_FormClosed(object sender, FormClosedEventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "designChildWindow_FormClosed Entering", "V");
            //When design template is closed navigate to the 
            FormTemplateDesign designChildWindow = (FormTemplateDesign)sender;
            EncapsulatedTemplate templateCapsule = designChildWindow.GetTemplateCapsule();

            new TemplateController(AppData.Instance, TemplateCacheInstance.Instance).RemoveFromTemplateCash(templateCapsule.Guid);
            infoDocker.UnloadInformation();

            //dispose the form so the expanding of the template goest correctly
            //otherwise the expanding function can think that template is open in design
            designChildWindow.Dispose();

            // Open template in SignProductionSurface by selecting the node in tree.
            templateSearch.TemplateTree.SelectedNode = templateSearch.TemplateTree.AllNodes.Where(n => n.Tag is ShoppaTreeNode && (n.Tag as ShoppaTreeNode).ContentGuids.Contains(templateCapsule.Guid)).First();
        }

        void designChildWindow_BeginBrowseToPicture(object sender, BrowseToPictureEventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            NavigateToPicture(e.PictureGuid);
            pictureSearch.PicturesImageListView.Focus();
        }


        void formTemplateBase_SelectBrowseToPicture(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            pictureSearch.PicturesImageListView.Focus();
        }

        /// <summary>
        /// User changed or selected open Template
        /// </summary>
        void formTemplateBase_InformationChangedByUser(object sender, InformationChangedByUserEventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            OutputBarMode = OutputBarQueueModes.Template;

            if (sender is FormSignProduction formSignProduction)
            {
                var template = formSignProduction.GetTemplate();
                if (formSignProduction.ShowingFlowQueueTemplate && signChildWindow.SaveOnExit)
                {
                    var capsuleGuid = formSignProduction.GetTemplateCapsule().Guid;
                    regenerateFlowThumb(template, capsuleGuid);
                }
                else
                {
                    var sizesToUpdate = formSignProduction.ShowingPrinterQueueTemplate ? queueTemplateLayoutSizes : currentTemplateLayoutSizes;
                    if (e.Reason == InformationChangedReason.TemplateReverted)
                        sizesToUpdate.SetCurrentTemplate(template);
                    sizesToUpdate.RerenderThumbnailsForCurrentTemplate();
                }
            }
            else if (sender is FormTemplateDesign)
            {
                currentTemplateLayoutSizes.RerenderThumbnailsForCurrentTemplate();
            }
        }

        /// <summary>
        /// Clicked on Template to Deselect image and exit Image browsing
        /// </summary>
        void signChildWindow_EndBrowseToPicture(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if ((sender as FormTemplateBase) == signChildWindow)
                EnsureTreeVisibility(signChildWindow.TreeIDEnumaration);
            else
                EnsureTreeVisibility(TreeIDEnumaration.Template);   // Will have been sent here from designChildWindow which is always from TemplateTree
        }

        void signChildWindow_SurfaceClicked(object sender, EventArgs e)
        {
            if ((sender as FormTemplateBase) == signChildWindow)
                EnsureTreeVisibility(signChildWindow.TreeIDEnumaration);
            else
                EnsureTreeVisibility(TreeIDEnumaration.Template);   // Will have been sent here from designChildWindow which is always from TemplateTree
        }

        /// <summary>
        /// The design window has signalled that a new product search should be performed.
        /// Starts a new background thread for product retrieval.
        /// </summary>
        /// <remarks>If a previous product search is still running that thread is aborted.</remarks>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void designChildWindow_BeginProductSearch(object sender, ProductSearchEventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            try
            {
                if (!e.key.isChanged && e.key.Value != "")
                {
                    //Force a change flag of the product field that instigated the search
                    string value = e.key.Value;
                    e.key.Value = "";
                    e.key.AcceptChanges();
                    e.key.Value = value;
                }
                dockerProductSearch.SearchProductWithoutCampaignAndProductGroupFiters(e.product);

                if (dockerProductSearch.CurrentSearchProvider.TotalHits == 0)
                {
                    //Redo the search with only the last enterered field if no results were found at all
                    Product p = new Product();
                    if ((e.key.Type & Data.DataType.Identity) == Data.DataType.Identity)
                        p.AddCode(e.key);
                    else
                        p.AddTextField(e.key);
                    dockerProductSearch.SearchProductWithoutCampaignAndProductGroupFiters(p);
                }
                if (dockerProductSearch.CurrentSearchProvider.TotalHits > 0)
                    _syncmanager.SyncronizeStoreInfo();

                if (dockerProductSearch.CurrentSearchProvider.TotalHits == 1 && dockingPanel.ActiveContent is FormTemplateBase)
                {
                    var product = dockerProductSearch.CurrentSearchProvider.GetProduct(0);
                    FormTemplateBase activeForm = (FormTemplateBase)dockingPanel.ActiveContent;
                    activeForm.StopEditBeforeCommit();
                    activeForm.SetProduct(product, e.productIndex, 0, dockerProductSearch.CurrentSearchProvider.Filter);
                    if (activeForm is FormSignProduction)
                        (activeForm as FormSignProduction).RefreshTemplateInfo();
                    else if (activeForm is FormTemplateDesign)
                        infoDocker.UpdateDockerInfoFromProduct(activeForm.GetTemplate().Products);
                    if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.BarcodeQueueMode))
                    {
                        AddToQueue(QueuedOutputStateEnumaration.PrinterQueue, 1);
                        UpdateQueueCountBoxes();
                        activeForm.Focus();
                    }
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                Trace.WriteLineIf(traceSwitch.TraceInfo, "The Thread was aborted.", "I");
            }
            catch (Exception ex)
            {
                Trace.WriteLineIf(traceSwitch.TraceError, ex.ToString(), "E");
            }
        }

        void designChildWindow_Saved(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            _syncmanager.BeginSynchronize();
        }

        void designChildWindow_UndoRedo(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if (!(dockingPanel.ActiveContent is FormTemplateDesign)) return;
            var ftd = dockingPanel.ActiveContent as FormTemplateDesign;
            Template template = ftd.GetTemplate();
            currentTemplateLayoutSizes.SetFormats(template, true);
            infoDocker.LoadInformation(template.Products.Clone() as TemplateProducts, template.TemplateInformation, ftd.ProductIndexIsVisible, ftd.LanguageIndexIsVisible);
            infoDocker.SetMediaTypes(template.OutputFormat.RecomendedMediaTypes);
        }

        void SetOutputFormats(object sender, EventArgs e)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            FormTemplateBase formTB = (FormTemplateBase)sender;
            if (formTB.Visible)
            {
                //  Template template = formTB.GetTemplate();
                // templateSizes1.SetFormats(template.OutputFormats, template.GetCurrentOutputFormat());
            }
            else
            {
                currentTemplateLayoutSizes.ClearControls();
                queueTemplateLayoutSizes.ClearControls();
            }

        }

        #endregion GUI EVENT LISTENERS METHODS

        #region NewTree related
        /// <summary>
        /// Add a New Template, including GUI, tree update and sync
        /// </summary>
        /// <param name="treeNode"></param>
        public void AddNewTemplate()
        {
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);

            // Ask for formats
            using (Add_Edit_OutputFormat aeof = new Add_Edit_OutputFormat())
            {
                DialogResult result = aeof.ShowDialog();
                if (result != DialogResult.OK || aeof.SelectedFormats.Count == 0)
                    return;

                var tree = GetTreeFromTreeId(TreeIDEnumaration.Template);
                var node = tree.SelectedNode;
                // If no node is selected default to root for own material
                if (node == null)
                    node = tree.GetRoot(TreeGroupInformation.GroupType.MyMaterial);
                // Remove selection
                node.IsSelected = false;

                // Selected node is not a folder, select parent as the folder for new template
                if (node.IsLeaf)
                    node = node.Parent;

                // Make sure it is a folder
                var folderTreeNode = node.Tag as ShoppaTreeNode;
                if (folderTreeNode == null || !folderTreeNode.IsFolder)
                    throw new ApplicationException("Missing Folder Node");

                // Ensure permission
                if (!new Permission.Folder(AppData.Instance).CanAddOrRemoveContent(folderTreeNode.ContentGuid, TreeIDEnumaration.Template))
                    throw new ApplicationException("Permission denied");

                // Create new Template and add it to tree and database
                var folderGuid = folderTreeNode.ContentGuid;
                var templateName = getNextName(node, "TREE_NewName_Template", false);
                if (string.IsNullOrEmpty(templateName))
                    templateName = "New Template";
                var templateFormats = aeof.NewFormats;

                var encapsulatedTemplate = templateController.CreateNewTemplate(
                    folderGuid,
                    templateName,
                    templateFormats);
                
                if (templateController.Add(encapsulatedTemplate, tree))
                {
                    forceUpdateAfterBrowsing = true;

                    // Browse to new node in tree and Open in Template Design
                    OpenTemplateInDesign(encapsulatedTemplate.Guid);

                    List<Guid> path = TreeController2.GetPathToItem(TreeIDEnumaration.Template, encapsulatedTemplate.Guid);
                    path.Add(encapsulatedTemplate.Guid); // Add own node last
                    var newNode = tree.BrowseToContent(path);
                    (newNode.Tag as ShoppaTreeNode).IsNewNode = true;

                    templateSearch.SetScrollToSelectedItem();

                    // Synchronize the new template
                    //_syncmanager.BeginSynchronize();

                    // Start renaming of template
                    tree.BeginEdit();
                }
            }
        }

        private string getNextName(TreeNodeAdv node, string translateKey, bool isFolder)
        {
            var defaultName = MLS.Instance.TranslateText(translateKey);
            var availableNames = new List<string>();
            foreach (TreeNodeAdv childNodeAvd in node.Children)
            {
                var innerNode = childNodeAvd.Tag as ShoppaTreeNode;
                if (innerNode != null && innerNode.IsFolder == isFolder)
                    availableNames.Add(innerNode.Text);
            }

            return TreeController2.GetNextName(availableNames, defaultName);
        }

        private void AddMediaFiles(MediaImportType mediaType)
        {
            var tree = GetTreeFromTreeId(TreeIDEnumaration.Picture);
            var node = tree.SelectedNode;
            // If no node is selected default to root for own material
            if (node == null)
                node = tree.GetRoot(TreeGroupInformation.GroupType.MyMaterial);

            // Selected Node is Content within a folder, select the folder instead
            if (node.IsLeaf)
                node = node.Parent;

            // Make sure it is a folder
            var folderTreeNode = node.Tag as ShoppaTreeNode;
            if (folderTreeNode == null || !folderTreeNode.IsFolder)
                throw new ApplicationException("Missing Folder Node");

            // Ensure permission
            if (!new Permission.Folder(AppData.Instance).CanAddOrRemoveContent(folderTreeNode.ContentGuid, TreeIDEnumaration.Picture))
                throw new ApplicationException("Permission denied");

            var sources = GetMediaUsingImportDialogs(mediaType);

            if (!sources.Any())
                return;

            if (sources.Count() >= 2 && !_allowedMultiplePicImport)
            {
                MessageBoxes.MultiPictureImportNotAllowed(this);
                return;
            }

            ProcessPictureFileNamesToImport(sources, node);
            _syncmanager.BeginSynchronize();
        }

        private const int VIDEO_FILE_WARNING_SIZE_DEFAULT = 50;
        private const int VIDEO_FILE_BLOCKING_SIZE_DEFAULT = 500;
        private List<IMediaSource> FilterLocalVideoFilesBySize(ICollection<IMediaSource> sources)
        {
            var metaController = new MetaController(AppData.Instance);

            var filteredSources = new List<IMediaSource>();

            foreach (var source in sources)
            {
                bool shouldAdd = true;
                if (source is VideoSource videoSource && !videoSource.IsUrl)
                {
                    var fileInfo = new FileInfo(videoSource.OriginalPath);
                    long fileSizeInMegaBytes = fileInfo.Length / 1024 / 1024;

                    if (!metaController.TryGetMetaValueAsInt(MetaValue.Fields.VideoFileWarningSize, out int fileSizeToWarn))
                        fileSizeToWarn = VIDEO_FILE_WARNING_SIZE_DEFAULT;

                    if (!metaController.TryGetMetaValueAsInt(MetaValue.Fields.VideoFileBlockingSize, out int fileSizeToBlock))
                        fileSizeToBlock = VIDEO_FILE_BLOCKING_SIZE_DEFAULT;

                    if (fileSizeInMegaBytes > fileSizeToBlock)
                    {
                        shouldAdd = false;
                        MessageBoxes.VideoFileTooLarge(fileSizeToBlock);
                    }
                    else if (fileSizeInMegaBytes > fileSizeToWarn)
                    {
                        var result = MessageBoxes.WarnVideoFileLarge(fileSizeInMegaBytes);
                        shouldAdd = result == DialogResult.Yes;
                    }
                }

                if (shouldAdd)
                    filteredSources.Add(source);
            }
            return filteredSources;
        }

        private List<IMediaSource> GetMediaUsingImportDialogs(MediaImportType mediaType)
        {
            IEnumerable<IMediaSource> sources;
            switch (mediaType)
            {
                case MediaImportType.Video:
                    sources = ShowImportVideoDialog();
                    break;
                case MediaImportType.PictureAndVideo:
                case MediaImportType.Picture:
                    var filesToImport = ShowImportMediaDialog(mediaType);
                    sources = new MediaFilter(AppData.Instance.CurrentUser).GetSourcesByFileExtention(filesToImport);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(mediaType), mediaType, "Unknown media import type.");
            }

            var filteredSources = FilterLocalVideoFilesBySize(sources.ToList());
            return filteredSources;
        }

        private void processPictureFileNames(List<EncapsulatedPicture> pictures, TreeNodeAdv targetNode, out List<Guid> failedPictures)
        {
            var pictureController = new PictureController(AppData.Instance, PictureThumbnailCacheInstance.Instance);

            failedPictures = new List<Guid>();
            if (pictures != null && pictures.Count > 0)
            {
                DialogResult? useResultForAll = null;
                DialogResult dialogResult;
                int counter = 0;

                //Dictionary<string, Guid> toReplace = new Dictionary<string, Guid>();
                List<EncapsulatedPicture> toRename = new List<EncapsulatedPicture>();

                foreach (var picture in pictures)
                {
                    Dictionary<string, Guid> namesThatClash = PictureController.GetNameClashes(new List<string>() { picture.Name }, (targetNode.Tag as ShoppaTreeNode).ContentGuids);
                    if (namesThatClash.Count > 0)
                    {
                        var kvp = namesThatClash.FirstOrDefault();
                        if (!useResultForAll.HasValue)
                        {
                            using (var convertForm = new PictureNameClashDialogForm(picture.Name, namesThatClash.Count - counter, false))
                            {
                                convertForm.StartPosition = FormStartPosition.CenterParent;
                                convertForm.TopMost = true;
                                dialogResult = convertForm.ShowDialog();

                                if (convertForm.UseResultForAll)
                                    useResultForAll = convertForm.DialogResult;
                            }
                            counter++;
                        }
                        else
                            dialogResult = useResultForAll.Value;
                        if (dialogResult == System.Windows.Forms.DialogResult.No)
                            toRename.Add(picture);
                        if (dialogResult == System.Windows.Forms.DialogResult.Cancel)
                            failedPictures.Add(picture.Guid);
                    }
                }

                //if (toReplace.Count > 0)
                //    performPictureExchange(toReplace);
                var targetGuid = (targetNode.Tag as ShoppaTreeNode).ContentGuid;
                foreach (var picture in toRename)
                {
                    var parentEncapsulatedFolder = FolderController.GetEncapsulatedFolder(targetGuid);
                    int renameIndex = 1;
                    string renamed = string.Format(MLS.Instance.TranslateText("PNC_PictureNameClashRenamePattern"), picture.Name, renameIndex);

                    while (PictureController.GetNameClashes(new List<string>() { renamed }, new List<Guid>() { targetGuid }).Count > 0)
                    {
                        renameIndex++;
                        renamed = string.Format(MLS.Instance.TranslateText("PNC_PictureNameClashRenamePattern"), picture.Name, renameIndex);
                    }
                    picture.Name = renamed;
                    pictureController.SavePictureLightData(picture);
                }
            }
        }

        private Shoppa.Controllers.Helpers.PdfDownloadHandler ProcessPdfFiles(List<string> fileNames, bool shouldAllowMultiSelect)
        {
            var processedFiles = new List<string>();

            Shoppa.Controllers.Helpers.PdfDownloadHandler pdfHandler = null;

            foreach (var fileName in fileNames)
            {
                if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ConvertOnServer))
                    return pdfHandler;
                var fi = new FileInfo(fileName);
                if (fi.Extension.ToLower() == ".pdf")
                {
                    var pdfDocument = PictureController.VerifyPdf(fileName);
                    if (pdfDocument == null)
                        throw new NotImplementedException("Not sure what do when we fail to recognize a pdf");
                    if (pdfHandler == null)
                        pdfHandler = new Shoppa.Controllers.Helpers.PdfDownloadHandler();
                    pdfHandler.AddToQueue(pdfDocument);
                }
            }
            if (pdfHandler != null && pdfHandler.Documents.Count > 0)
            {
                foreach (var document in pdfHandler.Documents)
                {
                    DialogResult dialogResult;
                    SelectPdfPages selectForm = null;
                    if (!document.IsCancelled)
                    {
                        selectForm = new SelectPdfPages(document, shouldAllowMultiSelect);
                        selectForm.StartPosition = FormStartPosition.CenterParent;
                        dialogResult = selectForm.ShowDialog();
                    }
                    else
                        dialogResult = System.Windows.Forms.DialogResult.Cancel;
                    if (dialogResult == System.Windows.Forms.DialogResult.OK)
                        document.MarkAsReadyForDownload(selectForm.GetSelectedPages());
                    else
                        document.SetCancelled();
                    selectForm?.Dispose();
                    processedFiles.Add(document.FileName);
                }
                // user interaction is complete!
                pdfHandler.EndPreview();
                pdfHandler.StartConverting();
                // note that pdf fileNames doesnt necessarily exist yet!
                fileNames.AddRange(pdfHandler.GetExpectedFileNames());
            }
            // remove any pdf file from normal fileList. THey will be processed seperately!
            foreach (var fileToRemove in processedFiles)
                fileNames.Remove(fileToRemove);
            return pdfHandler;
        }

        private delegate List<EncapsulatedPicture> asyncAddPicturesHandler(IEnumerable<IMediaSource> files, Guid targetGuid, List<string> toRename, string renamePattern);
        private void ProcessPictureFileNamesToImport(ICollection<IMediaSource> filesToImport, TreeNodeAdv targetNode)
        {
            // Verify and Add all Valid filenames
            var videoFilePaths = filesToImport.OfType<VideoSource>().Select(file => file.OriginalPath);
            var pictureFilePaths = filesToImport.OfType<PictureSource>().Select(file => file.OriginalPath);
            
            var validPicFilePaths = validatePictureFilenames(pictureFilePaths);
            var pdfHandler = ProcessPdfFiles(validPicFilePaths, true);
            var allFilePaths = videoFilePaths.Concat(validPicFilePaths).ToList();
            if (allFilePaths.Any())
            {
                var namesThatClash = PictureController.GetNameClashes(allFilePaths, (targetNode.Tag as ShoppaTreeNode).ContentGuids);
                var toReplace = new Dictionary<string, Guid>();
                var toRename = new List<string>();

                DialogResult? useResultForAll = null;
                DialogResult dialogResult;
                int counter = 0;

                foreach (var kvp in namesThatClash)
                {
                    string filename = kvp.Key;

                    if (!useResultForAll.HasValue)
                    {
                        bool isDesigner = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design);
                        using (var convertForm = new PictureNameClashDialogForm(filename, namesThatClash.Count - counter, isDesigner)
                        {
                            StartPosition = FormStartPosition.CenterParent,
                            TopMost = true
                        })
                        {
                            dialogResult = convertForm.ShowDialog();

                            if (convertForm.UseResultForAll)
                                useResultForAll = convertForm.DialogResult;
                        }
                        counter++;
                    }
                    else
                        dialogResult = useResultForAll.Value;

                    if (dialogResult == DialogResult.Yes)
                    {
                        toReplace.Add(kvp.Key, kvp.Value);
                        allFilePaths.Remove(kvp.Key);
                    }
                    else if (dialogResult == DialogResult.No)
                        toRename.Add(kvp.Key);
                    else if (dialogResult == DialogResult.Cancel)
                    {
                        if (pdfHandler != null)
                            pdfHandler.Cancel();
                        return;
                    }
                }

                if (pdfHandler != null && pdfHandler.GetPagesCount() > 0)
                {
                    using (ShoppaAB.Forms.AsyncSplash splash = new ShoppaAB.Forms.AsyncSplash(MLS.Instance.TranslateText("ASPL_ConvertingPictures"), Resources.img_splash))
                    {
                        splash.TranslationPleaseWait = MLS.Instance.TranslateText("MF_SplashPleaseWait");
                        splash.StartPosition = FormStartPosition.CenterParent;

                        var myAsyncAction = new Action(pdfHandler.WaitForHandlerToFinish);
                        var result = myAsyncAction.BeginInvoke(splash.GetCallback(), null);
                        splash.ShowDialog();
                    }
                }

                if (toReplace.Count > 0)
                {
                    using (var splash = new ShoppaAB.Forms.AsyncSplash(MLS.Instance.TranslateText("ASPL_ReplacingPictures"), Resources.img_splash))
                    {
                        splash.TranslationPleaseWait = MLS.Instance.TranslateText("MF_SplashPleaseWait");
                        splash.StartPosition = FormStartPosition.CenterParent;

                        var myAsyncAction = new Action<Dictionary<string, Guid>>(PerformPictureExchange);
                        var result = myAsyncAction.BeginInvoke(toReplace, splash.GetCallback(), null);
                        splash.ShowDialog();
                    }
                    PerformPictureExchange(toReplace);
                }
                var targetGuid = (targetNode.Tag as ShoppaTreeNode).ContentGuid;

                var distinctFilesToImport = filesToImport.ToList();
                var pdfsAsPictures = GetProcessedPdfs(allFilePaths, distinctFilesToImport).ToList();
                distinctFilesToImport.AddRange(pdfsAsPictures);
                distinctFilesToImport.RemoveAll(file => !allFilePaths.Contains(file.OriginalPath));

                List<EncapsulatedPicture> newPictures;
                bool hasVideoFilesToImport = distinctFilesToImport.OfType<VideoSource>().Any();
                if (distinctFilesToImport.Count > 3 || hasVideoFilesToImport)
                {
                    string message = hasVideoFilesToImport
                        ? MLS.Instance.TranslateText("ASPL_AddingMediaFiles")
                        : MLS.Instance.TranslateText("ASPL_AddingPictures");
                    using (ShoppaAB.Forms.AsyncSplash splash = new ShoppaAB.Forms.AsyncSplash(message, Resources.img_splash))
                    {
                        splash.TranslationPleaseWait = MLS.Instance.TranslateText("MF_SplashPleaseWait");
                        splash.StartPosition = FormStartPosition.CenterParent;

                        var myAsyncAction = new asyncAddPicturesHandler(ImportMediaFiles);
                        var result = myAsyncAction.BeginInvoke(
                            distinctFilesToImport,
                            targetGuid,
                            toRename,
                            MLS.Instance.TranslateText("PNC_PictureNameClashRenamePattern"),
                            splash.GetCallback(),
                            null
                        );
                        splash.ShowDialog();
                        newPictures = myAsyncAction.EndInvoke(result);
                    }
                }
                else
                    newPictures = ImportMediaFiles(
                        distinctFilesToImport,
                        targetGuid,
                        toRename,
                        MLS.Instance.TranslateText("PNC_PictureNameClashRenamePattern")
                        );

                int failedFileCount = allFilePaths.Count - (newPictures.Count + toReplace.Count);
                if (failedFileCount > 0)
                    MessageBoxes.MediaImportFailed(failedFileCount, !hasVideoFilesToImport, this);

                if (pdfHandler != null)
                    pdfHandler.CleanTemporaryFiles();

                if ((pictureSearch.PictureTree.SelectedNode.Tag as ShoppaTreeNode).ContentGuids.Contains(targetGuid))
                {
                    foreach (var item in newPictures)
                        pictureSearch.PicturesImageListView.AddPictureContent(item.Guid, item.Name, targetNode, item.IsVideo);
                }
            }
        }

        private IEnumerable<PictureSource> GetProcessedPdfs(ICollection<string> allFilePaths, ICollection<IMediaSource> distinctFilesToImport)
        {
            var pdfNamesToImport = distinctFilesToImport
                .Where(f => Path.GetExtension(f.OriginalPath).ToLowerInvariant() == ".pdf")
                .Select(pdf => Path.GetFileNameWithoutExtension(pdf.OriginalPath));

            if (pdfNamesToImport.Any())
            {
                foreach (var filePath in allFilePaths)
                {
                    string fileName = Path.GetFileNameWithoutExtension(filePath);
                    bool isProcessedPdf = pdfNamesToImport.Any(pdfName => fileName.Contains(pdfName));
                    if (isProcessedPdf)
                        yield return new PictureSource(filePath);
                }
            }
        }

        private static List<EncapsulatedPicture> ImportMediaFiles(
            IEnumerable<IMediaSource> files,
            Guid folderGuid,
            List<string> picturesToRename,
            string renamePattern)
        {
            FillPicAndThubForVideoFiles(files);
            return new PictureController(AppData.Instance, PictureThumbnailCacheInstance.Instance).Add(files, folderGuid, picturesToRename, renamePattern);
        }

        private static void FillPicAndThubForVideoFiles(IEnumerable<IMediaSource> filesToImport)
        {
            foreach (var videoSource in filesToImport.OfType<VideoSource>())
                FillPicAndThumbForVideoFile(videoSource);
        }

        private static void FillPicAndThumbForVideoFile(VideoSource videoSource)
        {
            videoSource.FileContent = ImageHelper.GetVideoFrame(videoSource.OriginalPath, out Image videoThumb);
            videoSource.ThumbContent = videoThumb;
        }

        private void ChangeMediaFile(Guid pictureGuid, MediaImportType mediaType)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if (pictureGuid == Guid.Empty)
                return;

            var newFiles = GetMediaUsingImportDialogs(mediaType);

            if (!newFiles.Any())
                return;

            if (newFiles.First() is PictureSource pictureSource)
            {
                // Verify and Add all Valid filenames
                var validPicFilePaths = validatePictureFilenames(pictureSource.OriginalPath.Yield());
                if (validPicFilePaths != null && validPicFilePaths.Count == 1)
                {
                    var pdfHandler = ProcessPdfFiles(validPicFilePaths, false);
                    if (pdfHandler != null && pdfHandler.GetPagesCount() > 0)
                    {
                        using (ShoppaAB.Forms.AsyncSplash splash = new ShoppaAB.Forms.AsyncSplash(
                            MLS.Instance.TranslateText("ASPL_ConvertingPictures"),
                            Resources.img_splash))
                        {
                            splash.TranslationPleaseWait = MLS.Instance.TranslateText("MF_SplashPleaseWait");
                            splash.StartPosition = FormStartPosition.CenterParent;

                            var asyncAction = new Action(pdfHandler.WaitForHandlerToFinish);
                            var result = asyncAction.BeginInvoke(splash.GetCallback(), null);
                            splash.ShowDialog();
                        }
                    }

                    if (validPicFilePaths.Count == 1)
                    {
                        ChangeMediaFileAndUpdateUI(pictureGuid, new PictureSource(validPicFilePaths.Single()));
                        _syncmanager.BeginSynchronize();
                    }

                    if (pdfHandler != null)
                        pdfHandler.CleanTemporaryFiles();
                }
            }
            else if (newFiles.First() is VideoSource videoSource)
            {
                using (ShoppaAB.Forms.AsyncSplash splash = new ShoppaAB.Forms.AsyncSplash(
                    MLS.Instance.TranslateText("ASPL_ConvertingVideos"),
                    Resources.img_splash))
                {
                    splash.TranslationPleaseWait = MLS.Instance.TranslateText("MF_SplashPleaseWait");
                    splash.StartPosition = FormStartPosition.CenterParent;

                    var asyncAction = new Action(() => ChangeMediaFileAndUpdateUI(pictureGuid, videoSource));
                    var result = asyncAction.BeginInvoke(splash.GetCallback(), null);
                    splash.ShowDialog();
                }
                _syncmanager.BeginSynchronize();
            }          
        }

        private void PerformPictureExchange(Dictionary<string, Guid> toExchange)
        {
            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design))
                return;

            foreach (var kvp in toExchange)
            {
                Guid pictureGuid = kvp.Value;
                string filename = kvp.Key;
                var mediaFiles = new MediaFilter(AppData.Instance.CurrentUser).GetSourcesByFileExtention(filename.Yield().ToList());
                ChangeMediaFileAndUpdateUI(pictureGuid, mediaFiles.Single());
            }
        }

        private void ChangeMediaFileAndUpdateUI(Guid pictureGuid, IMediaSource mediaSource)
        {
            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design))
                return;

            if (mediaSource is VideoSource videoSource)
                FillPicAndThumbForVideoFile(videoSource);

            try
            {
                new PictureController(AppData.Instance, PictureThumbnailCacheInstance.Instance).ChangePicture(pictureGuid, mediaSource);
            }
            catch (InvalidOperationException ex)
            {
                ShowMessageOnMediaFileException(ex, mediaSource.OriginalPath);
            }
            // Update on contentView
            pictureSearch.PicturesImageListView.UpdatePictureContent(pictureGuid);
            // Update on Templates, both saved and currently open
            List<Guid> templateGuids;
            using (var templateDal = Embedded.Factory.Template)
                templateGuids = templateDal.GetTemplatesWithSpecificPicture(pictureGuid);

            var templateCapsule = GetActiveFormTemplateBase()?.GetTemplateCapsule();
            if (templateCapsule != null)
                templateGuids.Add(templateCapsule.Guid);

            RefreshPictureOnTemplates(templateGuids, pictureGuid);
        }

        /// <summary>
        /// AddNewFolder, including GUI, tree update and sync
        /// </summary>
        private void AddNewFolder(TreeIDEnumaration treeId)
        {
            var folderController = new FolderController(AppData.Instance);

            var tree = GetTreeFromTreeId(treeId);
            var node = tree.SelectedNode;
            // If no node is selected default to root for own material
            if (node == null)
                node = tree.GetRoot(TreeGroupInformation.GroupType.MyMaterial);

            // Deselect current
            node.IsSelected = false;

            // Selected Node is Content within a folder, select the folder instead
            if (node.IsLeaf)
                node = node.Parent;

            // Make sure it is a folder
            var folderTreeNode = node.Tag as ShoppaTreeNode;
            if (folderTreeNode == null || !folderTreeNode.IsFolder)
                throw new ApplicationException("Missing Folder Node");

            // Ensure permission
            if (!new Permission.Folder(AppData.Instance).CanAddOrRemoveContent(folderTreeNode.ContentGuid, treeId))
                throw new ApplicationException("Permission denied");

            var folderName = getNextName(node, "TREE_NewName_Folder", true);
            if (string.IsNullOrEmpty(folderName))
                folderName = "New Folder";
            var parentGuid = folderTreeNode.ContentGuid;
            var newFolderGuid = folderController.Add(parentGuid, folderName, tree);

            forceUpdateAfterBrowsing = true;

            if (newFolderGuid != Guid.Empty)
            {
                //_syncmanager.BeginSynchronize();
                // Select new Folder
                node.IsSelected = false;
                var path = TreeController2.GetPathToItem(TreeIDEnumaration.Folder, newFolderGuid);
                path.Add(newFolderGuid); // Add own node last
                var newNode = tree.BrowseToContent(path);
                (newNode.Tag as ShoppaTreeNode).IsNewNode = true;
                tree.Focus();

                // Start renaming of folder
                tree.BeginEdit();
            }

            if (treeId == TreeIDEnumaration.Template)
            {
                templateSearch.FindVisibleTemplateTreeNodeCount();
                templateSearch.AdjustTreeView();
            }
        }

        private void toolStripMenuItemShowBindings_Click(object sender, EventArgs e)
        {
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);

            List<Guid> guidsToReportFor = new List<Guid>();
            var nodes = templateSearch.TemplateTree.SelectedNodes;
            if (nodes != null && nodes.Count > 0)
            {
                // Read first as any other operations will change the NodeCollection
                foreach (var node in nodes)
                {
                    var shoppaNode = node.Tag as ShoppaTreeNode;
                    if (shoppaNode != null && !shoppaNode.IsFolder)
                        guidsToReportFor.Add(shoppaNode.ContentGuid);
                }
            }

            string rtf = templateController.GetTagUsageAsRTF(guidsToReportFor);

            richTextBoxPrintCtrl1 = new RichTextBoxPrintCtrl();
            richTextBoxPrintCtrl1.Rtf = rtf;

            PrintDocument pd = new PrintDocument();
            pd.BeginPrint += new PrintEventHandler(pd_BeginPrint);
            pd.PrintPage += new PrintPageEventHandler(pd_PrintPage);

            using (PrintPreviewDialog ppd = new PrintPreviewDialog())
            {
                ppd.Document = pd;
                ppd.ShowDialog();
            }
        }

        RichTextBoxPrintCtrl richTextBoxPrintCtrl1;
        private int checkPrint;
        private object lockObject = new object();

        void pd_PrintPage(object sender, PrintPageEventArgs e)
        {
            // Print the content of RichTextBox. Store the last character printed.
            checkPrint = richTextBoxPrintCtrl1.Print(checkPrint, richTextBoxPrintCtrl1.TextLength, e);

            // Check for more pages
            if (checkPrint < richTextBoxPrintCtrl1.TextLength)
                e.HasMorePages = true;
            else
                e.HasMorePages = false;
        }

        void pd_BeginPrint(object sender, PrintEventArgs e)
        {
            checkPrint = 0;
        }

        /// <summary>
        /// Delete Template / TemplateFolders / PictureFolders from Tree and Database
        /// </summary>
        private void DeleteContentOrFolder(TreeIDEnumaration treeId)
        {
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);
            var folderController = new FolderController(AppData.Instance);

            var tree = GetTreeFromTreeId(treeId);
            // Delete all selected.
            var nodes = tree.SelectedNodes;
            if (nodes != null && nodes.Count > 0)
            {
                // Save parent for post-delete-selection, require tree with 'multiselect under own node only'
                var parent = nodes.First().Parent;
                var folderToDelete = new List<Guid>();
                var contentsToDelete = new List<Guid>();
                var guidsToName = new Dictionary<Guid, string>();

                // Read first as any other operations will change the NodeCollection
                foreach (var node in nodes)
                {
                    var shoppaNode = node.Tag as ShoppaTreeNode;

                    if (shoppaNode != null)
                    {
                        if (shoppaNode.IsFolder)
                            folderToDelete.Add(shoppaNode.ContentGuid);
                        else
                            contentsToDelete.Add(shoppaNode.ContentGuid);

                        if (!guidsToName.ContainsKey(shoppaNode.ContentGuid))
                            guidsToName.Add(shoppaNode.ContentGuid, shoppaNode.Text);
                    }
                }

                // Confirm with user
                if (folderToDelete.Count > 0 && contentsToDelete.Count == 0)
                {
                    var dialogResult = MessageBoxes.ConfirmDeleteFolders(this.FindForm(), guidsToName.Select(i => i.Value));
                    if (dialogResult != DialogResult.Yes)
                    {
                        isNewKeyPress = true;
                        return;
                    }
                }
                else
                {
                    var dialogResult = MessageBoxes.ConfirmDeleteContent(this.FindForm(), guidsToName.Select(i => i.Value));
                    if (dialogResult != DialogResult.Yes)
                    {
                        isNewKeyPress = true;
                        return;
                    }
                }

                resetChangesInTree(folderToDelete, contentsToDelete, treeId);

                // Do not change selection while deleting, remember to ReAdd at end
                templateSearch.TemplateTree.SelectionChanged -= new EventHandler(TemplateTree_SelectionChangedAsync);

                // Get all templatesGuid under folderToDelete
                var templatesGuids = templateController.GetAllTemplateGuidsUnderFolders(folderToDelete);

                // Get all templates open in Design
                var templateOpenInDesign = new List<Guid>();
                foreach (var content in dockingPanel.DockedContent)
                    if (content is FormTemplateDesign)
                        templateOpenInDesign.Add((content as FormTemplateBase).TemplateGuid);

                // Find all templates that are open in Design AND about to be deleted
                var contentToClose = new List<Guid>();
                contentToClose.AddRange(templateOpenInDesign.Intersect(contentsToDelete));
                contentToClose.AddRange(templateOpenInDesign.Intersect(templatesGuids));

                // Close delete templates still open in design
                foreach (var item in contentToClose)
                {
                    bool alreadyOpen, openInDesign;
                    FormTemplateBase baseForm;
                    TryGetOpenTemplate(item, out baseForm, out alreadyOpen, out openInDesign);
                    // Design opens in new dockwindows so they need to be closed. This will force reopening in Production, but that does not matter.
                    if (openInDesign)
                    {
                        // Discard any changes
                        baseForm.GetTemplate().Dirty = false;
                        baseForm.Close();
                    }
                    // Production uses same windows so switching it to Parent is enough
                }

                // Delete templates
                if (treeId == TreeIDEnumaration.Template)
                {
                    foreach (var item in contentsToDelete)
                        templateController.Delete(item, tree);
                }

                // Delete folders. This will also delete any subfolders and templates therein
                foreach (var item in folderToDelete)
                    folderController.Delete(item, tree);

                forceUpdateAfterBrowsing = true;

                // Remove any autoselections
                var newlySelectedNodes = new List<TreeNodeAdv>(tree.SelectedNodes);
                foreach (var item in newlySelectedNodes)
                    item.IsSelected = false;

                // Important, ReAdd SelectionChanged
                templateSearch.TemplateTree.SelectionChanged += new EventHandler(TemplateTree_SelectionChangedAsync);
                // Database changes occured, Synchronize them to server
                _syncmanager.BeginSynchronize();
                // Set parent as new Selection
                parent.IsSelected = true;
                isNewKeyPress = true;

                templateSearch.FindVisibleCampaignTreeNodeCount();
                templateSearch.AdjustTreeView();
            }
        }


        /// <summary>
        /// Resets the folder nodes to their original state if all changed material within the folder was deleted.
        /// Used in Template Tree And Picture Tree
        /// </summary>
        /// <param name="deletedFolder"></param>
        /// <param name="deletedContents"></param>
        /// <param name="treeId"></param>
        void resetChangesInTree(List<Guid> deletedFolder, List<Guid> deletedContents, TreeIDEnumaration treeId)
        {
            var folderController = new FolderController(AppData.Instance);

            var tree = GetTreeFromTreeId(treeId);

            using (var dalCM = Embedded.Factory.ChangedMaterial)
            {
                List<GuidParentGuid> changesToReset = new List<GuidParentGuid>();

                if (treeId == TreeIDEnumaration.Template)
                {
                    foreach (var content in deletedContents.Where(c => dalCM.IsChangedItem(c)))
                        changesToReset.AddRange(AvailabilityController.ResetChanged(content));
                }

                List<Guid> folderContents = new List<Guid>();
                var subFolders = new List<FolderStructureCapsule>();
                foreach (var folder in deletedFolder)
                {
                    if (treeId == TreeIDEnumaration.Template)
                        folderContents.AddRange(TemplateController.GetChangedTemplates(folder));
                    else if (treeId == TreeIDEnumaration.Picture)
                        folderContents.AddRange(PictureController.GetChangedPictures(folder));
                    subFolders.AddRange(folderController.GetAllSubFolders(folder));
                }
                foreach (var sub in subFolders)
                {
                    if (treeId == TreeIDEnumaration.Template)
                        folderContents.AddRange(TemplateController.GetChangedTemplates(sub.Guid));
                    else if (treeId == TreeIDEnumaration.Picture)
                        folderContents.AddRange(PictureController.GetChangedPictures(sub.Guid));
                }

                foreach (var deletedCM in folderContents)
                    changesToReset.AddRange(AvailabilityController.ResetChanged(deletedCM));

                // Make sure we don't add any pictures as tree nodes when resetting the folders
                if (treeId == TreeIDEnumaration.Picture)
                    changesToReset = changesToReset.Where(c => !folderContents.Contains(c.ItemGuid)).ToList();

                foreach (var change in changesToReset)
                {
                    tree.AddContent(new ItemChangeInfo()
                    {
                        ChangeType = ItemChangeType.NewlyChanged,
                        IsNewlyChanged = false,
                        ItemGuid = change.ItemGuid,
                        ParentGuid = change.ParentGuid
                    });
                }
            }
        }

        /// <summary>
        /// ContentView Delete
        /// </summary>
        private void deletePictureInContentView(TreeIDEnumaration treeId)
        {
            var pictureController = new PictureController(AppData.Instance, PictureThumbnailCacheInstance.Instance);

            var tree = GetTreeFromTreeId(treeId);
            var node = tree.SelectedNode;
            if (node == null)
                return;

            if (treeId == TreeIDEnumaration.Picture)
            {
                List<GuidParentGuid> itemsToRefresh = new List<GuidParentGuid>();
                var selected = pictureSearch.PicturesImageListView.SelectedItems;
                var syncAfter = false;
                var selectedGuid = new List<Guid>();
                if (selected.Count > 0)
                {
                    var usedPictures = new Dictionary<Guid, List<Guid>>();
                    var itemsToDelete = new List<Guid>();
                    var i = 0;
                    DialogResult? useResultForAll = null;
                    foreach (var item in selected)
                    {
                        var guid = (Guid)item.VirtualItemKey;
                        if (guid != Guid.Empty)
                        {
                            List<Guid> templateGuids;
                            using (var templateDal = Embedded.Factory.Template)
                                templateGuids = templateDal.GetTemplatesWithSpecificPicture(guid);
                            List<string> templateNames = templateGuids.Select(tg => TemplateController.GetTemplateName(tg)).ToList();

                            DialogResult result;
                            if (useResultForAll.HasValue)
                                result = useResultForAll.Value;
                            else
                            {
                                using (var pddf = new PictureDeleteDialogForm(item.Text, templateNames, selected.Count - i, selected.Count > 1))
                                {
                                    pddf.StartPosition = FormStartPosition.CenterParent;
                                    result = pddf.ShowDialog();
                                    if (pddf.UseResultForAll)
                                        useResultForAll = result;
                                }
                            }

                            i++;

                            bool isCancelled = false;
                            switch (result)
                            {
                                case DialogResult.Cancel:
                                    isCancelled = true;
                                    break;
                                case DialogResult.No:
                                    continue;
                                case DialogResult.Yes:
                                    break;
                                default:
                                    throw new NotImplementedException(string.Format("case {0} is not implemented", result));
                            }
                            if (isCancelled)
                            {
                                isNewKeyPress = true;
                                break;
                            }

                            if (templateGuids.Count > 0)
                                usedPictures.Add(guid, templateGuids);
                            itemsToDelete.Add(guid);
                        }
                    }
                    foreach (var guid in itemsToDelete)
                    {
                        itemsToRefresh.AddRange(AvailabilityController.ResetChanged(guid));
                        itemsToRefresh = itemsToRefresh.Where(item => item.ItemGuid != guid).ToList(); // Remove the picture's GuidParentGuid so that we don't add the picture as a tree node

                        pictureController.Delete(guid);
                        selectedGuid.Add(guid);
                        if (usedPictures.ContainsKey(guid))
                            ClearPictureOnTemplates(usedPictures[guid], guid);
                    }

                    foreach (var change in itemsToRefresh)
                    {
                        pictureSearch.PictureTree.AddContent(new ItemChangeInfo()
                        {
                            ChangeType = ItemChangeType.NewlyChanged,
                            IsNewlyChanged = false,
                            ItemGuid = change.ItemGuid,
                            ParentGuid = change.ParentGuid
                        });
                    }

                    var folderGuid = (node.Tag as ShoppaTreeNode).ContentGuid;
                    pictureSearch.PicturesImageListView.RemovePictureContent(selectedGuid, folderGuid);
                    syncAfter = itemsToDelete.Count > 0;
                }

                if (syncAfter)
                    _syncmanager.BeginSynchronize();

                isNewKeyPress = true;
            }
            else
            {
                throw new NotImplementedException("");
            }
        }

        private void deleteTemplateOrFolderInContentView(TreeIDEnumaration treeId)
        {
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);
            var folderController = new FolderController(AppData.Instance);

            var tree = GetTreeFromTreeId(treeId);
            var node = tree.SelectedNode;
            if (node == null)
                return;

            if (treeId == TreeIDEnumaration.Template)
            {
                var selected = templatesImageListView.SelectedItems.Where(i => i.Text != "..").ToList();
                var syncAfter = false;
                var folderToDelete = new List<Guid>();
                var contentsToDelete = new List<Guid>();
                var guidsToName = new Dictionary<Guid, string>();

                foreach (var item in selected)
                {
                    if ((bool)item.Tag)
                    {
                        var folderNodeAdv = node.Children.Where(c => (c.Tag as ShoppaTreeNode).ContentGuids.Contains((Guid)item.VirtualItemKey)).FirstOrDefault();
                        if (folderNodeAdv != null)
                        {
                            var folderNode = folderNodeAdv.Tag as ShoppaTreeNode;
                            folderToDelete.AddRange(folderNode.ContentGuids);
                        }
                    }
                    else
                        contentsToDelete.Add((Guid)item.VirtualItemKey);

                    if (!guidsToName.ContainsKey((Guid)item.VirtualItemKey))
                        guidsToName.Add((Guid)item.VirtualItemKey, item.Text);
                }

                // Confirm with user
                if (folderToDelete.Count > 0 && contentsToDelete.Count == 0)
                {
                    var dialogResult = MessageBoxes.ConfirmDeleteFolders(this.FindForm(), guidsToName.Select(i => i.Value));
                    if (dialogResult != DialogResult.Yes)
                    {
                        isNewKeyPress = true;
                        return;
                    }
                }
                else
                {
                    var dialogResult = MessageBoxes.ConfirmDeleteContent(this.FindForm(), guidsToName.Select(i => i.Value));
                    if (dialogResult != DialogResult.Yes)
                    {
                        isNewKeyPress = true;
                        return;
                    }
                }

                // Get all templatesGuid under folderToDelete
                var templatesGuids = templateController.GetAllTemplateGuidsUnderFolders(folderToDelete);

                // Get all templates open in Design
                var templateOpenInDesign = new List<Guid>();
                foreach (var content in dockingPanel.DockedContent)
                    if (content is FormTemplateDesign)
                        templateOpenInDesign.Add((content as FormTemplateBase).TemplateGuid);

                // Find all templates that are open in Design AND about to be deleted
                var contentToClose = new List<Guid>();
                contentToClose.AddRange(templateOpenInDesign.Intersect(contentsToDelete));
                contentToClose.AddRange(templateOpenInDesign.Intersect(templatesGuids));

                // Close delete templates still open in design
                foreach (var item in contentToClose)
                {
                    bool alreadyOpen, openInDesign;
                    FormTemplateBase baseForm;
                    TryGetOpenTemplate(item, out baseForm, out alreadyOpen, out openInDesign);
                    // Design opens in new dockwindows so they need to be closed. This will force reopening in Production, but that does not matter.
                    if (openInDesign)
                    {
                        // Discard any changes
                        baseForm.GetTemplate().Dirty = false;
                        baseForm.Close();
                    }
                    // Production uses same windows so switching it to Parent is enough
                }

                resetChangesInTree(folderToDelete, contentsToDelete, treeId);

                // Delete templates
                foreach (var item in contentsToDelete)
                {
                    templateController.Delete(item, tree);
                    syncAfter = true;
                }

                // Delete folders. This will also delete any subfolders and templates therein
                foreach (var item in folderToDelete)
                {
                    folderController.Delete(item, tree);
                    syncAfter = true;
                }

                if (syncAfter)
                {
                    // A folder is selected to see contentView, reselect it to update content change
                    reselectCurrentTreeNode(templateSearch.TemplateTree);
                    _syncmanager.BeginSynchronize();
                }
            }
            else
            {
                throw new NotImplementedException("");
            }
            isNewKeyPress = true;
        }

        /// <summary>
        /// Return a Tree from TreeId
        /// </summary>
        /// <param name="treeId"></param>
        /// <returns>Tree or on failure exception, never NULL</returns>
        private ShoppaTree2 GetTreeFromTreeId(TreeIDEnumaration treeId)
        {
            if (treeId == TreeIDEnumaration.Template)
            {
                var tree = templateSearch.TemplateTree;
                if (tree != null)
                    return tree;
                else
                    throw new ApplicationException("Missing Tree");
            }
            else if (treeId == TreeIDEnumaration.Picture)
            {
                var tree = pictureSearch.PictureTree;
                if (tree != null)
                    return tree;
                else
                    throw new ApplicationException("Missing Tree");
            }
            else if (treeId == TreeIDEnumaration.CampaignRecording)
            {
                var tree = templateSearch.CampaignTree;
                if (tree != null)
                    return tree;
                else
                    throw new ApplicationException("Missing Tree");
            }
            else
                throw new NotSupportedException("Unsupported tree");
        }

        #endregion

        #region PRIVATE METHODS TO GUI EVENT LISTENERS METHODS

        /// <summary>
        /// NavigateToPicture
        /// </summary>
        void NavigateToPicture(Guid pictureGuid)
        {
            navigateToContent(TreeIDEnumaration.Picture, pictureGuid);
        }

        private void clearDeferredTemplates()
        {
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);
            var deferredTemplates = templateController.GetDeferredTemplates();
            bool alreadyOpen;
            for (int i = deferredTemplates.Count - 1; i >= 0; i--)
            {
                Guid guid = deferredTemplates.ElementAt(i);
                TryGetOpenTemplate(guid, out _, out alreadyOpen, out _);
                if (!alreadyOpen)
                    templateController.RemoveFromTemplateCash(guid);
            }
        }

        /// <summary>
        /// OpenTemplate in Production, for Design see OpenTemplateInDesign
        /// </summary>
        void OpenTemplate(Guid templateGuid, TreeIDEnumaration templateType, bool keepOutputSizes = false, byte[] templateData = null)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if (templateGuid == Guid.Empty)
                throw new ArgumentException("Can not Expand Template with empty guid");

            var metaController = new MetaController(AppData.Instance);
            var queueController = new QueueController(AppData.Instance);

            // Any changed templates we received in the sync that could not be cleared from cache should now be cleared if not still open
            clearDeferredTemplates();

            EncapsulatedTemplate encTemplate = null;
            Template template = null;
            bool alreadyOpen;
            bool openInDesign;
            FormTemplateBase baseForm;

            TryGetOpenTemplate(templateGuid, out baseForm, out alreadyOpen, out openInDesign);
            if (alreadyOpen)
            {
                template = baseForm.GetTemplate();
                lock (_activeDocumentChangedSubscriptionLock)
                {
                    dockingPanel.ActiveContentChanged -= new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                    baseForm.Activate();
                    Messenger.Send(Messenger.Events.ActivateContentInDockPanel, baseForm);
                    dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                }
                var workMode = openInDesign ? WorkMode.Design : WorkMode.Production;
                if (workMode == WorkMode.Design)
                {
                    var designForm = (FormTemplateDesign)baseForm;
                    DesignModeChanged(MapDesignDockerStateToRibbonUiStateDesignMode(designForm.DockerState));
                }
                else
                    workModeSwitched(workMode);
            }
            else
            {
                if (templateType == TreeIDEnumaration.Template)
                {
                    template = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance).GetTemplate(templateGuid);
                    if (template == null)
                        return;   //TODO: The template failed to instanciate
                    var (metaProduct, metaPictures) = metaController.GetMetaValueProduct();
                    template.UpdateMetaValueProduct(metaProduct, metaPictures);
                    encTemplate = TemplateController.GetEncapsulatedTemplate(templateGuid);
                }
                else if (templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.PrintshopCustomer || templateType == TreeIDEnumaration.PrintshopServer || templateType == TreeIDEnumaration.CampaignRecording)
                {
                    template = queueController.GetTemplate(templateGuid, _productSearchStateFilter?.PriceDate);
                    if (template == null)
                        return;   //TODO: The template failed to instanciate
                    if (templateType == TreeIDEnumaration.CampaignRecording)
                    {
                        var (metaProduct, metaPictures) = metaController.GetMetaValueProduct();
                        template.UpdateMetaValueProduct(metaProduct, metaPictures);
                    }
                    encTemplate = QueueController.GetEncapsulatedTemplate(templateGuid);
                }
                else if (templateType == TreeIDEnumaration.Flow)
                {
                    var flowCapsule = DSController.GetSign(templateGuid);
                    if (flowCapsule != null)
                        DSController.FillSign(flowCapsule);
                    template = DSController.ConvertToTemplate(flowCapsule);
                    if (template == null && templateData == null)
                        return;   //TODO: The template failed to instanciate
                    else if (template == null)
                        template = TemplateController.GetTemplate(templateData);

                    encTemplate = new EncapsulatedTemplate();
                    encTemplate.Guid = templateGuid;
                    encTemplate.Name = "Flowsign";
                }

                (signChildWindow as FormSignProduction).ShowingPrinterQueueTemplate = templateType == TreeIDEnumaration.PrinterQueue;
                (signChildWindow as FormSignProduction).ShowingFlowQueueTemplate = templateType == TreeIDEnumaration.Flow;

                //Change which sign is displayed in the production area

                //Only allow saving queued signs when they are closed
                signChildWindow.SaveOnExit = false;
                if (templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.PrintshopCustomer || templateType == TreeIDEnumaration.CampaignRecording)
                {
                    signChildWindow.SaveOnExit = QueueController.GetState(templateGuid, QueuedOutputStateEnumaration.Queued) ||
                        QueueController.GetState(templateGuid, QueuedOutputStateEnumaration.Campaign);
                }
                else if (templateType == TreeIDEnumaration.Flow)
                    signChildWindow.SaveOnExit = true;
                //Decide if the user CAN edit the template or not (controls whether the InfoDocker is enabled or not)
                signChildWindow.TreeIDEnumaration = templateType;
                signChildWindow.SetTemplate(template, encTemplate);

                if (!openInDesign)
                {
                    var form = signChildWindow as FormSignProduction;
                    form.SetupTemplateInfo();
                }

                ShowSignInDockingPanel();
                if (signChildWindow is FormSignProduction sign
                    && (templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.Flow))
                {
                    sign.DesignPermission = FormSignProduction.Permission.None;
                }

                if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer)) // we don't manage workModes as printshopServer, also DOckerTags is not initialized.
                {
                    workModeSwitched(openInDesign ? WorkMode.Design : WorkMode.Production);
                }
            }

            if (templateType == TreeIDEnumaration.Template || templateType == TreeIDEnumaration.CampaignRecording)
            {
                if (!keepOutputSizes)
                {
                    if (openInDesign)
                        currentTemplateLayoutSizes.SetThumbSizeForComingTemplate((baseForm as FormTemplateDesign).WantedThumbSize);
                    currentTemplateLayoutSizes.SetFormats(template, openInDesign);
                }
                else
                    currentTemplateLayoutSizes.SetFormats(template, false);
                if (openInDesign)
                {
                    var format = template.GetCurrentOutputFormat();
                    dsDocker.SelectSlideAnimations(format.SignAnimSettings);
                    dsDocker.SetTimeslotUsage(template.GetUsedFlowTimeslots());
                    var ftd = (baseForm as FormTemplateDesign);
                    var animationsOfSelection = ftd.GetAnimationsOfSelection();
                    Messenger.Send(ftd, Messenger.Events.BeginTemplateEditing, animationsOfSelection);
                    stack.ClearStack();
                    stack.AddElementsToStack(baseForm.GetStacked());
                }
            }
            else if (!keepOutputSizes && templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.PrintshopCustomer)
            {
                queueTemplateLayoutSizes.SetFormats(template, openInDesign);
            }
            else if (!keepOutputSizes && templateType == TreeIDEnumaration.PrintshopServer)
            {
                queueTemplateLayoutSizes.SetFormats(template, openInDesign, true);
            }
        }

        private void ShowSignInDockingPanel()
        {
            lock (_activeDocumentChangedSubscriptionLock)
            {
                // Show template window and hide folder browser
                dockingPanel.ActiveContentChanged -= new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                Messenger.Send(Messenger.Events.ActivateContentInDockPanel, signChildWindow);
                Messenger.Send(Messenger.Events.RemoveContentInDockPanel, browseFoldersAndTemplatesDock);
                dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
            }
        }

        async Task OpenTemplateAsync(Guid templateGuid, TreeIDEnumaration templateType, CancellationToken cancellationToken, bool keepOutputSizes = false, byte[] templateData = null)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if (templateGuid == Guid.Empty)
                throw new ArgumentException("Can not Expand Template with empty guid");

            var metaController = new MetaController(AppData.Instance);
            var queueController = new QueueController(AppData.Instance);

            // Any changed templates we received in the sync that could not be cleared from cache should now be cleared if not still open
            clearDeferredTemplates();

            EncapsulatedTemplate encTemplate = null;
            Template template = null;
            bool alreadyOpen;
            bool openInDesign;
            FormTemplateBase baseForm;

            TryGetOpenTemplate(templateGuid, out baseForm, out alreadyOpen, out openInDesign);
            if (cancellationToken.IsCancellationRequested)
                return;

            if (alreadyOpen)
            {
                template = baseForm.Template;
                lock (_activeDocumentChangedSubscriptionLock)
                {
                    dockingPanel.ActiveContentChanged -= new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                    baseForm.Activate();
                    Messenger.Send(Messenger.Events.ActivateContentInDockPanel, baseForm);
                    dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                }
                var workMode = openInDesign ? WorkMode.Design : WorkMode.Production;
                if (workMode == WorkMode.Design)
                {
                    var designForm = (FormTemplateDesign)baseForm;
                    DesignModeChanged(MapDesignDockerStateToRibbonUiStateDesignMode(designForm.DockerState));
                }
                else
                    workModeSwitched(workMode);
            }
            else
            {
                currentTemplateLayoutSizes.ClearControls();
                signChildWindow.ResetTemplate();

                if (templateType == TreeIDEnumaration.Template)
                {
                    var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);
                    (template, encTemplate) = await ReadTemplateAsync(templateGuid, templateController, metaController, cancellationToken);
                }
                else if (templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.PrintshopCustomer || templateType == TreeIDEnumaration.PrintshopServer || templateType == TreeIDEnumaration.CampaignRecording)
                {
                    (template, encTemplate) = await ReadQueuedTemplateAsync(templateGuid, templateType, queueController, metaController, cancellationToken);
                }
                else if (templateType == TreeIDEnumaration.Flow)
                {
                    (template, encTemplate) = await ReadFlowSignAsync(templateGuid, templateData, cancellationToken);
                }
                if (template == null) return;

                (signChildWindow as FormSignProduction).ShowingPrinterQueueTemplate = templateType == TreeIDEnumaration.PrinterQueue;
                (signChildWindow as FormSignProduction).ShowingFlowQueueTemplate = templateType == TreeIDEnumaration.Flow;

                //Change which sign is displayed in the production area

                await UpdateSaveOnExitFlagAsync(templateGuid, templateType, cancellationToken);
                signChildWindow.TreeIDEnumaration = templateType;

                if (cancellationToken.IsCancellationRequested) return;
                // Casting the signChildWindow because the async version of the method only exists in 
                await ((FormSignProduction)signChildWindow).SetTemplateAsync(template, encTemplate, cancellationToken);

                if (cancellationToken.IsCancellationRequested)
                    return;

                //Decide if the user CAN edit the template or not (controls whether the InfoDocker is enabled or not)
                if (!openInDesign)
                {
                    var form = signChildWindow as FormSignProduction;
                    await form.SetupTemplateInfoAsync(cancellationToken);
                }
                if (cancellationToken.IsCancellationRequested)
                    return;
                UpdateUIForOpenedTemplate(templateType, openInDesign);
            }
            if (templateType == TreeIDEnumaration.Template || templateType == TreeIDEnumaration.CampaignRecording)
            {
                if (!keepOutputSizes)
                {
                    if (openInDesign)
                        currentTemplateLayoutSizes.SetThumbSizeForComingTemplate((baseForm as FormTemplateDesign).WantedThumbSize);
                    currentTemplateLayoutSizes.SetFormats(template, openInDesign);
                }
                else
                    currentTemplateLayoutSizes.SetFormats(template, false);
                if (openInDesign)
                {
                    var format = template.GetCurrentOutputFormat();
                    dsDocker.SelectSlideAnimations(format.SignAnimSettings);
                    dsDocker.SetTimeslotUsage(template.GetUsedFlowTimeslots());
                    var ftd = (baseForm as FormTemplateDesign);
                    var animationsOfSelection = ftd.GetAnimationsOfSelection();
                    Messenger.Send(ftd, Messenger.Events.BeginTemplateEditing, animationsOfSelection);
                    stack.ClearStack();
                    stack.AddElementsToStack(baseForm.GetStacked());
                }
            }
            else if (!keepOutputSizes && templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.PrintshopCustomer)
            {
                queueTemplateLayoutSizes.SetFormats(template, openInDesign);
            }
            else if (!keepOutputSizes && templateType == TreeIDEnumaration.PrintshopServer)
            {
                queueTemplateLayoutSizes.SetFormats(template, openInDesign, true);
            }
        }

        private async Task UpdateSaveOnExitFlagAsync(Guid templateGuid, TreeIDEnumaration templateType, CancellationToken ct)
        {
            //Only allow saving queued signs when they are closed
            signChildWindow.SaveOnExit = false;
            if (templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.PrintshopCustomer || templateType == TreeIDEnumaration.CampaignRecording)
            {
                signChildWindow.SaveOnExit = (await QueueController.GetStateAsync(templateGuid, QueuedOutputStateEnumaration.Queued, ct)) ||
                    await QueueController.GetStateAsync(templateGuid, QueuedOutputStateEnumaration.Campaign, ct);
            }
            else if (templateType == TreeIDEnumaration.Flow)
                signChildWindow.SaveOnExit = true;
        }

        private async Task<(Template template, EncapsulatedTemplate encTemplate)> ReadFlowSignAsync(Guid guid, byte[] templateData, CancellationToken ct)
        {
            var flowCapsule = await DSController.GetSignAsync(guid, ct);
            if (flowCapsule != null)
                await DSController.FillSignAsync(flowCapsule, ct);
            var template = await DSController.ConvertToTemplateAsync(flowCapsule, ct);
            if (template == null && templateData == null)
                return (null, null);   //TODO: The template failed to instantiate
            else if (template == null)
                template = await TemplateController.GetTemplateAsync(templateData, ct);

            var encTemplate = new EncapsulatedTemplate() { Guid = guid, Name = "Flowsign" };
            return (template, encTemplate);
        }

        private async Task<(Template template, EncapsulatedTemplate encTemplate)> ReadQueuedTemplateAsync(Guid guid, TreeIDEnumaration templateType, QueueController queueController, MetaController metaController, CancellationToken ct)
        {
            var template = await queueController.GetTemplateAsync(guid, _productSearchStateFilter?.PriceDate, ct);
            if (template == null)
                return (null, null);   //TODO: The template failed to instantiate
            if (templateType == TreeIDEnumaration.CampaignRecording)
            {
                var (metaProduct, metaPictures) = await metaController.GetMetaValueProductAsync(ct);
                template.UpdateMetaValueProduct(metaProduct, metaPictures);
            }
            var encTemplate = QueueController.GetEncapsulatedTemplate(guid);
            return (template, encTemplate);
        }

        private async Task<(Template, EncapsulatedTemplate)> ReadTemplateAsync(Guid guid, TemplateController templateController, MetaController metaController, CancellationToken ct)
        {
            var template = await templateController.GetTemplateAsync(guid, ct);
            if (template == null)
                return (null, null);   //TODO: The template failed to instantiate
            var (metaProduct, metaPictures) = await metaController.GetMetaValueProductAsync(ct);
            await Task.Run(() => template.UpdateMetaValueProduct(metaProduct, metaPictures), ct);
            var encTemplate = await TemplateController.GetEncapsulatedTemplateAsync(guid, ct);
            return (template, encTemplate);
        }

        private void UpdateUIForOpenedTemplate(TreeIDEnumaration templateType, bool openInDesign)
        {
            lock (_activeDocumentChangedSubscriptionLock)
            {
                // Show template window and hide folder browser
                dockingPanel.ActiveContentChanged -= new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                Messenger.Send(Messenger.Events.ActivateContentInDockPanel, signChildWindow);
                Messenger.Send(Messenger.Events.RemoveContentInDockPanel, browseFoldersAndTemplatesDock);
                dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
            }

            if (signChildWindow is FormSignProduction sign
                && (templateType == TreeIDEnumaration.PrinterQueue || templateType == TreeIDEnumaration.Flow))
            {
                sign.DesignPermission = FormSignProduction.Permission.None;
            }

            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer)) // we don't manage workModes as printshopServer, also DOckerTags is not initialized.
            {
                workModeSwitched(openInDesign ? WorkMode.Design : WorkMode.Production);
            }
        }

        void PictureChangeRequested(Guid guid)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            using (var dalCM = Embedded.Factory.ChangedMaterial)
            {
                if (dalCM.IsChangedItem(guid))
                {
                    List<GuidParentGuid> itemsToRefresh = AvailabilityController.ResetChanged(guid);
                    itemsToRefresh.RemoveAt(0);     //The first item in the list is the picture which is not a tree node and should not be added to the tree
                    foreach (var item in itemsToRefresh)
                    {
                        pictureSearch.PictureTree.AddContent(new ItemChangeInfo()
                        {
                            ChangeType = ItemChangeType.NewlyChanged,
                            ParentGuid = item.ParentGuid,
                            ItemGuid = item.ItemGuid,
                            IsNewlyChanged = false
                        });
                    }
                    var ilviPicture = pictureSearch.PicturesImageListView.Items.Where(i => (Guid)i.VirtualItemKey == guid).FirstOrDefault();
                    if (ilviPicture != null)
                        ilviPicture.NewlyChanged = false;
                }
            }
            if (!(dockingPanel.ActiveContent is FormTemplateBase)) return;
            FormTemplateBase activeForm = (FormTemplateBase)dockingPanel.ActiveContent;
            activeForm.ChangePicture(guid);
        }

        void EnsureTreeVisibility(TreeIDEnumaration treeId)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "EnsureTreeVisibility Entering", "V");

            if (treeId == TreeIDEnumaration.Template)
            {
                if (tabControlContent.SelectedTab != tabPageTemplates)
                {
                    try
                    {
                        TemplateSearchResult.DisableAutoSemaphore.WaitOne();
                        templateSearch.TemplateSearchResult.DisableAutoSearch();
                        tabControlContent.SelectedTab = tabPageTemplates;
                        templateSearch.TemplateSearchResult.EnableAutoSearch();
                    }
                    finally
                    {
                        TemplateSearchResult.DisableAutoSemaphore.Release();
                    }
                }
                
                currentTree = templateSearch.TemplateTree;
                currentNode = templateSearch.TemplateTree.SelectedNode;
                if (currentNode == null)
                {
                    if (templateSearch.TemplateTree.Root.Children.Count > 0)
                    {
                        templateSearch.TemplateTree.SelectionChanged -= TemplateTree_SelectionChangedAsync;
                        currentNode = templateSearch.TemplateTree.SelectedNode = templateSearch.TemplateTree.Root.Children[0];
                        templateSearch.TemplateTree.SelectionChanged += TemplateTree_SelectionChangedAsync;
                    }
                }
            }
            else if (treeId == TreeIDEnumaration.CampaignRecording)
            {
                if (tabControlContent.SelectedTab != tabPageTemplates)
                {
                    try
                    {
                        TemplateSearchResult.DisableAutoSemaphore.WaitOne();
                        templateSearch.TemplateSearchResult.DisableAutoSearch();
                        tabControlContent.SelectedTab = tabPageTemplates;
                        templateSearch.TemplateSearchResult.EnableAutoSearch();
                    }
                    finally
                    {
                        TemplateSearchResult.DisableAutoSemaphore.Release();
                    }
                }

                currentTree = templateSearch.CampaignTree;
                currentNode = templateSearch.CampaignTree.SelectedNode;
                if (currentNode == null)
                {
                    if (templateSearch.CampaignTree.Root.Children.Count > 0)
                    {
                        templateSearch.CampaignTree.SelectionChanged -= CampaignTree_SelectionChangedAsync;
                        currentNode = templateSearch.CampaignTree.SelectedNode = templateSearch.CampaignTree.Root.Children[0];
                        templateSearch.CampaignTree.SelectionChanged += CampaignTree_SelectionChangedAsync;
                    }
                }
            }
            else if (treeId == TreeIDEnumaration.Picture)
            {
                tabControlContent.SelectedTab = tabPagePictures;
                currentTree = pictureSearch.PictureTree;
                currentNode = pictureSearch.PictureTree.SelectedNode;
                if (currentNode == null)
                {
                    if (pictureSearch.PictureTree.Root.Children.Count > 0)
                    {
                        pictureSearch.PictureTree.SelectionChanged -= pictureTree_SelectionChanged;
                        currentNode = pictureSearch.PictureTree.SelectedNode = pictureSearch.PictureTree.Root.Children[0];
                        pictureSearch.PictureTree.SelectionChanged += pictureTree_SelectionChanged;
                    }
                }
            }
            else if (treeId == TreeIDEnumaration.PrinterQueue)
            {
                tabControlContent.SelectedTab = tabPageOutputs;
                currentTree = printTree;
                currentNode = printTree.SelectedNode;
                if (currentNode == null)
                {
                    if (printTree.Root.Children.Count > 0)
                    {
                        printTree.SelectionChanged -= printTree_SelectionChanged;
                        currentNode = printTree.SelectedNode = printTree.Root.Children[0];
                        printTree.SelectionChanged += printTree_SelectionChanged;
                    }
                }
            }
            else if (treeId == TreeIDEnumaration.Flow)
            {
                tabControlContent.SelectedTab = tabPageDS;
                currentTree = null;
                currentNode = null;
            }
            // Startpage has this state.
            else if (treeId == TreeIDEnumaration.Unknown)
            {
                currentTree = null;
                currentNode = null;
            }

            EnsureGuiVisibilityBasedOnNode(currentTree, currentNode);
        }

        /// <summary>
        /// THE METHOD LOOP THROUGH ALL OPEN TEMPLATES AND CHECK IF THE CURRENT TEMPLATE IS OPEN
        /// </summary>
        /// <param name="guid">The guid of the template tocheck</param>
        /// <param name="template">Template object to set if found</param>
        /// <param name="alreadyOpen">out value which indicates if template is already open</param>
        /// <param name="openInDesign">out value which indicates if template is open in design</param>
        private void TryGetOpenTemplate(Guid templateGuid, out FormTemplateBase baseForm, out bool alreadyOpen, out bool openInDesign)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            alreadyOpen = false;
            openInDesign = false;
            baseForm = null;
            if (dockingPanel != null)
            {
                foreach (var dockedableContent in dockingPanel.DockedContent)
                {
                    var dockedControlOrForm = dockedableContent.GetControlOrForm();
                    if (dockedControlOrForm is FormTemplateBase)
                    {
                        var templateCapsule = ((FormTemplateBase)dockedControlOrForm).GetTemplateCapsule();
                        if (templateCapsule == null)
                            break;

                        System.Guid tg = templateCapsule.Guid;
                        if (tg == templateGuid)
                        {
                            baseForm = ((FormTemplateBase)dockedControlOrForm);
                            alreadyOpen = true;
                            openInDesign = dockedControlOrForm is FormTemplateDesign;
                            break;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// OpenTemplateInDesign
        /// </summary>
        Control  OpenTemplateInDesign(Guid templateGuid)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if (templateGuid == Guid.Empty)
                return null;

            var metaController = new MetaController(AppData.Instance);
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);

            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design))
                throw new ApplicationException("You're not allowed to open a template in design without AddOn.Design");

            Template template = templateController.GetTemplateUnCashed(templateGuid);
            var (metaProduct, metaPictures) = metaController.GetMetaValueProduct();
            template.UpdateMetaValueProduct(metaProduct, metaPictures);

            EncapsulatedTemplate encTemplate = TemplateController.GetEncapsulatedTemplate(templateGuid);
            FormTemplateDesign designChildWindow = new FormTemplateDesign(tags, dsDocker);
            designChildWindow.VisibleChanged += new EventHandler(SetOutputFormats);
            designChildWindow.FormClosing += new FormClosingEventHandler(SetOutputFormats);
            designChildWindow.Saved += new EventHandler(designChildWindow_Saved);
            designChildWindow.BeginProductSearch += new FormTemplateBase.BeginProductSearchHandler(designChildWindow_BeginProductSearch);
            designChildWindow.BeginBrowseToPicture += new FormTemplateBase.BeginBrowseToPictureHandler(designChildWindow_BeginBrowseToPicture);
            designChildWindow.SelectBrowseToPicture += new EventHandler(formTemplateBase_SelectBrowseToPicture);
            designChildWindow.EndBrowseToPicture += new EventHandler(signChildWindow_EndBrowseToPicture);
            designChildWindow.SurfaceClicked += new EventHandler(signChildWindow_SurfaceClicked);
            designChildWindow.UndoRedo += new EventHandler(designChildWindow_UndoRedo);
            designChildWindow.FormClosed += new FormClosedEventHandler(designChildWindow_FormClosed);
            designChildWindow.InformationChangedByUser += new EventHandler<InformationChangedByUserEventArgs>(formTemplateBase_InformationChangedByUser);
            designChildWindow.InputTagChanged += new EventHandler<TagChangedEventArgs>(designChildWindow_InputTagChanged);
            designChildWindow.TagVisibilityChanged += new EventHandler<TagVisibilityChangedEventArgs>(designChildWindow_TagVisibilityChanged);
            designChildWindow.UnStackComplete += new EventHandler(designChildWindow_UnStackComplete);
            designChildWindow.SetTemplate(template, encTemplate);
            designChildWindow.TreeIDEnumaration = TreeIDEnumaration.Template;
            designChildWindow.TopLevel = false;
            designChildWindow.ColorsAltered += DesignChildWindow_ColorsAltered;
            designChildWindow.VideoUrlAnimationAdded += new EventHandler<SetAnimationEventArgs>(SetVideoUrlAnimationForVideoFile);


            templateController.IncreaseTemplateCounter(TemplateCounter.TemplateOpenedInDesign, encTemplate.Guid);

            //ToolStripManager.Merge(designChildWindow.ToolStripToJoin(), toolStrip1);
            stack.ClearStack();
            stack.AddElementsToStack(designChildWindow.GetStacked());
            
            // Show design surface and hide production surface
            // Disable Active document change event
            lock (_activeDocumentChangedSubscriptionLock)
            {
                dockingPanel.ActiveContentChanged -= new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);

                // Hide the sign production surface if opening template was in production mode
                if (signChildWindow != null && signChildWindow.GetTemplateCapsule() != null && signChildWindow.GetTemplateCapsule().Guid == encTemplate.Guid)
                {
                    Messenger.Send(Messenger.Events.RemoveContentInDockPanel, signChildWindow);
                    Messenger.Send(Messenger.Events.ActivateContentInDockPanel, browseFoldersAndTemplatesDock);
                }
                // Show design surface
                dockingPanel.ShowContent(designChildWindow);
                workModeSwitched(WorkMode.Design);
                designChildWindow.UpdateExitDesignButtonTabColor();
                // Restore Active document change event
                dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
            }

            designChildWindow.Focus();
            Messenger.Send(Messenger.Events.ActivateContentInDockPanel, designChildWindow);
            currentTemplateLayoutSizes.SetThumbSizeForComingTemplate(designChildWindow.WantedThumbSize);
            currentTemplateLayoutSizes.SetFormats(template, true);
            designChildWindow.BeginImageLoad();
            workModeSwitched(WorkMode.Design);

            var templateDesignDoc = dockingPanel.ActiveContent as FormTemplateDesign;
            templateDesignDoc.ExitDesignClicked += templateDesignDoc_ExitDesignClicked;
            Messenger.Register(Messenger.Events.ExitDesign, messenger_OnExitDesign);
            
            return designChildWindow;
        }

        private void DesignChildWindow_ColorsAltered(object sender, ColorsAlteredEventArgs e)
        {            
            foreach(var form in dockingPanel.DockedContent)
            {
                if (form is FormTemplateDesign designForm)
                    designForm.UpdateColors(e);
            }
            new TemplateController(AppData.Instance, TemplateCacheInstance.Instance).ReplaceColorInCashedTemplates(e.colorsToAlter);
            TemplateController.ClearTemplateThumbsContainingColor(e.colorsToAlter.Select(c => c.Guid).ToList());
            refreshTemplateContentView();
        }

        void messenger_OnExitDesign(MessagePayload payload)
        {
            dockingPanel.RemoveContent(payload.Get<IDockableContent>());

            if (tabControlContent.SelectedTab != tabPageTemplates)
            {
                tabControlContent.SelectedTab = tabPageTemplates;
            }
        }

        private void ExitDesign(IDockableContent sender)
        {            
            dockingPanel.CloseContent(sender);
        }

        void Messenger_OnNavigationCloseClicked(MessagePayload payload, EventArgs args)
        {
            ExitDesign(payload.Get<IDockableContent>());
        }

        void templateDesignDoc_ExitDesignClicked(object sender, EventArgs e)
        {
            if (dockingPanel.ActiveContent != sender)
                throw new ArgumentException("Sender is always expected to be same as ActiveDocument");
            ExitDesign((IDockableContent)sender);
        }

        void designChildWindow_TagVisibilityChanged(object sender, TagVisibilityChangedEventArgs e)
        {
            infoDocker.ResolveTagVisibilityChanged(e);
        }

        void designChildWindow_InputTagChanged(object sender, TagChangedEventArgs e)
        {
            infoDocker.ResolveChangedTag(e);
        }

        private void RefreshPictureOnTemplates(List<Guid> templateGuids, Guid pictureGuid, bool replaceWithEmpty = false)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<List<Guid>, Guid, bool>(RefreshPictureOnTemplates), new object[] { templateGuids, pictureGuid, replaceWithEmpty });
            }
            else
            {
                //update picture on signproductiojn surface
                if (signChildWindow != null && signChildWindow.GetTemplateCapsule() != null && templateGuids.Contains(signChildWindow.GetTemplateCapsule().Guid))
                {
                    if (replaceWithEmpty)
                        signChildWindow.ReplacePictureOnSurface(pictureGuid, Guid.Empty);
                    else
                        signChildWindow.RefreshPictureOnSurface(pictureGuid);
                }

                //update all design open templates
                FormTemplateBase baseForm;
                //update all design surfaces
                foreach (var content in dockingPanel.DockedContent)
                {
                    if (content is FormTemplateBase)
                    {
                        Guid tg = ((FormTemplateBase)content).GetTemplateCapsule().Guid;
                        if (templateGuids.Contains(tg))
                        {
                            baseForm = ((FormTemplateBase)content);
                            if (replaceWithEmpty)
                                baseForm.ReplacePictureOnSurface(pictureGuid, Guid.Empty);
                            else
                                baseForm.RefreshPictureOnSurface(pictureGuid);
                            formTemplateBase_InformationChangedByUser(baseForm, new InformationChangedByUserEventArgs());
                        }
                    }
                }
            }
        }

        private void ClearPictureOnTemplates(List<Guid> templateGuids, Guid pictureGuid)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            if (this.InvokeRequired)
                this.Invoke(new Action<List<Guid>, Guid>(ClearPictureOnTemplates), new object[] { templateGuids, pictureGuid });
            else
                RefreshPictureOnTemplates(templateGuids, pictureGuid, replaceWithEmpty: true);
        }

        private string[] ShowImportMediaDialog(MediaImportType mediaType)
        {
            Trace.WriteLineIf(traceSwitch.TraceVerbose, "Entering", "V");
            string[] fileNames = new string[0];

            //TODO GET THE FILTER FROM PICTURE CONTROLLER FOR  THE AVAILABLE PICTURE FORMATS
            var mediaFilter = new MediaFilter(AppData.Instance.CurrentUser);
            openFileDialogImportPicture.Filter = mediaFilter.GetSupportedMediaFilter(mediaType);
            openFileDialogImportPicture.Multiselect = _allowedMultiplePicImport;

            DialogResult dr = openFileDialogImportPicture.ShowDialog();
            if (dr == DialogResult.OK)
                fileNames = openFileDialogImportPicture.FileNames;

            openFileDialogImportPicture.FileName = string.Empty;
            return fileNames;
        }

        private IEnumerable<IMediaSource> ShowImportVideoDialog()
        {
            using (var importForm = new FormVideoImport(_allowedMultiplePicImport))
            {
                importForm.StartPosition = FormStartPosition.CenterParent;
                importForm.ShowDialog();
                return importForm.FilesToImport;
            }
        }

        /// <summary>
        /// Check filename
        /// </summary>
        /// <param name="fileNames">Array of Filenames containing Full Path and Name</param>
        /// <returns>A List of all filenames failed</returns>
        private List<string> validatePictureFilenames(IEnumerable<string> fileNames)
        {
            var importedFileNames = new List<string>();
            if (fileNames == null)
                return importedFileNames;

            var pictureToProcess = new Dictionary<PictureValidationResult, string>();
            foreach (string filename in fileNames)
                pictureToProcess.Add(PictureValidator.FilenameAndPath(filename, AppData.Instance.CurrentUser) as PictureValidationResult, filename);

            var failedImages = pictureToProcess.Where(ptr => !ptr.Key.IsValid && !ptr.Key.Invalids.TrueForAll(message => message == PictureValidator.MessageTypes.FileSizePngTooLarge || message == PictureValidator.MessageTypes.FileSizeTooLarge)).Select(ptr => ptr.Value);
            if (failedImages.Count() > 0)
                MessageBoxes.PictureImportFailed(failedImages, new Form() { TopMost = true });
            var ImagesToConvertCount = pictureToProcess.Count(ptr => !ptr.Key.IsValid && (ptr.Key.Invalids.Contains(PictureValidator.MessageTypes.FileSizeTooLarge) || ptr.Key.Invalids.Contains(PictureValidator.MessageTypes.FileSizePngTooLarge)));
            int ImagesToConvertProcessed = 0;
            DialogResult? useResultForAll = null;
            foreach (var picture in pictureToProcess)
            {
                var result = picture.Key;
                var filename = picture.Value;

                if (result.IsValid)
                    importedFileNames.Add(filename);
                else
                {
                    // Determine proper Feedback of why picture import Failed
                    if (result.Invalids.Contains(PictureValidator.MessageTypes.FileSizeTooLarge) ||
                        result.Invalids.Contains(PictureValidator.MessageTypes.FileSizePngTooLarge))
                    {
                        DialogResult dialogResult;
                        if (!useResultForAll.HasValue)
                        {
                            using (var convertForm = new PictureConvertDialogForm(filename, ImagesToConvertCount - ImagesToConvertProcessed))
                            {
                                convertForm.StartPosition = FormStartPosition.CenterParent;
                                dialogResult = convertForm.ShowDialog();

                                if (convertForm.UseResultForAll)
                                    useResultForAll = convertForm.DialogResult;
                            }
                            ImagesToConvertProcessed++;
                        }
                        else
                            dialogResult = useResultForAll.Value;
                        if (dialogResult == System.Windows.Forms.DialogResult.Yes)
                            importedFileNames.Add(filename);
                        else if (dialogResult == System.Windows.Forms.DialogResult.Cancel)
                            break;
                    }
                }
            }
            return importedFileNames;
        }

        #endregion

        protected override void WndProc(ref Message m)
        {
            var handled = MainWmProcessor.ProcessMessage(ref m);
            if (!handled)
                base.WndProc(ref m);
        }
    }
}
