using Aga.Controls.Tree;
using Mediablob.Objects.Public;
using Shoppa.Controllers;
using Shoppa.Controllers.Destination;
using Shoppa.Controllers.Helpers;
using Shoppa.Controllers.Sync;
using Shoppa.DataLayer.Embedded;
using Shoppa.DataLayer.Server;
using Shoppa.DPI;
using Shoppa.Objects;
using Shoppa.Objects.Common;
using Shoppa.Objects.Common.Capsules;
using Shoppa.Objects.ProductSearch;
using Shoppa.Objects.TemplateObjects;
using Shoppa.Objects.TemplateObjects.Output;
using Shoppa.Objects.TemplateObjects.Output.Destinations;
using Shoppa.PL.Windows.Controls;
using Shoppa.PL.Windows.Controls.Docking;
using Shoppa.PL.Windows.Controls.LeftPanelControls;
using Shoppa.PL.Windows.Controls.ProductSearch;
using Shoppa.PL.Windows.Controls.TemplateRibbonPanel;
using Shoppa.PL.Windows.Controls.TitleBar;
using Shoppa.PL.Windows.Forms.ModernFormComponents;
using Shoppa.PL.Windows.Forms.TemplateImport;
using Shoppa.PL.Windows.Printing;
using Shoppa.PL.Windows.Theme;
using Shoppa.PL.Windows.RealTime;
using Shoppa.PL.Windows.Pda;
using Shoppa.Plugins.Esl;
using Shoppa.Rendering;
using Shoppa.WinApi;
using ShoppaAB.Forms;
using ShoppaAB.Imaging;
using ShoppaAB.Objects;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.IO.Pipes;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Shoppa.Objects.Extensions;
using System.Windows.Input;
using TreeParts;
using Cursors = System.Windows.Forms.Cursors;
using KeyEventArgs = System.Windows.Forms.KeyEventArgs;
using KeyEventHandler = System.Windows.Forms.KeyEventHandler;
using MouseEventArgs = System.Windows.Forms.MouseEventArgs;
using MouseEventHandler = System.Windows.Forms.MouseEventHandler;
using Shoppa.Objects.Media;
using Shoppa.Rendering;
using CancellationToken = System.Threading.CancellationToken;
using static Shoppa.Objects.TreeMenuEventArgs;
using TreeParts.Picture;
using TreeParts.CampaignRecording;
using Shoppa.PL.Windows.Telemetry;

namespace Shoppa.PL.Windows.Forms
{
    public partial class MainForm : ModernForm
    {
        #region Fields
        private const string TEMPLATE_FILE_DIALOG_FILTER = "Shoppa Template|*.shte";
        private const int OEM_OKI_DPI_CHOISE = 2; // 200 dpi
        private const int OPEN_DPI_SELECTION_DIALOGUE = 4;
        private const string SAVE_AS_PICTURE_DEFAULT_FILTER = "PNG Image|*.png|JPEG Image|*.jpg;*.jpeg|Bitmap Image|*.bmp;*.dib|TIFF Image|*tif;*.tiff;";
        private const string SAVE_AS_PICTURE_PDF_FILTER = "PDF Document|*.pdf";
        private const int FIND_ANY_PRINTER_TIMEOUT = 4 * 1000;

        private FormTemplateBase signChildWindow;
        private DockableContent browseFoldersAndTemplatesDock;
        private ContentView templatesImageListView;
        private DockerStack stack;
        private DockerTags tags;
        private DockerPurpose purposes;
        private DockerInfoDesign infoDocker;
        private DockerPrintshopInfo printshopInfo;
        private DockerDS dsDocker;
        public DockerSearchReplace searchAndReplaceDocker;
        private int printshopTeaserCounter;
        private OutputBarQueueModes outputBarMode = OutputBarQueueModes.Template;
        private WorkMode currentWorkMode = WorkMode.Production;
        private FormLoadingScreen loadingScreen = null;
        private TreeNodeAdv currentNode;
        private TreeViewAdv currentTree;
        private bool isNewKeyPress = true;
        private static object fillPictureWaitingListLock = new object();
        private static List<ProductPicture> fillPictureWaitingList;
        private static bool isWorkingFillPictureWaitingList;
        private object _activeDocumentChangedSubscriptionLock = new object();
        private VisibilityDetailsFanout visibilityDetailsFanout;
        private RecordingHoverToolstrip recordingHoverToolstrip;
        private Color splitter1ExitIconColor = Themes.Current.Colors.ExitDesignButtonColor;
        private Color splitter1RibbonActiveBackColor = Themes.Current.Colors.ExitDesignButtonColor;
        private bool _shouldAbortMainFormClosing;
        private bool _formIsClosing = false;
        private StartPageBox _startpageFanout = null;
        private WebBrowserFanout _printshopFanout = null;
        private Guid _dependencyToken = Guid.NewGuid();
        public Scrollholder _scrollholderSearchAndReplace;
        private const int RIGHT_PANEL_TOP_OFFSET = 9;
        private const int ONE_DAY_IN_MILLISECONDS = 24 * 60 * 60 * 1000;
        private const int RECORDING_HOVER_TOOLSTRIP_HEIGHT = 25;
        private const int VISIBILITY_DETAILS_FANOUT_HEIGHT = 300;
        private const int MAX_IN_MEMORY_PRODUCTS_DURING_PRINT = 10_000;
        private System.Threading.Timer _midNightTrigger;
        private FormEslSendFailed _eslSendFailedWarn;
        private AutoSignagePrintingProcessor _autoSignagePrintingProcessor;
        private bool saveWindowState = false;
        FormAutoSignage _curFormAutoSignage;
        private CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
        #endregion

        public readonly List<(string Printer, Task<bool> Task)> PrinterPrerequisitesTasks = new List<(string Printer, Task<bool> Task)>();

        private readonly IPrintClient printClient;
        private readonly PdaSignagePrintingProcessor pdaSignagePrintingProcessor;

        private CampaignRecordingTreeModel CampaignModel
        {
            get
            {
                return templateSearch.CampaignTree.Model as CampaignRecordingTreeModel;
            }
        }

        public LazyMediaTypedDestinationHolder MediaTypedDestinationHolder { get; private set; }

        private static readonly AddOnsEnumeration[] QueueSheetAddOns = {
            AddOnsEnumeration.ShoppaOkey,
            AddOnsEnumeration.QueueSheet };

        private enum WorkMode
        {
            Production,
            Design
        };

        private MainForm() { }
        public MainForm(FormLoadingScreen loadingScreen)
        {
            this.loadingScreen = loadingScreen;
            this.loadingScreen.Disposed += (sender, e) =>
            {
                this.loadingScreen = null;
            };

            _eslSendFailedWarn = new FormEslSendFailed();
            Messenger.Register(Messenger.Events.EslSendFailed, (payload) => UpdateEslFailedPopUp(payload.Get<bool>()));

            initializeOpenConnectionProblemHandler();
            InitializeSyncManager();
            Messenger.Register(Messenger.Events.FormTemplateDesignLoad, (payload) => { SplitContainerTemplateTab_Resize(); });
            Messenger.Register(Messenger.Events.ExitButtonStateChanged, (payload) =>
            {
                var kvp = payload.Get<KeyValuePair<Color, Color>>();
                RecolorSplitterAndActiveDocument(kvp.Value, kvp.Key);
            });
            Messenger.Register(Messenger.Events.NavigationTabUpdated, (payload) => { OnNavigationTabUpdated(); });
            Messenger.Register(Messenger.Events.AddEditLayout, (payload) => { Messenger_AddOrEditLayout(); });
            Messenger.Register(_dependencyToken, Messenger.Events.ReloadTrees, (sender, payload) => { RefreshTreesAndContentViews(); });
            Messenger.Register(Messenger.Events.InvokeStoreGroupFilterDialog, (payload) => { ShowStoreGroupFilterDialog(); });
            Messenger.Register(Messenger.Events.ClearStoreGroupFilter, (payload) => { ClearStoreGroupFilter(); });

            _eslRenderJobProcessor = new Esl.EslRenderJobProcessor(new Controllers.Plugins.PluginController(AppData.Instance).GetPlugin<IEslProvider>());
            _autoSignagePrintingProcessor = new AutoSignagePrintingProcessor();
            _autoSignagePrintingProcessor.PrintingPDF += hotfolderWatcher_HotFolderPrintingPDF;
            _autoSignagePrintingProcessor.QueueSign += _autoSignagePrintingProcessor_QueueSign;

            UpdateRegionBasedOnWindowState();

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Luna))
            {
                printClient = new PrintClient(AppData.Instance);

                var templateInformationHandler = new TemplateInformationHandler(AppData.Instance);
                pdaSignagePrintingProcessor = new PdaSignagePrintingProcessor(printClient, templateInformationHandler, _syncmanager, AppData.Instance);
            }

            Microsoft.Win32.SystemEvents.SessionSwitch += SystemEvents_SessionSwitch;
        }

        private void OnNavigationTabUpdated()
        {
            ControlHelper.SuspendDrawing(this);
            RecolorSplitterAndActiveDocument(splitter1ExitIconColor, splitter1RibbonActiveBackColor);
            UpdateRightDocker();
            ControlHelper.ResumeDrawing(this);
            ReloadDesignInfo();
            StopPlayingDesignModePreview();
        }

        private void _autoSignagePrintingProcessor_QueueSign(object sender, PrintSignOrderEventArgs e)
        {
            var queuedSign = new QueueController(AppData.Instance).AddToQueue(e.Copies, e.Sign, e.Name, QueuedOutputStateEnumaration.PrinterArchive, e.Timestamp);
            printTree.AddSignsToArchive(queuedSign);
            displayPrinterArchive();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            WindowStateController.ApplySettings(this);
            saveWindowState = true;

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Luna))
                Task.Run(() => printClient.StartConnectionWithRetryAsync());
            else
                Trace.WriteLineIf(traceSwitch.TraceInfo, "Real-time connection is not started for the non-Luna user", "I");
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            // Start the message loop
            _syncmanager.Start();    //Start syncronization thread
            nudgeSplitter2ToMakeItResizable();

            if (Server.Factory.OfflineMode)
                Trace.WriteLineIf(traceSwitch.TraceWarning, "Shoppa is in OfflineMode", "W");

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
            {
                printTree.SelectedNode = printTree.Root.Children.First(); // not printed folder
                Messenger.Send(Messenger.Events.ActivateContentInDockPanel, browseFoldersAndTemplatesDock);
            }

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ShoppaOkey))
            {
                new WelcomeToShoppaGoForm { Owner = this }.ShowDialog();
            }
        }

        protected void OnMouseMove(Object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                DialogTitleBar.SendMessageNclButtonDown(Handle);
            }
        }

        public bool Initialize()
        {
            // If splash-window is manually killed by user it will be null here
            if (loadingScreen == null)
                return false;

            loadingScreen.StepForward("Check printer bindings");
            // move printer bindings to the new table if there are any. Removes them after.
            new OutputController(AppData.Instance).UpgradePrinterBindingsIfNeeded();
            // Re-check printer matchText, important for finalDestination upgrade
            new PrinterSettingsController(AppData.Instance).ReCheckPrinterMatchText();

            var psc = new PrinterSettingsController(AppData.Instance);
            psc.ImportPrintersFromConfig();
            // Re-check printers availability
            psc.FilterPrinters();
            psc.ImportFormatBindingFromConfig();

            loadingScreen.StepForward("Updating campaigns");
            new StoreInfoController(AppData.Instance).UpdateAvailabilities();
            loadingScreen.StepForward("");

            var saveToRegistry = AppData.Instance.LoginMethod == LoginMethod.Standard 
                                    || AppData.Instance.LoginMethod == LoginMethod.OpenID;
            CustomerController.SaveServerCredentialsLocally(AppData.Instance.CurrentUser, AppData.Instance.DisplayName, AppData.Instance.LoginMethod, saveToRegistry);

            loadingScreen.StepForward("Starting Shoppa");

            CustomerController.IncreaseStartupCounter();
            // Instantiate MLS it is instantiated with default english language
            MLS.Instance.SetMLSLanguage(LanguageController.LoadGuiLanguage(AppData.Instance.CurrentUser));

            InitializeComponent();
            dockerProductSearch.SetupIgnoreOnClick(titleBarStrip1);
            this.ClientSize = new Size(1300, 900);
            this.Icon = Properties.Resources.ppa3_Icon;
            ThemeUtil.ApplyTo(this);


            tabControlContent.TabBackColor = ColorTranslator.FromHtml("#2a3d49");
            tabControlContent.BorderColor = ColorTranslator.FromHtml("#576975");
            tabControlContent.SelectedTabColor = ColorTranslator.FromHtml("#3a6177");
            tabControlContent.HoverColor = ColorTranslator.FromHtml("#39566a");

            SetupTreeComponent();
            RemoveUnnecessaryCampaignTreeScrollbars();
            #region Docker Initialization

            dockerProductSearch.Initialize(ProductCache);

            Messenger.Register(Messenger.Events.MenuStripItemClicked, (e) => MenuStripItemsClicked(e, EventArgs.Empty));
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
            {
                printshopInfo = new DockerPrintshopInfo();
                tabControlContent.TabPages.Remove(tabPageTemplates);
                tabControlContent.TabPages.Remove(tabPagePictures);

                panelDockers.Controls.Clear();
                panelDockers.Controls.Add(printshopInfo);
                dockerProductSearch.SetUIStateObject(new FilterUIState());
            }
            else
            {
                Messenger.Register(Messenger.Events.ActivateContentInDockPanel, messenger_OnContentActivation);
                Messenger.Register(Messenger.Events.QueryProductImage, (e) => searchResult_QueryProductImage(null, e.Get<QueryProductImageEventArgs>()));
                Messenger.Register(Messenger.Events.BeginProductDrag, (e) => searchResult_BeginProductDrag(null, EventArgs.Empty));
                Messenger.Register(Messenger.Events.EndProductDrag, (e) => searchResult_EndProductDrag(null, EventArgs.Empty));
                Messenger.Register(Messenger.Events.BeginProductSelection, (e) => searchResult_BeginProductSelection(null, EventArgs.Empty));
                Messenger.Register(Messenger.Events.EndProductSelection, (e) => searchResult_EndProductSelection(null, EventArgs.Empty));
                Messenger.Register(Messenger.Events.DisplayInfoDockerClicked, (e) => DesignModeInfoDocker());
                Messenger.Register(Messenger.Events.NavigationCloseClicked, (e) => Messenger_OnNavigationCloseClicked(e, EventArgs.Empty));
                Messenger.Register(_dependencyToken, Messenger.Events.RequestLayoutRefresh, (sender, payload) => currentTemplateLayoutSizes.RerenderThumbnailsForCurrentTemplate());

                List<EncapsulatedStoreInfo> allPackages;
                using (var dal = Embedded.Factory.StoreInfo)
                    allPackages = dal.GetPackages(true);

                //UiState
                using (var dal = Embedded.Factory.Setting)
                    _productSearchStateFilter = FilterUIStateSerializer.Deserialize(dal.GetBytes(FilterUIStateSerializer.STATE_SETTING_KEY));
                SearchHelper.ValidateAndCorrectAllUIState(_productSearchStateFilter, allPackages.Select(c => c.Guid));
                dockerProductSearch.SetUIStateObject(_productSearchStateFilter);

                SetupInfoDocker();

                if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LocalIntegration))
                {
                    HotfolderController.ArchiveFinishedFiles(PosFilter.ExpandedHotFolder);
                }

                if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.MediaBlobCampaign))
                {
                    Messenger.Register(Messenger.Events.PrintCurrentCampaign, (e) => PrintCampaign(e.Get<Guid>()));
                }

                Messenger.Register(_dependencyToken, Messenger.Events.DesignModeChanged, (sender, payload) => DesignModeChanged(payload.Get<Controls.TemplateRibbonPanel.RibbonUiState.DesignMode>()));

                tags = new DockerTags();
                Messenger.Register(Messenger.Events.BeginSetTag, (payload) => { tags_SetTag(payload.Get<SetTagEventArgs>()); });
                Messenger.Register(Messenger.Events.ProductLinkSettingsChanged, payload => Messenger_SetProductLinkSettings(payload.Get<TemplateProductLinkSettings>()));
                tags.UpdateTagList();

                dsDocker = new DockerDS();
                dsDocker.SetSlideAnimation += new EventHandler<SetSlideAnimationEventArgs>(dsDocker_SetSlideAnimation);
                dsDocker.NeedSynchronize += new EventHandler<EventArgs>(dsDocker_NeedSynchronize);
                dsDocker.StartStopPlay += DsDocker_StartStopPlay;
                Messenger.Register(_dependencyToken, Messenger.Events.SetAnimation, (sender, payload) => Messenger_SetAnimation(sender, payload.Get<SetAnimationEventArgs>()));

                stack = new DockerStack();
                stack.ElementsDropped += new EventHandler(stack_ElementsDropped);
                stack.UnStack += new EventHandler<UnStackEventArgs>(stack_UnStack);

                purposes = new DockerPurpose();

                SetupSearchAndReplaceDocker();

                panelRight.Resize += panelRight_Resize;
                panelDockers.Visible = true;
                panelDesignModes.Visible = false;

                Messenger.Register(Messenger.Events.CampaignPublishClicked, (payload) => { buttonCampaignRecording_Click(); });
                Messenger.Register(Messenger.Events.FormTemplateDesignClosingCanceled, (payload) => { AbortMainFormClosing(); });

                Messenger.Register(Messenger.Events.RequireSync, (payload) => { BeginSynchronize(); });
            }

            signChildWindow = new FormSignProduction(false);
            signChildWindow.Size = dockingPanel.Size;
            signChildWindow.TreeIDEnumaration = TreeIDEnumaration.Template;
            signChildWindow.TopLevel = false;
            signChildWindow.VisibleChanged += new EventHandler(SetOutputFormats);
            signChildWindow.BeginBrowseToPicture += new FormTemplateBase.BeginBrowseToPictureHandler(designChildWindow_BeginBrowseToPicture);
            signChildWindow.SelectBrowseToPicture += new EventHandler(formTemplateBase_SelectBrowseToPicture);
            signChildWindow.EndBrowseToPicture += new EventHandler(signChildWindow_EndBrowseToPicture);
            signChildWindow.SurfaceClicked += new EventHandler(signChildWindow_SurfaceClicked);
            (signChildWindow as FormSignProduction).TemplateSetDirty += new EventHandler(signChildWindow_TemplateSetDirty);

            signChildWindow.BeginProductSearch += new FormTemplateBase.BeginProductSearchHandler(designChildWindow_BeginProductSearch);
            signChildWindow.InformationChangedByUser += new EventHandler<InformationChangedByUserEventArgs>(formTemplateBase_InformationChangedByUser);
            signChildWindow.DesignButtonClicked += new EventHandler(templateDoc_DesignButtonClicked);
            signChildWindow.CloneAndDesignButtonClicked += SignChildWindow_CloneAndDesignButtonClicked;
            (signChildWindow as FormSignProduction).ParentFolderButtonClicked += MainForm_ParentFolderButtonClicked;

            // Templates Group Holder Initialization
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
                templatesImageListView = new ContentView(TreeIDEnumaration.PrintshopServer);
            else
                templatesImageListView = new ContentView(TreeIDEnumaration.Template);
            templatesImageListView.BorderStyle = BorderStyle.None;
            templatesImageListView.Name = "New TemplateListView";
            templatesImageListView.Text = "New Templates";
            templatesImageListView.Dock = DockStyle.Fill;
            templatesImageListView.Change += new EventHandler<ChangeEventArgs>(contentView_Changed);
            templatesImageListView.SelectionChanged += new EventHandler(contentView_SelectionChanged);
            templatesImageListView.Leave += new EventHandler(control_Leave);
            templatesImageListView.KeyDown += new KeyEventHandler(contentView_KeyDown);

            browseFoldersAndTemplatesDock = new DockableContent(templatesImageListView, string.Empty, false);
            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
                dockingPanel.ShowContent(browseFoldersAndTemplatesDock);
            #endregion

            idleSpy = new IdleDetector(800, 180);
            idleSpy.IdleTimeDetected += new EventHandler(idleSpy_IdleTimeDetected);
            idleSpy.LongIdleTimeDetected += new EventHandler(idleSpy_LongIdleTimeDetected);

            dockerProductSearch.LoadFriendlyPlate();

            //Continue the initialization in MainForm.Controller
            this.InitializeMyValues();

            dockingPanel.Resize += DockingPanel_Resize;

            SendChangedProductsToDestinations();
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ESL))
                InitializeMidNightTrigger();

            this.MinimumSize = new System.Drawing.Size(AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design) ? MinimumSize.Width : 638, MinimumSize.Height);

            //TODO: Visit later
            //if (printTree != null)
            //{
            //    var campaignRoot = (printTree.GetCampaignQueue().Tag as PrintNode);
            //    if (campaignRoot.IsNewlyChanged)
            //        setTabPageOutputsIconToNew();
            //}

            var dpi = DpiHelper.GetDpiFromGraphics(this);
            splitter1.Width =
            splitter2.Width =
            titleBarStrip1.SplitterWidth = DpiHelper.GetDpiAdjustedValue(3, dpi);

            splitter1.Paint += Splitter1_Paint;
            splitter2.Paint += Splitter2_Paint;

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.OEM_OKI))
            {
                var finder = new MediaTypedDestinationFinder(PrinterDriverScanner.Instance, AppData.Instance);
                MediaTypedDestinationHolder = new LazyMediaTypedDestinationHolder(finder);
            }

            return true;
        }       

        private void DockingPanel_Resize(object sender, EventArgs e)
        {
            ResizePreviewPlayer();
            ResizeStartpageBox();
        }

        private void DsDocker_StartStopPlay(object sender, EventArgs e)
        {
            TogglePreviewPlayer(PreviewModeSource.Animate);
        }

        private void SetupInfoDocker()
        {
            infoDocker = new DockerInfoDesign();
            infoDocker.ProductSearch += new EventHandler<InputBox.ProductSearchEventArgs>(designChildWindow_BeginProductSearch);
            infoDocker.AddSelectedField += new EventHandler<AddSelectedFieldsEventArgs>(infoDocker_AddSelectedField);
            infoDocker.FieldChanged += new EventHandler<FieldChangedEventArgs>(infoDocker_FieldChanged);
            infoDocker.RequestInputboxDispose += new EventHandler<EventArgs>(infoDocker_RequestInputboxDispose);
            infoDocker.TemplateInformationChanged += new EventHandler<TemplateInformationChangedEventArgs>(infoDocker_TemplateInformationChanged);
            infoDocker.ClearMetaValueOverrides += InfoDocker_ClearMetaValueOverrides;
            infoDocker.Closing += InfoDocker_Closing;
            infoDocker.Dock = DockStyle.None;
            infoDocker.Size = new Size(220, 500);
            infoDocker.Parent = this;
            infoDocker.Visible = false;
        }

        private void RightDocker_VisibleChanged(object sender, EventArgs e)
        {
            ResizeRightDockerFanouts();
            splitter2.Refresh();
        }

        private void BeginSynchronize() => _syncmanager.BeginSynchronize();

        private void InitializeMidNightTrigger()
        {
            var midNightTime = DateTime.Today.AddHours(24).AddSeconds(5);
            var untilMidNightInMiliseconds = (midNightTime - DateTime.Now).TotalMilliseconds;
            _midNightTrigger = new System.Threading.Timer(new TimerCallback(DoTasksOnMidNight), null, (int)untilMidNightInMiliseconds, ONE_DAY_IN_MILLISECONDS);
        }

        private void DoTasksOnMidNight(object state)
        {
            _syncmanager.SyncronizeStoreInfo();
            SendChangedProductsToDestinations();
        }

        private void UpdateRightDocker()
        {
            var activeForm = GetActiveFormTemplateBase();
            if (activeForm is FormTemplateDesign ftd)
            {
                var ribbonUiDesignMode = MapDesignDockerStateToRibbonUiStateDesignMode(ftd.DockerState);
                UpdatePanelDocker(ribbonUiDesignMode);
            }
        }

        private RibbonUiState.DesignMode MapDesignDockerStateToRibbonUiStateDesignMode(DesignDockerStates dockerState)
        {
            var ribbonUiDesignMode = RibbonUiState.DesignMode.Unknown;

            switch (dockerState)
            {
                case DesignDockerStates.GeneralNormal:
                    ribbonUiDesignMode = RibbonUiState.DesignMode.General;
                    break;
                case DesignDockerStates.Tag:
                    ribbonUiDesignMode = RibbonUiState.DesignMode.Tags;
                    break;
                case DesignDockerStates.Animation:
                    ribbonUiDesignMode = RibbonUiState.DesignMode.Flow;
                    break;
                case DesignDockerStates.Precision:
                    ribbonUiDesignMode = RibbonUiState.DesignMode.Precision;
                    break;
                case DesignDockerStates.Arrange:
                    ribbonUiDesignMode = RibbonUiState.DesignMode.Arrange;
                    break;
                default:
                    break;
            }

            return ribbonUiDesignMode;
        }

        private void SetupSearchAndReplaceDocker()
        {
            searchAndReplaceDocker = new DockerSearchReplace();
            searchAndReplaceDocker.Closed += new EventHandler(searchAndReplaceDocker_Closed);

            _scrollholderSearchAndReplace = new Scrollholder
            {
                AllowHorizontalScrolling = false,
                Parent = this,
                ArrowHoverColor = Themes.Current.Colors.FontPickerScrollbarHoverColor,
                ThumbHoverColor = Themes.Current.Colors.FontPickerScrollbarHoverColor,
                ArrowIdleColor = Themes.Current.Colors.FontPickerScrollbarIdleColor,
                ThumbIdleColor = Themes.Current.Colors.FontPickerScrollbarIdleColor,
                ArrowPressedColor = Themes.Current.Colors.FontPickerScrollbarPressedColor,
                ThumbPressedColor = Themes.Current.Colors.FontPickerScrollbarPressedColor,
                Background = Themes.Current.Colors.FontPickerScrollbarBackColor,
                TrackColor = Themes.Current.Colors.FontPickerScrollbarBackColor,
                BackColor = Themes.Current.Colors.GeneralToolbar,
                Location = new Point(panelRight.Location.X, panelRight.Location.Y + RIGHT_PANEL_TOP_OFFSET),
                Size = new Size(panelRight.Width, panelRight.Height - RIGHT_PANEL_TOP_OFFSET),
            };
            _scrollholderSearchAndReplace.VisibleChanged += RightDocker_VisibleChanged;
            _scrollholderSearchAndReplace.SetContent(searchAndReplaceDocker);
            _scrollholderSearchAndReplace.BringToFront();
            _scrollholderSearchAndReplace.Hide();
        }

        private void RecolorSplitterAndActiveDocument(Color newExitIconColor, Color newRibbonActiveBackColor)
        {
            if (splitter1ExitIconColor != newExitIconColor || splitter1RibbonActiveBackColor != newRibbonActiveBackColor)
            {
                splitter1ExitIconColor = newExitIconColor;
                splitter1RibbonActiveBackColor = newRibbonActiveBackColor;
            }

            splitter1.Invalidate();
            splitter2.Invalidate();

            if (dockingPanel.ActiveContent is FormTemplateDesign activeForm)
                titleBarStrip1.SetDesignDocumentBackColor(activeForm.ribbonPanelBackColor);
        }

        private void Splitter1_Paint(object sender, PaintEventArgs e)
        {
            if (e.ClipRectangle.Width == 0 || e.ClipRectangle.Height == 0
                || dockingPanel.Width == 0 || dockingPanel.Height == 0)
                return;
            // paint in a way that the line disapears.
            var dockableContent = dockingPanel.ActiveContent;

            if (dockableContent is FormTemplateDesign formTemplateDesign)
            {
                e.Graphics.FillRectangle(new SolidBrush(Themes.Current.Colors.ToolBoxBarBackground), splitter1.ClientRectangle);

                Rectangle exitIconSection = new Rectangle(0, 0, splitter1.Width, formTemplateDesign.ExitDesignButtonHeight);
                e.Graphics.FillRectangle(new SolidBrush(splitter1ExitIconColor), exitIconSection);

                Rectangle infoButtonSection = new Rectangle(0, exitIconSection.Height, splitter1.Width, formTemplateDesign.InfoButtonHeight);
                e.Graphics.FillRectangle(new SolidBrush(splitter1RibbonActiveBackColor), infoButtonSection);

                if (formTemplateDesign.OutputFormatButtonPanelVisibility)
                {
                    Rectangle outputFormatSection = new Rectangle(0, formTemplateDesign.OutputFormatButtonPanelTop, splitter1.Width, formTemplateDesign.OutputFormatButtonPanelHeight);
                    e.Graphics.FillRectangle(new SolidBrush(Themes.Current.Colors.OutputFormatButtonPanelBackground), outputFormatSection);
                }
                if (dsContentBar1.Visible)
                {
                    Rectangle dsContentBarSection = new Rectangle(0, splitter1.Height - dsContentBar1.Height, splitter1.Width, dsContentBar1.Height);
                    e.Graphics.FillRectangle(new SolidBrush(dsContentBar1.BackColor), dsContentBarSection);
                }
            }
            else if (dockableContent is FormSignProduction)
            {
                LinearGradientBrush brLayout = new LinearGradientBrush(dockingPanel.ClientRectangle,
                    Themes.Current.Colors.PlusModeBackMiddleColor, Themes.Current.Colors.PlusModeBackEndColor,
                    LinearGradientMode.Vertical);

                e.Graphics.FillRectangle(brLayout, splitter1.ClientRectangle);
            }
            else
            {
                // make the line look like the background
                LinearGradientBrush brLayout = new LinearGradientBrush(dockingPanel.ClientRectangle, Themes.Current.Colors.PlusModeBackMiddleColor, Themes.Current.Colors.PlusModeBackEndColor, LinearGradientMode.Vertical);

                e.Graphics.FillRectangle(brLayout, splitter1.ClientRectangle);
            }
        }

        private void Splitter2_Paint(object sender, PaintEventArgs e)
        {
            var dockableContent = dockingPanel.ActiveContent;
            if (searchAndReplaceDocker != null && searchAndReplaceDocker.Visible)
            {
                e.Graphics.FillRectangle(new SolidBrush(Themes.Current.Colors.GeneralToolbar), splitter2.ClientRectangle);
            }
            else if (dockableContent is FormTemplateDesign formTemplateDesign)
            {
                if (formTemplateDesign.DockerState == DesignDockerStates.Precision)
                {
                    e.Graphics.FillRectangle(new SolidBrush(Themes.Current.Colors.PrecisionTabPageBackColor), splitter1.ClientRectangle);
                }
                else
                {
                    Color backColor;
                    switch (formTemplateDesign.DockerState)
                    {
                        case DesignDockerStates.GeneralNormal:
                            backColor = Themes.Current.Colors.ProductListPanelBackground;
                            break;
                        case DesignDockerStates.Tag:
                            backColor = Themes.Current.Colors.TagsToolbar;
                            break;
                        case DesignDockerStates.Animation:
                            backColor = Themes.Current.Colors.FlowToolbar;
                            break;
                        case DesignDockerStates.Arrange:
                            backColor = Themes.Current.Colors.ArrangeToolbar;
                            break;
                        default:
                            throw new ArgumentException($"Recieved unsupported designMode: {formTemplateDesign.DockerState}");
                    }

                    e.Graphics.FillRectangle(new SolidBrush(backColor), splitter2.ClientRectangle);
                }
            }
            else
            {
                Color backColor = Themes.Current.Colors.TitleBarDark;
                e.Graphics.FillRectangle(new SolidBrush(backColor), splitter2.ClientRectangle);
            }

            Rectangle tabNavigationSection = new Rectangle(0, 0, splitter2.Width, RIGHT_PANEL_TOP_OFFSET);
            e.Graphics.FillRectangle(new SolidBrush(Themes.Current.Colors.TitleBarDark), tabNavigationSection);
        }

        private void MainForm_ParentFolderButtonClicked(object sender, EventArgs e)
        {
            var dockableContent = dockingPanel.ActiveContent;
            var form = (dockableContent as FormSignProduction);

            if (form != null)
            {
                if (form.TreeIDEnumaration == TreeIDEnumaration.Template || form.TreeIDEnumaration == TreeIDEnumaration.CampaignRecording)
                {
                    var tree = GetTreeFromTreeId(form.TreeIDEnumaration);
                    if (tree.SelectedNode == null || tree.SelectedNode.Parent == null)
                        return;

                    var wantedNode = tree.SelectedNode.Parent;
                    tree.SelectedNode = null;
                    tree.SelectedNode = wantedNode;
                    EnsureTreeVisibility(form.TreeIDEnumaration);
                }
                else if (form.TreeIDEnumaration == TreeIDEnumaration.PrinterQueue)
                {
                    if (printTree.SelectedNode == null || printTree.SelectedNode.Parent == null)
                        return;
                    var wantedNode = printTree.SelectedNode.Parent;
                    //printTree.SelectedNode = null;
                    printTree.SelectedNode = wantedNode;
                    EnsureTreeVisibility(TreeIDEnumaration.PrinterQueue);
                }
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            SuspendLayout();
            base.OnLoad(e);

            //Ensure that all Campaigns loads and displays correctly
            using (var dal = Embedded.Factory.StoreInfo)
            {
                Messenger.Send(Messenger.Events.StoreInfosChanged, dal.GetPackages(true));
            }
            // Force a translate of all controls.
            Translate();

            // Select first node
            if (templateSearch.TemplateTree != null && templateSearch.TemplateTree.Root.Children.Count > 0)
            {
                templateSearch.TemplateTree.Root.Children.First().IsSelected = true;
                templateSearch.TemplateTree.Root.Children.First().IsExpanded = true;
            }

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LocalIntegration))
            {
                new HotfolderController(AppData.Instance).WriteDisplayGroupsXmlFile(PosFilter.ExpandedHotFolder);
            }

            templateSearch.TemplateSearchResult.SearchDoneEvent += new EventHandler<SearchDoneEventArgs>(tsr_SearchDoneEvent);

            setupPictureEventHandlers();
            ResumeLayout(false);
            CheckAndPromptCampaignRecordingsInEditMode();
            SetNewGdiElementFactory();
        }

        private void setupPictureEventHandlers()
        {
            pictureSearch.PictureSearchResult.SearchDone += pictureSearchResult_PictureSearchDone;
            pictureSearch.PicturesImageListView.Leave += new EventHandler(control_Leave);
            pictureSearch.PicturesImageListView.Change += new EventHandler<ChangeEventArgs>(contentView_Changed);
            pictureSearch.PicturesImageListView.SelectionChanged += new EventHandler(contentView_SelectionChanged);
            pictureSearch.PicturesImageListView.SelectionChangedFinished += new EventHandler(contentView_SelectionChangedFinished);
            pictureSearch.PicturesImageListView.KeyDown += new KeyEventHandler(contentView_KeyDown);
            pictureSearch.PicturesImageListView.MoveFocus += new EventHandler<MoveFocusEventArgs>(picturesImageListView_MoveFocus);
            pictureSearch.PicturesImageListView.DeletePicturesRequested += picturesImageListView_DeletePicturesRequested;
            pictureSearch.PicturesImageListView.DragDrop += picturesImageListView_DragDrop;
            pictureSearch.PicturesImageListView.VideoPlayRequested += picturesImageListView_VideoPlayRequested;
        }

        private void picturesImageListView_VideoPlayRequested(object sender, Manina.Windows.Forms.ItemEventArgs e)
        {
            if (e.Item.ItemButtonClicked)
                PlayVideo((Guid)e.Item.VirtualItemKey);
            else
                StopVideoPlayback();
        }

        void picturesImageListView_DragDrop(object sender, DragEventArgs e)
        {
            // Mimics logic that picture tree does before throwing a DragDropEvent

            bool isFileDrop = e.Data.GetDataPresent(DataFormats.FileDrop);
            TreeDropEventArgs tdea;
            if (isFileDrop)
            {
                // start file import from main form.
                tdea = new TreeDropEventArgs()
                {
                    FilePaths = new List<string>((string[])e.Data.GetData(DataFormats.FileDrop)),
                    Type = TreeDropEventArgs.EventType.ImportPicturesFromFile,
                    DroppedOnFolder = (pictureSearch.PictureTree.SelectedNode.Tag as ShoppaTreeNode).ContentGuid
                };
            }
            else
            {
                tdea = new TreeDropEventArgs()
                {
                    FilePaths = new List<string> { (string)e.Data.GetData(DataFormats.Text) },
                    Type = TreeDropEventArgs.EventType.ImportPicturesFromWeb,
                    DroppedOnFolder = (pictureSearch.PictureTree.SelectedNode.Tag as ShoppaTreeNode).ContentGuid
                };
            }
            pictureTree_DropEvent(sender, tdea);
        }

        void picturesImageListView_MoveFocus(object sender, MoveFocusEventArgs e)
        {
            var form = GetActiveFormTemplateBase();
            if (form != null)
            {
                Messenger.Send(Messenger.Events.ActivateContentInDockPanel, form);
                form.MoveFocus(e.Forward);
                e.Handled = true;
            }
        }

        void picturesImageListView_DeletePicturesRequested(object sender, EventArgs e)
        {
            deletePictureInContentView(TreeIDEnumaration.Picture);
        }

        void infoDocker_TemplateInformationChanged(object sender, TemplateInformationChangedEventArgs e)
        {
            var form = GetActiveFormTemplateBase();
            if (form is FormTemplateDesign)
            {
                var designForm = (form as FormTemplateDesign);
                designForm.SetTemplateInformation(e.TemplateInformation, e.DoUndoState);
            }
        }

        private void InfoDocker_ClearMetaValueOverrides(object sender, EventArgs e)
        {
            var form = GetActiveFormTemplateBase();
            if (form is FormTemplateDesign)
            {
                var designForm = (form as FormTemplateDesign);
                designForm.ClearMetaValueOverrides();
            }
        }

        void infoDocker_FieldChanged(object sender, FieldChangedEventArgs e)
        {
            var form = GetActiveFormTemplateBase();
            if (form is FormTemplateDesign design)
                design.ResolveChangeSet(e);
        }

        void infoDocker_RequestInputboxDispose(object sender, EventArgs e)
        {
            var form = GetActiveFormTemplateBase();
            if (form is FormTemplateDesign)
                (form as FormTemplateDesign).RequestInputboxDispose();
        }

        void infoDocker_AddSelectedField(object sender, AddSelectedFieldsEventArgs e)
        {
            var form = GetActiveFormTemplateBase();
            if (form is FormTemplateDesign)
                (form as FormTemplateDesign).FillFieldsFromSelectedElements(e);

        }

        /// <summary>
        /// If the OutputBar (contains Add to Queue for Printer/Printshop/Flow etc) should 
        /// take open template directly or first add template + all selected products.
        /// </summary>
        private OutputBarQueueModes OutputBarMode
        {
            get { return this.outputBarMode; }
            set { this.outputBarMode = this.outputBar.Mode = value; }
        }

        #region Tree Setup and Tree Event handlers

        void SetupTreeComponent()
        {
            List<TreeGroupInformation> rootGroupsTemplateToAdd, rootGroupsPicturesToAdd, rootGroupsCampaignRecToAdd;
            SetupRootGroupsBasedOnCustomer(out rootGroupsTemplateToAdd, out rootGroupsPicturesToAdd, out rootGroupsCampaignRecToAdd
                , out _, out _);

            #region Setup Tree
            // Template
            templateSearch.TemplateTree.ApplyModel(rootGroupsTemplateToAdd);
            templateSearch.TemplateTree.SelectionChanged += new EventHandler(TemplateTree_SelectionChangedAsync);
            templateSearch.TemplateTree.TreeChangedEvent += new EventHandler<TreeChangedEventArgs>(tree_TreeChangedEvent);
            templateSearch.TemplateTree.MenuEvent += new EventHandler<TreeMenuEventArgs>(templateTree_MenuEvent);
            templateSearch.TemplateTree.Leave += new EventHandler(control_Leave);
            templateSearch.TemplateTree.KeyDown += new KeyEventHandler(tree_KeyDown);
            templateSearch.TemplateTree.MouseClick += new MouseEventHandler(templateTree_MouseClick);

            // Campaign
            templateSearch.CampaignTree.ApplyModel(rootGroupsCampaignRecToAdd);
            templateSearch.CampaignTree.SelectionChanged += new EventHandler(CampaignTree_SelectionChangedAsync);
            templateSearch.CampaignTree.TreeChangedEvent += new EventHandler<TreeChangedEventArgs>(tree_TreeChangedEvent);
            templateSearch.CampaignTree.Leave += new EventHandler(control_Leave);
            templateSearch.CampaignTree.KeyDown += new KeyEventHandler(tree_KeyDown);
            templateSearch.CampaignTree.MouseClick += new MouseEventHandler(campaignTree_MouseClick);
            templateSearch.CampaignTree.CampaignRecordingMenuEvent += new EventHandler<CampaignTreeMenuEventArgs>(CampaignTree_MenuEvent);
            
            // Picture
            pictureSearch.PictureTree.ApplyModel(rootGroupsPicturesToAdd);
            pictureSearch.PictureTree.SelectionChanged += new EventHandler(pictureTree_SelectionChanged);
            pictureSearch.PictureTree.TreeChangedEvent += new EventHandler<TreeChangedEventArgs>(tree_TreeChangedEvent);
            pictureSearch.PictureTree.DropEvent += new EventHandler<TreeDropEventArgs>(pictureTree_DropEvent);
            pictureSearch.PictureTree.MenuEvent += new EventHandler<TreeMenuEventArgs>(pictureTree_MenuEvent);
            pictureSearch.PictureTree.Leave += new EventHandler(control_Leave);
            pictureSearch.PictureTree.KeyDown += new KeyEventHandler(tree_KeyDown);

            // Print
            printTree.SetModel(AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer));
            printTree.SelectionChanged += new EventHandler(printTree_SelectionChanged);
            printTree.PrintShopTreeEvent += new EventHandler<PrintShopTreeEventArgs>(printTree_PrintShopTreeEvent);
            printTree.MenuEvent += new EventHandler<PrintTreeMenuEventArgs>(printTree_MenuEvent);
            printTree.Leave += new EventHandler(control_Leave);
            printTree.KeyDown += new KeyEventHandler(tree_KeyDown);
            printTree.KeyUp += new KeyEventHandler(printView_KeyUp);
            printTree.MouseClick += new MouseEventHandler(printTree_MouseClick);
            printTree.Expanded += new EventHandler<TreeViewAdvEventArgs>(printTree_Expanded);
            #endregion
        }

        private void RemoveUnnecessaryCampaignTreeScrollbars()
        {
            var campaignTree = GetTreeFromTreeId(TreeIDEnumaration.CampaignRecording);
            // For the specific scenario where the campaign tree has only one root node, and that node has only one child, and that child is not expanded initially, 
            // the campaign tree will show a unnecessary scrollbar. This code will remove that scrollbar.
            if (campaignTree.Root.Children.Count == 1)
            {
                var firstChild = campaignTree.Root.Children.First();
                if (!firstChild.IsExpanded)
                {
                    firstChild.Expand();
                    firstChild.Collapse();
                }
            }
        }

        //TODO: Should try to break this method into smaller methods for sepearate concerns, Templates, Campaigns, Pictures, Print
        void SetupRootGroupsBasedOnCustomer(out List<TreeGroupInformation> rootGroupsTemplateToAdd, out List<TreeGroupInformation> rootGroupsPicturesToAdd, out List<TreeGroupInformation> rootGroupsCampaignRecToAdd
            , out List<TreeGroupInformation.GroupType> rootGroupsTemplateToDelete, out List<TreeGroupInformation.GroupType> rootGroupsPicturesToDelete)
        {
            // Setup RootGroups
            rootGroupsTemplateToAdd = new List<TreeGroupInformation>();
            rootGroupsTemplateToDelete = new List<TreeGroupInformation.GroupType>();
            rootGroupsPicturesToAdd = new List<TreeGroupInformation>();
            rootGroupsPicturesToDelete = new List<TreeGroupInformation.GroupType>();
            rootGroupsCampaignRecToAdd = new List<TreeGroupInformation>();
            var rootGroupsCampaignRecToDelete = new List<TreeGroupInformation.GroupType>();

            var user = AppData.Instance.CurrentUser;
            var templateAmounts = TemplateController.CountTemplatesPerCustomerId();
            var pictureAmounts = PictureController.CountPicturesPerCustomerId();
            var campaignRecordingAmounts = CampaignRecordingController.CountItemsPerCustomerId();
            List<int> customerIds;

            // My Material
            customerIds = new List<int>() { user.CustomerId };
            var hasDesign = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design);
            var canImportPicturesAndVideo = AppData.Instance.CurrentUser.IsInAnyRole(AddOnsEnumeration.Design, AddOnsEnumeration.MediaEditor);
            var canPublishCampaignRecording = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PublishCampaignRecording);
            var hasTemplates = hasLocalContent(templateAmounts, customerIds);
            var hasPictures = hasLocalContent(pictureAmounts, customerIds);
            var hasCampaignRecordings = hasLocalContent(campaignRecordingAmounts, customerIds);

            // MyMaterial should be available if user has design OR any templates exist (made by someone else with design)
            if (hasDesign || hasTemplates)
                rootGroupsTemplateToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_myMaterial", TreeGroupInformation.GroupType.MyMaterial, hasDesign));
            else
                rootGroupsTemplateToDelete.Add(TreeGroupInformation.GroupType.MyMaterial);

            if (canImportPicturesAndVideo || hasPictures)
                rootGroupsPicturesToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_myPictures", TreeGroupInformation.GroupType.MyMaterial, canImportPicturesAndVideo));
            else
                rootGroupsPicturesToDelete.Add(TreeGroupInformation.GroupType.MyMaterial);
            
            if (canPublishCampaignRecording || hasCampaignRecordings)
                rootGroupsCampaignRecToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_myCampaignRecordings", TreeGroupInformation.GroupType.MyMaterial, canPublishCampaignRecording));
            else
                rootGroupsCampaignRecToDelete.Add(TreeGroupInformation.GroupType.MyMaterial);

            // MyOrganization. Use customer name of highest MyOrganization.CustomerId as Node Name
            var myOrganisation = AppData.Instance.CurrentUser.GetAvailableCustomers(CustomerType.MyOrganisation);
            if (myOrganisation.Count() > 0)
            {
                var localCustomer = CustomerController.GetLocalCustomer(myOrganisation.Last());
                var nodeName = string.Empty;
                if (localCustomer != null)
                    nodeName = localCustomer.Name;

                customerIds = getCustomerIdsForCustomerType(templateAmounts, CustomerType.MyOrganisation);
                if (customerIds.Count > 0)
                {
                    var info = new TreeGroupInformation(customerIds, "TreeRootNode_myOrgTemplates", TreeGroupInformation.GroupType.MyOrganisation);
                    info.TranslateParameters.Add(nodeName);
                    rootGroupsTemplateToAdd.Add(info);
                }
                else
                    rootGroupsTemplateToDelete.Add(TreeGroupInformation.GroupType.MyOrganisation);

                customerIds = getCustomerIdsForCustomerType(pictureAmounts, CustomerType.MyOrganisation);
                if (customerIds.Count > 0)
                {
                    var info = new TreeGroupInformation(customerIds, "TreeRootNode_myOrgPictures", TreeGroupInformation.GroupType.MyOrganisation);
                    info.TranslateParameters.Add(nodeName);
                    rootGroupsPicturesToAdd.Add(info);
                }
                else
                    rootGroupsPicturesToDelete.Add(TreeGroupInformation.GroupType.MyOrganisation);

                customerIds = getCustomerIdsForCustomerType(campaignRecordingAmounts, CustomerType.MyOrganisation);
                if (customerIds.Count > 0)
                {
                    var info = new TreeGroupInformation(customerIds, "TreeRootNode_myOrgCampaignRecordings", TreeGroupInformation.GroupType.MyOrganisation);
                    info.TranslateParameters.Add(nodeName);
                    rootGroupsCampaignRecToAdd.Add(info);
                }
                else
                    rootGroupsCampaignRecToDelete.Add(TreeGroupInformation.GroupType.MyOrganisation);
            }

            // ExtraOrganisation
            customerIds = getCustomerIdsForCustomerType(templateAmounts, CustomerType.ExtraOrganisation);
            if (customerIds.Count > 0)
                rootGroupsTemplateToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_extraOrgTemplates", TreeGroupInformation.GroupType.ExtraOrganisation));
            else
                rootGroupsTemplateToDelete.Add(TreeGroupInformation.GroupType.ExtraOrganisation);

            customerIds = getCustomerIdsForCustomerType(pictureAmounts, CustomerType.ExtraOrganisation);
            if (customerIds.Count > 0)
                rootGroupsPicturesToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_extraOrgPictures", TreeGroupInformation.GroupType.ExtraOrganisation));
            else
                rootGroupsPicturesToDelete.Add(TreeGroupInformation.GroupType.ExtraOrganisation);

            customerIds = getCustomerIdsForCustomerType(campaignRecordingAmounts, CustomerType.ExtraOrganisation);
            if (customerIds.Count > 0)
                rootGroupsCampaignRecToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_extraOrgCampaignRecordings", TreeGroupInformation.GroupType.ExtraOrganisation));
            else
                rootGroupsCampaignRecToDelete.Add(TreeGroupInformation.GroupType.ExtraOrganisation);

            // Public
            customerIds = getCustomerIdsForCustomerType(templateAmounts, CustomerType.Public);
            if (customerIds.Count > 0)
                rootGroupsTemplateToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_publicTemplates", TreeGroupInformation.GroupType.Public));
            else
                rootGroupsTemplateToDelete.Add(TreeGroupInformation.GroupType.Public);

            customerIds = getCustomerIdsForCustomerType(pictureAmounts, CustomerType.Public);
            if (customerIds.Count > 0)
                rootGroupsPicturesToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_publicPictures", TreeGroupInformation.GroupType.Public));
            else
                rootGroupsPicturesToDelete.Add(TreeGroupInformation.GroupType.Public);

            customerIds = getCustomerIdsForCustomerType(campaignRecordingAmounts, CustomerType.Public);
            if (customerIds.Count > 0)
                rootGroupsCampaignRecToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_publicCampaignRecordings", TreeGroupInformation.GroupType.Public));
            else
                rootGroupsCampaignRecToDelete.Add(TreeGroupInformation.GroupType.Public);

            // Supplier
            customerIds = getCustomerIdsForCustomerType(templateAmounts, CustomerType.Supplier);
            if (customerIds.Count > 0)
                rootGroupsTemplateToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_suppliersTemplates", TreeGroupInformation.GroupType.Supplier));
            else
                rootGroupsTemplateToDelete.Add(TreeGroupInformation.GroupType.Supplier);

            customerIds = getCustomerIdsForCustomerType(pictureAmounts, CustomerType.Supplier);
            if (customerIds.Count > 0)
                rootGroupsPicturesToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_suppliersPictures", TreeGroupInformation.GroupType.Supplier));
            else
                rootGroupsPicturesToDelete.Add(TreeGroupInformation.GroupType.Supplier);

            customerIds = getCustomerIdsForCustomerType(campaignRecordingAmounts, CustomerType.Supplier);
            if (customerIds.Count > 0)
                rootGroupsCampaignRecToAdd.Add(new TreeGroupInformation(customerIds, "TreeRootNode_suppliersCampaignRecordings", TreeGroupInformation.GroupType.Supplier));
            else
                rootGroupsCampaignRecToDelete.Add(TreeGroupInformation.GroupType.Supplier);
        }


        private static bool hasLocalContent(Dictionary<int, int> customerIdToContentAmount, List<int> customerIds)
        {
            var count = 0;
            foreach (var item in customerIds)
            {
                if (customerIdToContentAmount.ContainsKey(item))
                    count += customerIdToContentAmount[item];

                if (count > 0)
                    return true;
            }

            return false;
        }

        private static List<int> getCustomerIdsForCustomerType(Dictionary<int, int> customerIdToTemplateAmount, CustomerType customerType)
        {
            var user = AppData.Instance.CurrentUser;
            var customerIds = user.GetAvailableCustomers(customerType);
            // Remove ourself if included
            customerIds.Remove(user.CustomerId);
            if (customerIds.Count == 0)
                return customerIds;

            var hasTemplates = hasLocalContent(customerIdToTemplateAmount, customerIds);
            // No ids with templates
            if (!hasTemplates)
                return new List<int>();

            return customerIds;
        }

        void tree_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && isNewKeyPress)
            {
                isNewKeyPress = false;

                // Suppress the event if the tree is not focused or if there is nothing selected in the tree
                if (!currentTree.Focused)
                {
                    isNewKeyPress = true;
                    e.SuppressKeyPress = true;
                    return;
                }
                else if (currentTree.SelectedNodes.Count == 0)
                {
                    isNewKeyPress = true;
                    e.SuppressKeyPress = true;
                    return;
                }

                // Suppress the event if we are not currently allowed to delete material
                if (currentTree == templateSearch.TemplateTree)
                {
                    if (!(buttonDeleteTemplate.Enabled))
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    DeleteContentOrFolder(TreeIDEnumaration.Template);
                }
                else if (currentTree == templateSearch.CampaignTree)
                {
                    if(!buttonDeleteCampaign.Enabled)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    DeleteFromCampaignTree();
                }
                else if (currentTree == pictureSearch.PictureTree)
                {
                    if (!buttonDeletePicture.Enabled)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }

                    DeleteContentOrFolder(TreeIDEnumaration.Picture);
                }
                else if (currentTree == printTree)
                {
                    if (!buttonDeleteOutput.Enabled)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }

                    var topNode = (printTree.SelectedNodes[0].Tag as PrintNode);
                    //TODO: Move this code to correct place when implementing new functionality.
                    //if (topNode.NodeType == PrintNodeType.Campaign)
                    //{
                    //    if (MessageBox.Show(MLS.Instance.TranslateText("CRT_DeleteMessage"), MLS.Instance.TranslateText("CRT_Delete"), MessageBoxButtons.YesNo) == System.Windows.Forms.DialogResult.Yes)
                    //        deleteFromPrintQueue();
                    //}
                    //else
                    DeleteFromPrintQueue();
                }
            }
            else if (e.KeyData == (Keys.Control | Keys.C) || e.KeyData == (Keys.Control | Keys.Insert))
            {
                if (SuppressCopyPaste())
                {
                    e.SuppressKeyPress = true;
                    return;
                }

                CopyTemplates(TreeIDEnumaration.Template);
            }
            else if (e.KeyData == (Keys.Control | Keys.V) || e.KeyData == (Keys.Shift | Keys.Insert))
            {
                if (SuppressCopyPaste())
                {
                    e.SuppressKeyPress = true;
                    return;
                }

                PasteTemplates(TreeIDEnumaration.Template);
            }

            // remove hover if we are navigating in printtree
            if (recordingHoverToolstrip != null)
                clearRecordingHoverToolstrip();
        }

        bool SuppressCopyPaste()
        {
            if (!AppData.Instance.CurrentUser.IsInAllRoles(AddOnsEnumeration.TreeCopyPaste, AddOnsEnumeration.Design))
                return true;
            if (!currentTree.Focused)
                return true;
            if (currentTree != templateSearch.TemplateTree)
                return true;
            if (currentTree.SelectedNodes.Count == 0)
                return true;
            if (currentTree.SelectedNodes.Any(n => n.Tag is CampaignNode))
                return true;

            return false;
        }

        void tree_TreeChangedEvent(object sender, TreeChangedEventArgs e)
        {
            var treeController = new TreeController2(AppData.Instance);

            treeController.UpdateLocalDB(e.TreeId, e.Changes);
            _syncmanager.BeginSynchronize();

            if (e.TreeId == TreeIDEnumaration.Template || e.TreeId == TreeIDEnumaration.CampaignRecording)
            {
                // Rename: Change name in active template Name to prevent it from saving old name
                var renamedItems = e.Changes.FindAll(x => x.ChangeType == ItemChangeType.ItemRenamed);
                var renamedItem = renamedItems.FirstOrDefault();

                if (e.TreeId == TreeIDEnumaration.Template)
                {
                    var form = GetActiveFormTemplateBase();
                    if (form != null && form is FormTemplateBase)
                    {
                        foreach (var changeinfo in renamedItems)
                        {
                            var templateCapsule = form.GetTemplateCapsule();
                            if (changeinfo.IsFolder)
                                browseFoldersAndTemplatesDock.TabName = changeinfo.ItemText;
                            else if (templateCapsule != null && changeinfo.ItemGuid == templateCapsule.Guid)
                                form.SetName(changeinfo.ItemText, false);
                        }
                    }
                }

                // Selected the renamed node
                if (renamedItem != null)
                {
                    var tree = GetTreeFromTreeId(e.TreeId);
                    if (renamedItem.IsFolder)
                        tree.BrowseToFolder(renamedItem.ItemGuid);
                    else if(renamedItem.IsCampaignSign)
                        tree.BrowseToCampaignSign(renamedItem.ItemGuid);
                    else
                        tree.BrowseToContent(renamedItem.ItemGuid);
                }
                else
                {
                    EncapsulatedFolder folder = null;
                    var movedItems = e.Changes.FindAll(x => x.ChangeType == ItemChangeType.ItemMoved);
                    var movedItem = movedItems.FirstOrDefault();
                    if (movedItem != null)
                    {
                        folder = FolderController.GetEncapsulatedFolder(movedItem.ParentGuid);
                        var tree = GetTreeFromTreeId(e.TreeId);
                        if (movedItem.IsFolder)
                        {
                            tree.BrowseToFolder(movedItem.ItemGuid);

                            // Mark all the children of any moved folder as moved as well
                            tree.SuspendLayout();
                            var newChanges = new List<ItemChangeInfo>();
                            TreeNodeAdv newParent = tree.AllNodes.FirstOrDefault(n => (n.Tag as ShoppaTreeNode).ContentGuid == movedItem.ParentGuid);
                            if (newParent != null)
                            {
                                foreach (var change in movedItems.Where(i => i.ItemGuid != (newParent.Tag as ShoppaTreeNode).ContentGuid))
                                {
                                    TreeNodeAdv treeNode = newParent.Children.FirstOrDefault(n => (n.Tag as ShoppaTreeNode).ContentGuid == change.ItemGuid);
                                    if (treeNode != null && change.IsFolder)
                                        markChildrenAsMoved(treeNode, newChanges, e.TreeId);
                                }
                            }
                            tree.ResumeLayout();
                            treeController.UpdateLocalDB(e.TreeId, newChanges);
                            newChanges = null;
                        }
                        else
                            tree.BrowseToContent(movedItem.ItemGuid);
                    }

                    if (folder != null && !folder.IsPublic)
                    {
                        foreach (var change in movedItems)
                        {
                            if (change.ItemGuid == folder.Guid)
                                continue;
                            Capsule item = null;
                            if (change.IsFolder)
                                item = FolderController.GetEncapsulatedFolder(change.ItemGuid);
                            else
                                item = TemplateController.GetEncapsulatedTemplate(change.ItemGuid);
                            if (item.IsPublic)
                            {
                                setTreeNodeVisibility(e.TreeId, folder.Guid, folder.StartDate, folder.StopDate, true);
                                break;
                            }
                        }
                    }
                }
            }           
            else if (e.TreeId == TreeIDEnumaration.Picture)
            {
                EncapsulatedFolder folder = null;
                var movedItems = e.Changes.FindAll(x => x.ChangeType == ItemChangeType.ItemMoved);
                var movedItem = movedItems.FirstOrDefault();
                if (movedItem != null)
                {
                    folder = FolderController.GetEncapsulatedFolder(movedItem.ParentGuid);
                    var tree = GetTreeFromTreeId(e.TreeId);
                    if (movedItem.IsFolder)
                    {
                        tree.BrowseToFolder(movedItem.ItemGuid);

                        // Mark all the children of any moved folder as moved as well
                        tree.SuspendLayout();
                        var newChanges = new List<ItemChangeInfo>();
                        TreeNodeAdv newParent = tree.AllNodes.FirstOrDefault(n => (n.Tag as ShoppaTreeNode).ContentGuid == movedItem.ParentGuid);
                        if (newParent != null)
                        {
                            foreach (var change in movedItems.Where(i => i.ItemGuid != (newParent.Tag as ShoppaTreeNode).ContentGuid))
                            {
                                TreeNodeAdv treeNode = newParent.Children.FirstOrDefault(n => (n.Tag as ShoppaTreeNode).ContentGuid == change.ItemGuid);
                                if (treeNode != null && change.IsFolder)
                                    markChildrenAsMoved(treeNode, newChanges, e.TreeId);
                            }
                        }
                        tree.ResumeLayout();
                        treeController.UpdateLocalDB(e.TreeId, newChanges);
                        newChanges = null;
                    }
                }

                if (folder != null && !folder.IsPublic)
                {
                    foreach (var change in movedItems)
                    {
                        if (change.ItemGuid == folder.Guid)
                            continue;
                        Capsule item = null;
                        if (change.IsFolder)
                            item = FolderController.GetEncapsulatedFolder(change.ItemGuid);
                        else
                            item = PictureController.GetEncapsulatedPictureLight(change.ItemGuid);
                        if (item.IsPublic)
                        {
                            setTreeNodeVisibility(e.TreeId, folder.Guid, folder.StartDate, folder.StopDate, true);
                            break;
                        }
                    }
                }
            }
            splitter1.Refresh();
        }

        int lastSelectedTemplateNodeIndex = -1;
        TreeNodeAdv lastSelectedNode = null;
        bool forceUpdateAfterBrowsing = false;
        async void TemplateTree_SelectionChangedAsync(object sender, EventArgs e)
        {
            cancellationTokenSource?.Cancel();

            cancellationTokenSource = new CancellationTokenSource();
            try
            {
                await OnTemplateTreeSelectionChangedAsync(sender, cancellationTokenSource.Token);
            }
            catch (TaskCanceledException)
            {
                // Task cancelled due to a new selection of template/ campaign recording
            }
            catch (Exception ex)
            {
                Trace.WriteLineIf(traceSwitch.TraceError, $"Error while handling template tree selection, {ex.Message} Stack trace: {ex.StackTrace}", "E");
            }
        }

        private async Task OnTemplateTreeSelectionChangedAsync(object sender, CancellationToken ct)
        {
            ClearCampaignTreeSelection();

            var tree = currentTree = sender as TreeViewAdv;
            bool pickLast = tree.SelectedNodes.Count > 1 && tree.SelectedNode.Index == lastSelectedTemplateNodeIndex;
            var node = currentNode = pickLast ? tree.SelectedNodes.Last() : tree.SelectedNode;

            lastSelectedTemplateNodeIndex = node == null ? -1 : node.Index;

            if (node == null || tabControlContent.SelectedTab == tabPageOutputs)
                return;

            StopPlayerAndResetUiStatus();

            if (lastSelectedNode?.Tag is CampaignNode && !(node.Tag is CampaignNode))
                SaveCheckCurrentQueuedSign();
            lastSelectedNode = node;

            // GUI Access control
            EnsureGuiVisibilityBasedOnNode(tree, node);

            outputBar.AddDeleteCampaignRecordingPrimaryEnabled = true;
            outputBar.CanAddToFlow = true;

            if (!node.IsLeaf)
            {
                OnTemplateTreeFolderSelected(tree, node);
            }
            else
            {
                toggleCanQueue(true);
                var templateGuid = (node.Tag as ShoppaTreeNode).ContentGuid;
                TreeIDEnumaration id = TreeIDEnumaration.Template;

                if (templateGuid != Guid.Empty)
                {
                    // S�tta i datalagret att den �r kollad p�
                    // H�mta upp alla som nu inte l�ngre har okollade mallar
                    await ResetNewlyChangedFlagAsync(tree, node, templateGuid, ct);

                    if (!IsTemplateActive(templateGuid))
                        await OpenTemplateAsync(templateGuid, id, ct);

                    // Return focus to tree
                    tree.Focus();

                    UpdateDesignPermission(tree, node);

                    bool showQueueSheetButton = true;
                    showQueueSheetButton &= AppData.Instance.CurrentUser.IsInAnyRole(QueueSheetAddOns);
                    showQueueSheetButton &= node.IsLeaf;

                    toggleCanQueueSheet(showQueueSheetButton);
                }
            }
        }

        private void UpdateDesignPermission(TreeViewAdv tree, TreeNodeAdv node)
        {
            if (dockingPanel.ActiveContent is FormSignProduction sign
                && tree.Model is ModelBaseShoppa mbs)
            {
                bool inEditableTree = mbs.NodeIsInEditableTree(node);
                var design = FormSignProduction.Permission.None;

                if (inEditableTree && AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.Design))
                    design = FormSignProduction.Permission.Allow;
                else if (!inEditableTree && AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ShoppaOkey))
                    design = FormSignProduction.Permission.CloneAndAllow;

                sign.DesignPermission = design;
            }
        }

        private static async Task ResetNewlyChangedFlagAsync(TreeViewAdv tree, TreeNodeAdv node, Guid templateGuid, CancellationToken ct)
        {
            if ((node.Tag as ShoppaTreeNode).IsNewlyChanged)
            {
                var itemsToRefresh = await AvailabilityController.ResetChangedAsync(templateGuid, ct);
                foreach (var item in itemsToRefresh)
                {
                    (tree as ShoppaTree2).AddContent(new ItemChangeInfo()
                    {
                        ChangeType = ItemChangeType.NewlyChanged,
                        IsNewlyChanged = false,
                        ItemGuid = item.ItemGuid,
                        ParentGuid = item.ParentGuid
                    });
                }
            }
        }

        private void OnTemplateTreeFolderSelected(TreeViewAdv tree, TreeNodeAdv node)
        {
            EnsureChildrenLoaded(node);

            toggleCanQueue(node.Children.Any(n => n.IsLeaf));

            bool wasInTemplate = dockingPanel.DockedContent.Contains(signChildWindow);

            lock (_activeDocumentChangedSubscriptionLock)
            {
                // Switch dock to Folder browser, if templateDock (signChildWindow) is visable hide it
                dockingPanel.ActiveContentChanged -= new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                if (wasInTemplate)
                    Messenger.Send(Messenger.Events.RemoveContentInDockPanel, signChildWindow);
                Messenger.Send(Messenger.Events.ActivateContentInDockPanel, browseFoldersAndTemplatesDock);
                dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
            }

            var dockText = "Templates";
            var folderGuid = Guid.Empty;
            // Hidden root node have node.Tag == null
            if (node.Tag is ShoppaTreeNode)
            {
                dockText = (node.Tag as NodeBase).Text ?? string.Empty;
                folderGuid = (node.Tag as ShoppaTreeNode).ContentGuid;
            }

            // Return focus to tree
            tree.Focus();

            // Only add content if node has changed
            if (forceUpdateAfterBrowsing || wasInTemplate || (templatesImageListView.FolderGuid != folderGuid))
            {
                templatesImageListView.TreeId = TreeIDEnumaration.Template;
                templatesImageListView.FolderGuid = folderGuid;
                templatesImageListView.Editable = (tree.Model as ModelBaseShoppa).NodeIsInEditableTree(node);
                templatesImageListView.AddTemplateContent(node.Children);
                browseFoldersAndTemplatesDock.TabName = dockText;
                forceUpdateAfterBrowsing = false;
            }
            currentTemplateLayoutSizes.ClearControls();
            workModeSwitched(WorkMode.Production);
        }

        private void ClearCampaignTreeSelection()
        {
            templateSearch.CampaignTree.SelectionChanged -= CampaignTree_SelectionChangedAsync;
            templateSearch.CampaignTree.SelectedNode = null;
            templateSearch.CampaignTree.SelectionChanged += CampaignTree_SelectionChangedAsync;
        }

        private bool IsTemplateActive(Guid templateGuid) =>
            IsTemplateActiveInDesign(templateGuid) ||
            IsTemplateActiveInProduction(templateGuid);

        private bool IsTemplateActiveInDesign(Guid templateGuid)
        {
            return dockingPanel?.ActiveContent is FormTemplateDesign sign
                   && sign.TemplateGuid == templateGuid;
        }

        private bool IsTemplateActiveInProduction(Guid templateGuid)
        {
            return dockingPanel?.ActiveContent is FormSignProduction sign
                   && sign.TemplateGuid == templateGuid;
        }

        void templateDoc_DesignButtonClicked(object sender, EventArgs e)
        {
            OpenActiveSignInDesignMode();
        }

        private void SignChildWindow_CloneAndDesignButtonClicked(object sender, EventArgs e)
        {
            if (dockingPanel.ActiveContent is FormSignProduction sign)
            {
                Guid myTemplatesFolder = GetTreeFolderGuid(TreeIDEnumaration.Template, TreeGroupInformation.GroupType.MyMaterial);

                sign.CloneTemplate(targetFolder: myTemplatesFolder);
                AddTemplateAndSelectIt(template: sign.GetTemplateCapsule());

                OpenActiveSignInDesignMode();
            }
        }

        public void SelectTreeRootGroup(TreeIDEnumaration treeType, TreeGroupInformation.GroupType groupType)
        {
            if (GetTreeFromTreeId(treeType) is var tree &&
                tree.GetRoot(groupType) is var node)
            {
                tree.SelectedNode = node;
                node.ExpandAll();
            }
        }

        private void OpenActiveSignInDesignMode()
        {
            var activeNode = currentNode; // save this as changing activedoc will change them too
            if (dockingPanel.ActiveContent is FormSignProduction sign)
            {
                Control newWindow = null;
                ControlHelper.SuspendDrawing(this);
                if (tabControlContent.SelectedTab == tabPageTemplates && activeNode != null)
                {
                    // Select parent folder to force browseFoldersAndTemplatesDock to point correct 
                    templateSearch.TemplateTree.SelectedNode = activeNode.Parent;

                    // Open template in design
                    var templateGuid = sign.GetTemplateCapsule().Guid;
                    newWindow = OpenTemplateInDesign(templateGuid);

                    // Select correct node again to make it active
                    templateSearch.TemplateTree.SelectedNode = activeNode;
                }
                else
                {
                    // Open template in design without selecting the tree node
                    var templateGuid = sign.GetTemplateCapsule().Guid;
                    newWindow = OpenTemplateInDesign(templateGuid);
                }
                ControlHelper.ResumeDrawing(this);
                newWindow.Focus();
            }
        }

        void templateTree_MenuEvent(object sender, TreeMenuEventArgs e)
        {
            switch (e.Type)
            {
                case TreeMenuEventArgs.EventType.AddNewTemplateRequest:
                    AddNewTemplate();
                    break;
                case TreeMenuEventArgs.EventType.AddNewFolderRequest:
                    AddNewFolder(TreeIDEnumaration.Template);
                    break;
                case TreeMenuEventArgs.EventType.DeleteTemplateRequest:
                case TreeMenuEventArgs.EventType.DeleteFolderRequest:
                    DeleteContentOrFolder(TreeIDEnumaration.Template);
                    break;
                case TreeMenuEventArgs.EventType.SetVisibilityRequest:
                    ChangeVisibility(TreeIDEnumaration.Template);
                    break;
                case TreeMenuEventArgs.EventType.MarkedVisibleRequest:
                    MarkVisited(TreeIDEnumaration.Template);
                    break;
                case TreeMenuEventArgs.EventType.CopyRequest:
                    CopyTemplates(TreeIDEnumaration.Template);
                    break;
                case TreeMenuEventArgs.EventType.PasteRequest:
                    PasteTemplates(TreeIDEnumaration.Template);
                    break;
                case TreeMenuEventArgs.EventType.ToggleProductWorkflowRequest:
                    ToggleProductWorkflowFlag(e.Data as ProductWorkflowEventData);
                    break;
            }
        }

        private void CopyTemplates(TreeIDEnumaration treeId)
        {
            if (!AppData.Instance.CurrentUser.IsInAllRoles(AddOnsEnumeration.TreeCopyPaste, AddOnsEnumeration.Design))
                return;

            var tree = GetTreeFromTreeId(treeId);

            if (tree.SelectedNodes.Count > 0)
            {
                foreach (var folderNode in tree.SelectedNodes.Where(node => ((ShoppaTreeNode)node.Tag).IsFolder))
                    tree.PopulateSubtree(folderNode);

                var treeSelection = new TreeSelection(tree.SelectedNodes);

                DataFormats.Format format = DataFormats.GetFormat(typeof(TreeSelection).ToString());
                DataObject obj = new DataObject(format.Name, treeSelection.Serialize());
                Clipboard.SetDataObject(obj);
            }
        }

        private void PasteTemplates(TreeIDEnumaration treeId)
        {
            var tree = GetTreeFromTreeId(treeId);
            if (tree.SelectedNodes.Count != 1)
                return;

            IDataObject obj = Clipboard.GetDataObject();
            if (obj == null)
                return;

            if (!obj.GetDataPresent(typeof(TreeSelection).ToString()))
                return;

            DataFormats.Format format = DataFormats.GetFormat(typeof(TreeSelection).ToString());
            var copiedSelection = TreeSelection.Deserialize((byte[])obj.GetData(format.Name));

            var selectedNode = (ShoppaTreeNode)tree.SelectedNode.Tag;
            var targetLocationGuid = selectedNode.IsFolder ? selectedNode.ContentGuid : ((ShoppaTreeNode)selectedNode.Parent).ContentGuid;
            var targetLocationCapsule = FolderController.GetEncapsulatedFolder(targetLocationGuid);

            var templatePasteHelper = new TemplatePasteHelper(tree, targetLocationCapsule, copiedSelection, this);
            templatePasteHelper.Paste();

            RefreshTreesAndContentViews();
            _syncmanager.BeginSynchronize();
        }

        private void ChangeVisibility(TreeIDEnumaration treeId)
        {
            var tree = GetTreeFromTreeId(treeId);

            // We do not allow any changes to be made to nodes that we do not own
            if (!tree.GetSelectedEditableNodes().Any())
                return;

            using (var vp = new VisibilityForm())
            {
                if (treeId == TreeIDEnumaration.Template || treeId == TreeIDEnumaration.Picture)
                {
                    var primaryNode = (ShoppaTreeNode)tree.SelectedNode.Tag;

                    vp.SetMaterial(
                        type: primaryNode.IsFolder ? CapsuleType.Folder : treeId.ToCapsuleType(),
                        material: primaryNode
                    );
                }
                else if (treeId == TreeIDEnumaration.CampaignRecording)
                {
                    var primaryNode = (CampaignNode)tree.SelectedNode.Tag;

                    vp.SetMaterial(
                        type: primaryNode.NodeType == CampaignNodeType.Folder ? CapsuleType.Folder : treeId.ToCapsuleType(),
                        material: primaryNode
                    );
                }

                if (vp.ShowDialog() == DialogResult.OK && vp.HasAnyValueChanged)
                {
                    //We can't build the list of selectedNodes before the dialog is closed,
                    //    because a redraw of the tree (ie. during sync) may have regenerated it
                    var selectedNodes = tree.GetSelectedEditableNodes();
                    //TODO: Assert the nodes list is the same as when the dialog was opened.

                    ChangeVisibility(
                        tree,
                        selectedNodes,
                        vp.StartDate,
                        vp.StopDate,
                        vp.IsPublic,
                        vp.SelectedCustomerGroups.Select(g => g.Guid).ToList(),
                        vp.HasCustomerGroupsChanged, 
                        vp.PublicOrDateRangeChanged
                    );
                }
            }
        }

        private void ToggleProductWorkflowFlag(ProductWorkflowEventData eventData)
        {
            if (eventData == null || !eventData.Guids.Any())
                return;

            var changes = new List<ItemChangeInfo>();

            foreach (var guid in eventData.Guids)
            {
                var change = new ItemChangeInfo
                {
                    ChangeType = ItemChangeType.CategoryChanged,
                    ItemGuid = guid,
                };

                if (eventData.ShowInProductWorkflow)
                {
                    change.CategoriesAssigned |= TemplateCategories.ProductWorkflow;
                }
                else
                {
                    change.CategoriesRemoved |= TemplateCategories.ProductWorkflow;
                }
                changes.Add(change);
            }

            new TreeController2(AppData.Instance).UpdateLocalDB(TreeIDEnumaration.Template, changes);
            RefreshTreesAndContentViews();
            _syncmanager.BeginSynchronize();
        }

        private void ChangeVisibility(ShoppaTree2 tree, IList<TreeNodeAdv> nodes, DateTime startDate, DateTime stopDate, bool isPublic, IList<Guid> selectedCustomerGroupGuids, bool hasCustomerGroupsChanged, bool publicOrDateRangeChanged)
        {
            var treeController = new TreeController2(AppData.Instance);
            var pictureController = new PictureController(AppData.Instance, PictureThumbnailCacheInstance.Instance);

            List<ItemChangeInfo> changes = new List<ItemChangeInfo>();
            foreach (var node in nodes)
            {
                var shoppaNode = node.Tag as ShoppaTreeNode;
                var change = new ItemChangeInfo
                {
                    ChangeType = ItemChangeType.VisibilityChanged,
                    StartDate = startDate,
                    StopDate = stopDate,
                    IsPublic = isPublic,
                    IsFolder = shoppaNode.IsFolder,
                    ItemGuid = shoppaNode.ContentGuid,
                    ParentGuid = (node.Parent.Tag as ShoppaTreeNode)?.ContentGuid ?? Guid.Empty,
                    PublicOrDateRangeChanged = publicOrDateRangeChanged,

                    CustomerGroups = selectedCustomerGroupGuids,
                    CustomerGroupsChanged = hasCustomerGroupsChanged ? ChangeBehavior.Replace : ChangeBehavior.None
                };

                if (publicOrDateRangeChanged)
                {
                    bool wasAvailable = shoppaNode.IsPublic && (shoppaNode.StartDate <= DateTime.Today && shoppaNode.StopDate >= DateTime.Today);
                    bool willBeAvailable = isPublic && (startDate <= DateTime.Today && stopDate >= DateTime.Today);
                    if (change.IsFolder && wasAvailable && !willBeAvailable)
                    {
                        var result = MessageBoxes.ConfirmChangeFolderVisibility(this, shoppaNode.Text, tree.TreeId, nodes.Count > 1);
                        if (result == System.Windows.Forms.DialogResult.No)
                            continue;
                        else if (result == System.Windows.Forms.DialogResult.Cancel)
                            return;
                    }
                    else if (!change.IsFolder && ((change.IsPublic && change.IsPublic != shoppaNode.IsPublic) || (change.StartDate <= DateTime.Now && shoppaNode.StartDate > DateTime.Now)))
                    {
                        List<EncapsulatedPicture> pictureList = new List<EncapsulatedPicture>();
                        var tpImageGuids = TemplateController.GetTemplatePictures(shoppaNode.ContentGuid).Where(p => p != Guid.Empty);
                        foreach (var guid in tpImageGuids)
                        {
                            var ep = PictureController.GetEncapsulatedPictureLight(guid);
                            if (ep != null && (!ep.IsPublic || ((change.StartDate <= DateTime.Now && shoppaNode.StartDate > DateTime.Now) && ep.StartDate > DateTime.Now)))
                                pictureList.Add(ep);
                        }

                        if (pictureList.Count > 0)
                        {
                            var res = MessageBoxes.ConfirmChangeTemplateVisibility(this, shoppaNode.Text, change.IsPublic && change.IsPublic != shoppaNode.IsPublic);
                            if (res == System.Windows.Forms.DialogResult.Yes)
                            {
                                // Set all images to public
                                foreach (var ep in pictureList)
                                {
                                    ep.StartDate = startDate;
                                    ep.StopDate = stopDate;
                                    ep.IsPublic = isPublic;
                                    pictureController.SavePictureLightData(ep);

                                    setTreeNodeVisibility(TreeIDEnumaration.Picture, ep.FolderGuid, startDate, stopDate, isPublic);
                                }
                            }
                            else if (res == System.Windows.Forms.DialogResult.Cancel)
                                return;
                        }
                    }
                }
                changes.Add(change);
            }

            if (changes.Any())
            {
                if (publicOrDateRangeChanged)
                {
                    // If we made it visible, we need to make sure the entire path is visible
                    if (isPublic && startDate <= DateTime.Today && stopDate >= DateTime.Today)
                    {
                        foreach (var node in nodes)
                        {
                            object[] pathElements = tree.GetPath(node).FullPath;
                            // get the folders between the changed material and the root
                            if (pathElements.Length > 2)
                            {
                                for (int walker = pathElements.Length - 2; walker > 0; walker--)
                                {
                                    var parentNode = (ShoppaTreeNode)pathElements[walker - 1];
                                    var shoppaNode = (ShoppaTreeNode)pathElements[walker];

                                    if (!shoppaNode.IsPublic || !shoppaNode.IsAvailable)
                                    {
                                        var change = new ItemChangeInfo
                                        {
                                            ChangeType = ItemChangeType.VisibilityChangedNoKids,
                                            StartDate = startDate,
                                            StopDate = stopDate,
                                            IsPublic = isPublic,
                                            IsFolder = true,
                                            ItemGuid = shoppaNode.ContentGuid,
                                            ParentGuid = parentNode.ContentGuid,
                                            PublicOrDateRangeChanged = publicOrDateRangeChanged,
                                        };

                                        changes.Add(change);
                                    }
                                }
                            }
                        }
                    }
                }
                // Update database
                treeController.UpdateLocalDB(tree.TreeId, changes);

                // Rebuild tree to reflect changes in database
                // This ensures that visibility and customer group icons get updated
                RefreshTreesAndContentViews();

                foreach (var c in changes)
                {
                    TryGetOpenTemplate(c.ItemGuid, out var baseForm, out bool _, out bool openInDesign);
                    if (openInDesign)
                    {
                        (baseForm as FormTemplateDesign).UpdateTemplateVisibility(c);
                        break;
                    }
                }
                // Make sure that the content view is refreshed as well
                if (tree.TreeId == TreeIDEnumaration.Template)
                    reselectCurrentTreeNode(templateSearch.TemplateTree);
                else if(tree.TreeId == TreeIDEnumaration.CampaignRecording)
                    reselectCurrentTreeNode(templateSearch.CampaignTree);
                else if (tree.TreeId == TreeIDEnumaration.Picture)
                    reselectCurrentPictureNode();
                _syncmanager.BeginSynchronize();
            }
        }

        void pictureTree_SelectionChanged(object sender, EventArgs e)
        {
            var tree = currentTree = sender as TreeViewAdv;
            var node = currentNode = tree.SelectedNode;

            // GUI Access control
            EnsureGuiVisibilityBasedOnNode(tree, node);

            if (node == null)
                return;

            // Only Folders exist in pictureTree.
            if (node.IsLeaf)
                return;

            EnsureChildrenLoaded(node);

            var folderGuid = Guid.Empty;
            // Hidden root node have node.Tag == null
            if (node.Tag is ShoppaTreeNode)
                folderGuid = (node.Tag as ShoppaTreeNode).ContentGuid;

            // Only add content if node has changed
            if (forceUpdateAfterBrowsing || pictureSearch.PicturesImageListView.FolderGuid != folderGuid)
            {
                pictureSearch.PicturesImageListView.FolderGuid = folderGuid;
                pictureSearch.PicturesImageListView.Editable = (tree.Model as PictureTreeModel).NodeIsInEditableTree(node);
                pictureSearch.PicturesImageListView.SetupPictureContent((tree.Model as PictureTreeModel).PicturesUnderNode(node).OrderBy(x => x.Name));
                UpdateModifyPictureButton(enabled: false);
                forceUpdateAfterBrowsing = false;
            }
        }

        void pictureTree_DropEvent(object sender, TreeDropEventArgs e)
        {
            if (e.Type == TreeDropEventArgs.EventType.Pictures)
            {
                var tree = GetTreeFromTreeId(TreeIDEnumaration.Picture);
                var targetNode = tree.AllNodes.FirstOrDefault(n => (n.Tag as ShoppaTreeNode).ContentGuids.Contains(e.DroppedOnFolder));
                if (targetNode != null)
                {
                    List<EncapsulatedPicture> epList = new List<EncapsulatedPicture>();
                    foreach (var guid in e.Guids)
                    {
                        var picture = PictureController.GetEncapsulatedPictureLight(guid);
                        if (picture != null)
                            epList.Add(picture);
                    }
                    var failedPictures = new List<Guid>();
                    processPictureFileNames(epList, targetNode, out failedPictures);

                    // Any picture we did not want to rename should be removed from the continued execution so that is not moved anyway
                    foreach (var guid in failedPictures)
                    {
                        if (e.Guids.Contains(guid))
                            e.Guids.Remove(guid);
                    }
                }
                // Do not remove items if moved to same location
                if (pictureSearch.PicturesImageListView.FolderGuid != e.DroppedOnFolder)
                    pictureSearch.PicturesImageListView.RemovePictureContent(e.Guids, e.DroppedOnFolder);
            }
            else if (e.Type == TreeDropEventArgs.EventType.ImportPicturesFromFile)
            {
                var tree = GetTreeFromTreeId(TreeIDEnumaration.Picture);
                var targetNode = tree.AllNodes.FirstOrDefault(n => (n.Tag as ShoppaTreeNode).ContentGuids.Contains(e.DroppedOnFolder));
                if (targetNode != null)
                {
                    try
                    {
                        var filesToImport = new MediaFilter(AppData.Instance.CurrentUser).GetSourcesByFileExtention(e.FilePaths).ToList();

                        var filteredSources = FilterLocalVideoFilesBySize(filesToImport);
                        if (!filteredSources.Any()) return;

                        ProcessPictureFileNamesToImport(filteredSources, targetNode);
                    }
                    catch (Exception ex)
                    when (ex is FileNotFoundException || ex is InvalidOperationException)
                    {
                        ShowMessageOnMediaFileException(ex);
                    }
                }
            }
            else if (e.Type == TreeDropEventArgs.EventType.ImportPicturesFromWeb)
            {
                var tree = GetTreeFromTreeId(TreeIDEnumaration.Picture);
                var targetNode = tree.AllNodes.FirstOrDefault(n => (n.Tag as ShoppaTreeNode).ContentGuids.Contains(e.DroppedOnFolder));
                string url = e.FilePaths[0];
                bool isValidVideoUrl = DSController.IsVideoUrlValid(url);
                IMediaSource mediaSource = null;
                if (isValidVideoUrl)
                {
                    mediaSource = new VideoSource(url, true);
                }
                else
                {
                    string tempFilePath = FileDownloader.Download(url);
                    if (!string.IsNullOrEmpty(tempFilePath)) 
                        mediaSource = new PictureSource(tempFilePath);
                }

                if (targetNode == null || mediaSource == null)
                {
                    CMessageBox.Show(string.Format(MLS.Instance.TranslateText("PI_PictureImportFormUrlFailed"), e.FilePaths[0]));
                    return;
                }

                try
                {
                    ProcessPictureFileNamesToImport(mediaSource.Yield().ToList(), targetNode);
                }
                catch (Exception ex)
                when (ex is FileNotFoundException || ex is InvalidOperationException)
                {
                    ShowMessageOnMediaFileException(ex, mediaSource.OriginalPath);
                }
            }
        }

        private void ShowMessageOnMediaFileException(Exception ex, string filePath = null)
        {
            AddOnsEnumeration[] flowAddOns = { AddOnsEnumeration.Flow, AddOnsEnumeration.FlowDesign };
            bool onlyPictures = !AppData.Instance.CurrentUser.IsInAnyRole(flowAddOns);

            if (ex is FileNotFoundException)
            {
                var messageTemplate = onlyPictures
                    ? MLS.Instance.TranslateText("PI_PictureImportFileUnavailable")
                    : MLS.Instance.TranslateText("PI_MediaImportFileUnavailable");
                DisplayMessageBoxForFileImportError(filePath, messageTemplate);
            }
            else if (ex is InvalidOperationException)
            {
                var messageTemplate = onlyPictures
                    ? MLS.Instance.TranslateText("PI_PictureImportFileConversionFailed")
                    : MLS.Instance.TranslateText("PI_MediaImportFileConversionFailed");
                DisplayMessageBoxForFileImportError(filePath, messageTemplate);
            }
            else
            {
                throw ex;
            }
        }

        private static void DisplayMessageBoxForFileImportError(string filePath, string messageTemplate)
        {
            var message = string.Empty;
            if (string.IsNullOrWhiteSpace(filePath))
                message = messageTemplate.Replace("'{0}' ", string.Empty);
            else
                message = string.Format(messageTemplate, filePath);
            CMessageBox.Show(message);
        }

        void pictureTree_MenuEvent(object sender, TreeMenuEventArgs e)
        {
            switch (e.Type)
            {
                case TreeMenuEventArgs.EventType.AddNewFolderRequest:
                    AddNewFolder(TreeIDEnumaration.Picture);
                    break;
                case TreeMenuEventArgs.EventType.AddNewPicturesRequest:
                    AddMediaFiles(MediaImportType.Picture);
                    break;
                case TreeMenuEventArgs.EventType.AddNewVideosRequest:
                    AddMediaFiles(MediaImportType.Video);
                    break;
                case TreeMenuEventArgs.EventType.DeleteFolderRequest:
                    DeleteContentOrFolder(TreeIDEnumaration.Picture);
                    break;
                case TreeMenuEventArgs.EventType.SetVisibilityRequest:
                    ChangeVisibility(TreeIDEnumaration.Picture);
                    break;
                case TreeMenuEventArgs.EventType.MarkedVisibleRequest:
                    MarkVisited(TreeIDEnumaration.Picture);
                    break;
                default:
                    break;
            }
        }

        private bool QueueShouldBeVisible(TreeNodeAdv node)
        {
            if (node.Tag is PrintNode printNode)
            {
                //Don't allow queuing the entire archive or active print queue
                if (node == printTree.Root || printTree.Root.Children.Contains(node))
                    return false;

                // Disable add to queue if we are in product selection mode and don't have a sign selected in the tree
                if (printNode.NodeType != PrintNodeType.Sign && GetProductSearchResultControl().SelectionCount > 0)
                    return false;
            }
            else if (node.Tag is CampaignNode campaignNode)
            {
                if (campaignNode.NodeType == CampaignNodeType.CampaignRoot)
                    return false;
            }

            if (node.Children.Count == 0)
                return false;

            if (dockerProductSearch?.SelectedProducts?.Any(p => p.VisibleTexts.Any(d => d.Name == "DisplayGroup")) ?? false)
                return false;

            return true;
        }

        void recordingHoverToolstrip_CreateCopy(object sender, EventArgs e)
        {
            var campaignId = CurrentSelectedCampaignId();
            if (currentTree is ShoppaTree2 && (currentTree.SelectedNode.Parent.Parent.Tag as CampaignNode).NodeType == CampaignNodeType.Campaign)
            {
                AddToQueue(QueuedOutputStateEnumaration.Campaign, 1); //method will detect and switch to CampaignQueue if target is in edit mode
                if (!CampaignModel.InEditMode)
                {
                    UpdateRevertCampaignButton(campaignId);
                }
                templateSearch.FindVisibleCampaignTreeNodeCount();
                templateSearch.AdjustTreeView();
            }
            else if (currentTree is PrintTree && (currentTree.SelectedNode.Parent.Parent.Tag as PrintNode).NodeType == PrintNodeType.CurrentRoot)
            {
                AddToQueue(QueuedOutputStateEnumaration.PrinterQueue, 1);
            }

            UpdateQueueCountBoxes();
        }

        private void SetSignCapsuleCopies(EncapsulatedQueuedSign signCapsule, PrintCopiesEvent printCopiesEvent)
        {
            switch (printCopiesEvent)
            {
                case PrintCopiesEvent.Increase:
                    signCapsule.Copies = signCapsule.Copies == (ushort)300 ? (ushort)300 : (ushort)(signCapsule.Copies + 1);
                    break;
                case PrintCopiesEvent.Decrease:
                    if (signCapsule.Copies == 0)
                        break;
                    signCapsule.Copies = signCapsule.Copies == (ushort)1 ? (ushort)1 : (ushort)(signCapsule.Copies - 1);
                    break;
            }
            using (var oqDal = Embedded.Factory.OutputQueue)
            {
                oqDal.SaveItem(signCapsule, false);
            }
        }

        private void clearRecordingHoverToolstrip()
        {
            recordingHoverToolstrip.Visible = false;
            this.Controls.Remove(recordingHoverToolstrip);
            recordingHoverToolstrip = null;
        }

        private void clearVisibilityDetailsFanout()
        {
            visibilityDetailsFanout.Visible = false;
            this.Controls.Remove(visibilityDetailsFanout);
            visibilityDetailsFanout = null;
        }

        private int findVisibleNodesBeforeSelected(TreeNodeAdv selectedNode, TreeNodeAdv node, out bool completed)
        {
            int nodeCount = 1;
            completed = false;

            if (node.Children.Contains(selectedNode))
            {
                nodeCount = node.Children.IndexOf(selectedNode);
                completed = true;
            }
            else
            {
                foreach (var child in node.Children)
                {
                    if (completed)
                        break;

                    if (child.Children.Count == 0 || !child.IsExpanded)
                    {
                        nodeCount += 1;
                        continue;
                    }

                    nodeCount += findVisibleNodesBeforeSelected(selectedNode, child, out completed);
                }
            }
            return nodeCount;
        }

        private void UpdatePrintTreeAfterSync(IEnumerable<KeyValuePair<CapsuleType, SyncData<Capsule>>> items)
        {
            if (!AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
                return;

            var capsules = items
                .Where(pair => pair.Key == CapsuleType.OutputQueue)
                .SelectMany(pair => pair.Value.Items)
                .Cast<EncapsulatedQueuedSign>()
                .Where(c => c.OrderID != null)
                .ToList();

            var deletedOrderIds = capsules
                .Where(c => c.IsDeleted)
                .Select(c => (int)c.OrderID)
                .ToHashSet();

            printTree.RemoveOrders(deletedOrderIds);

            var signs = capsules.FindAll(c => !c.IsDeleted);

            var notPrintedOrderIds = signs
                .Where(s => s.State == QueuedOutputStateEnumaration.PrintshopOrder)
                .Select(s => (int)s.OrderID)
                .ToHashSet();

            var printedOrderIds = signs
                .Where(s => s.State == QueuedOutputStateEnumaration.PrintshopPrinted)
                .Select(s => (int)s.OrderID)
                .ToHashSet();

            printTree.AddOrderToNotPrinted(notPrintedOrderIds);
            printTree.AddOrderToPrinted(printedOrderIds);
        }

        internal bool IsFormTemplateDesignInstanceActive(FormTemplateDesign formTemplateDesign)
        {
            return dockingPanel.ActiveContent == formTemplateDesign;
        }

        private void findNearestVisibleTreeNode(ShoppaTree2 targetTree, Guid previouslySelectedFolderGuid, List<ItemChangeInfo> foldersDeletedInSync)
        {
            var localFolderStructure = FolderController.GetAllParentFolders(previouslySelectedFolderGuid);
            TreeNodeAdv nodeToSelect = null;
            List<Guid> pathToGuid;
            if (localFolderStructure.Count == 0)
            {
                // folders are not available locally.
                // look for guid in folders deleted in sync.
                var nextGuid = previouslySelectedFolderGuid;
                pathToGuid = new List<Guid>();
                pathToGuid.Add(nextGuid);
                for (int i = foldersDeletedInSync.Count - 1; i >= 0; i--)
                {
                    var curDeletedFolder = foldersDeletedInSync[i];
                    if (curDeletedFolder.ItemGuid == nextGuid)
                    {
                        nextGuid = curDeletedFolder.ParentGuid;
                        pathToGuid.Add(nextGuid);
                    }
                }

                // look for the last folders locally.
                var theRest = FolderController.GetAllParentFolders(nextGuid);
                if (theRest.Count > 1)
                    pathToGuid.AddRange(theRest.Select(tr => tr.Guid).Skip(1)); // skip first. We are already aware of that guid.
            }
            else
                pathToGuid = localFolderStructure.Select(pf => pf.Guid).ToList();
            // try to select nodes as far as they are visible
            // if none of the guids in pathToGuid are available
            // we will select root node.
            nodeToSelect = targetTree.Root;
            for (int i = pathToGuid.Count - 1; i >= 0; i--)
            {
                bool foundNew = false;
                foreach (var child in nodeToSelect.Children)
                {
                    if (child.Tag is CampaignNode)
                        continue;
                    if ((child.Tag as ShoppaTreeNode).ContentGuids.Contains(pathToGuid[i]))
                    {
                        nodeToSelect = child;
                        foundNew = true;
                        break;
                    }
                }
                if (!foundNew)
                {
                    nodeToSelect = targetTree.Root.Children.FirstOrDefault();
                    break;
                }
            }
            // select node in tree. its events will cause contentView to update as well.
            Invoke(new Action(() => targetTree.SelectedNode = nodeToSelect));
        }

        /// <summary>
        /// Get OrderBy depth for hierarchy sort, parent first.
        /// Based on answer in http://stackoverflow.com/questions/8014111/sorting-list-by-hierarchy
        /// </summary>
        int getDepth(List<ItemChangeInfo> list, ItemChangeInfo item)
        {
            if (item == null)
                return 0;
            return getDepth(list, list.FirstOrDefault(p => p.ItemGuid == item.ParentGuid)) + 1;
        }

        private void reselectCurrentPrintNode()
        {
            if (printTree.CurrentNode == null)
                return;

            var oldCurrent = printTree.CurrentNode;
            printTree.CurrentNode.IsSelected = false;
            forceUpdateAfterBrowsing = true;
            oldCurrent.IsSelected = true;
        }

        private void SelectNodeWithForceUpdate(TreeNodeAdv treeNodeAdv)
        {
            forceUpdateAfterBrowsing = true;
            treeNodeAdv.IsSelected = true;
            if (recordingHoverToolstrip != null)
                clearRecordingHoverToolstrip();
        }

        private void reselectCurrentTreeNode(ShoppaTree2 tree)
        {
            if (tree.CurrentNode == null)
                return;

            var oldCurrent = tree.CurrentNode;
            tree.CurrentNode.IsSelected = false;
            forceUpdateAfterBrowsing = true;
            oldCurrent.IsSelected = true;
        }

        private void reselectCurrentPictureNode()
        {
            if (pictureSearch.PictureTree.CurrentNode == null)
                return;

            var oldCurrent = pictureSearch.PictureTree.CurrentNode;
            pictureSearch.PictureTree.CurrentNode.IsSelected = false;
            forceUpdateAfterBrowsing = true;
            oldCurrent.IsSelected = true;
        }


        private bool IsValidTreeNodeSign(TreeNodeAdv treeNodeAdv)
        {
            if (treeNodeAdv.Tag is CampaignNode campaignNode)
            {
                return campaignNode.NodeType == CampaignNodeType.Sign && (campaignNode.CampaignId > 0 || (campaignNode.Parent.Parent as CampaignNode).NodeType == CampaignNodeType.CurrentRoot);
            }
            else if (treeNodeAdv.Tag is PrintNode printNode)
            {
                return printNode.NodeType == PrintNodeType.Sign && (printNode.CampaignId > 0 || (printNode.Parent.Parent as PrintNode).NodeType == PrintNodeType.CurrentRoot);
            }
            return false;

        }

        private Point GetMouseLocation(TreeViewAdv tree, MouseEventArgs e)
        {
            int nodesBefore = findVisibleNodesBeforeSelected(tree.SelectedNode, tree.Root, out _);
            int posX = e.Location.X + 15;
            int posY = (nodesBefore * tree.RowHeight) - (tree.FirstVisibleRow * tree.RowHeight) - RECORDING_HOVER_TOOLSTRIP_HEIGHT;
            Point location = tree.PointToScreen(new Point(posX, posY));
            return location;
        }
        #endregion

        void panelRight_Resize(object sender, EventArgs e)
        {
            ResizeRightDockerFanouts();
        }

        private void ResizeRightDockerFanouts()
        {
            if (searchAndReplaceDocker != null && searchAndReplaceDocker.Visible)
            {
                var size = new Size(panelRight.Width, panelRight.Height - 9);
                var location = new Point(panelRight.Location.X, panelRight.Location.Y + 9);
                _scrollholderSearchAndReplace.Size = size;
                _scrollholderSearchAndReplace.Location = location;
                searchAndReplaceDocker.Width = _scrollholderSearchAndReplace.PreferedContentWidth;
            }
        }

        void contentView_Changed(object sender, ChangeEventArgs e)
        {
            switch (e.Type)
            {
                case ChangeEventArgs.EventType.ItemChange:
                    if (e.TreeId == TreeIDEnumaration.Template)
                        templateSearch.TemplateTree.SelectChild(e.Guid);
                    if (e.TreeId == TreeIDEnumaration.CampaignRecording)
                        templateSearch.CampaignTree.SelectChild(e.Guid);
                    if (e.TreeId == TreeIDEnumaration.PrinterQueue)
                    {
                        templatesImageListView.SelectionChanged -= contentView_SelectionChanged;
                        templatesImageListView.ClearSelection();
                        templatesImageListView.SelectionChanged += contentView_SelectionChanged;
                        if (currentTree is ShoppaTree2)
                            templateSearch.TemplateTree.SelectChild(e.Guid);
                        else if (currentTree is TemplateSearchTree)
                            templateSearch.TemplateSearchResult.TemplateSearchTree.SelectChild(e.Guid);
                        else
                            printTree.SelectChild(e.Guid);
                    }
                    break;
                case ChangeEventArgs.EventType.Unknown:
                    throw new NotImplementedException();
                case ChangeEventArgs.EventType.PictureChange:
                    PictureChangeRequested(e.Guid);
                    break;
                case ChangeEventArgs.EventType.PictureFileChange:
                    ChangeMediaFile(e.Guid, MediaImportType.Picture);
                    break;
                case ChangeEventArgs.EventType.VideoFileChange:
                    ChangeMediaFile(e.Guid, MediaImportType.Video);
                    break;
                case ChangeEventArgs.EventType.PictureAddRequest:
                    AddMediaFiles(MediaImportType.Picture);
                    break;
                case ChangeEventArgs.EventType.VideoAddRequest:
                    AddMediaFiles(MediaImportType.Video);
                    break;
                case ChangeEventArgs.EventType.PictureNameChanged:
                case ChangeEventArgs.EventType.PictureDeleted:
                    // Done in contentView. Still need to synchronize though
                    _syncmanager.BeginSynchronize();
                    break;
                case ChangeEventArgs.EventType.PictureImageAdjustmentsChanged:
                    // Done in contentView. Still need to synchronize and update template thumbnails though

                    List<Guid> templateGuids;

                    using (var factory = Embedded.Factory.Template)
                        templateGuids = factory.GetTemplatesWithSpecificPicture(e.Guid);

                    var formCapsule = GetActiveFormTemplateBase()?.GetTemplateCapsule();
                    if (formCapsule != null) templateGuids.Add(formCapsule.Guid);

                    RefreshPictureOnTemplates(templateGuids, e.Guid);

                    _syncmanager.BeginSynchronize();
                    break;
                case ChangeEventArgs.EventType.VisibilityChanged:
                    // Done in contentView. Still need to synchronize though
                    if (e.TreeId != TreeIDEnumaration.Unknown && e.Guid != null)
                        setTreeNodeVisibility(e.TreeId, e.Guid, e.StartDate, e.StopDate, e.IsPublic);
                    _syncmanager.BeginSynchronize();
                    break;
                case ChangeEventArgs.EventType.MetaProductChange:
                    if (e is MetaValueChangeEventArgs me)
                    {
                        RefreshMetaProductOnOpenTemplates();
                        TemplateController.ClearTemplateThumbsContainingMetaValue(me.MetaValues.Select(mv => mv.FieldIdentifier));
                        _syncmanager.BeginSynchronize();
                    }
                    break;
                default:
                    break;
            }
        }

        private void RefreshMetaProductOnOpenTemplates()
        {
            if (this.InvokeRequired)
            {
                this.invoke(RefreshMetaProductOnOpenTemplates);
                return;
            }

            if (dockingPanel == null)
                return;

            foreach (var content in dockingPanel.DockedContent)
            {
                if (content is FormTemplateBase templateContent)
                {
                    templateContent.RefreshMetaProduct();
                }
            }

            Messenger.Send(this, Messenger.Events.RequestLayoutRefresh, null);
        }

        /// <summary>
        /// Gets a list of GuidParentGuids from the wanted node all the way up to but not including the root node.
        /// </summary>
        /// <param name="nodeGuid">The Guid of the wanted node</param>
        /// <param name="guidList">The list to populate with the GuidParentGuids</param>
        void findNodeParents(Guid nodeGuid, List<GuidParentGuid> guidList)
        {
            var ef = FolderController.GetEncapsulatedFolder(nodeGuid);
            if (ef.FolderGuid != Guid.Empty)
            {
                guidList.Add(new GuidParentGuid(nodeGuid, ef.FolderGuid));
                findNodeParents(ef.FolderGuid, guidList);
            }
        }

        /// <summary>
        /// Looks for the wanted node in the tree recursively until found
        /// </summary>
        /// <param name="currentNode">The current tree node</param>
        /// <param name="gpgList">A list of GuidParentGuids to help the search</param>
        /// <param name="nodeToFind">The Guid of the wanted node</param>
        /// <param name="foundNode">The found node</param>
        void getTreeNode(TreeNodeAdv currentNode, List<GuidParentGuid> gpgList, Guid nodeToFind, out TreeNodeAdv foundNode)
        {
            if (currentNode.Children.Any(n => (n.Tag as ShoppaTreeNode).ContentGuids.Contains(nodeToFind)))
                foundNode = currentNode.Children.Where(n => (n.Tag as ShoppaTreeNode).ContentGuids.Contains(nodeToFind)).FirstOrDefault();
            else
            {
                bool collapseChild = false;
                var childNode = currentNode.Children.Where(n => gpgList.Any(g => (n.Tag as ShoppaTreeNode).ContentGuids.Contains(g.ParentGuid))).FirstOrDefault();
                if (childNode != null)
                {
                    if (!childNode.IsExpanded)
                    {
                        collapseChild = true;
                        childNode.Expand();
                    }
                    getTreeNode(childNode, gpgList, nodeToFind, out foundNode);
                    if (collapseChild) childNode.Collapse();
                }
                else
                    foundNode = null;
            }
        }

        private void markChildrenAsMoved(TreeNodeAdv parentNode, List<ItemChangeInfo> args, TreeIDEnumaration treeId)
        {
            bool shouldCollapseAfter = false;
            if (!parentNode.IsExpanded)
            {
                shouldCollapseAfter = true;
                parentNode.Expand();
            }

            if (treeId == TreeIDEnumaration.Picture)
            {
                // Get all pictures inside this folder and mark them as newly changed
                List<FolderStructureCapsule> pictures = new List<FolderStructureCapsule>();
                using (var picDal = Embedded.Factory.Picture)
                {
                    int? customerIdToOverrideVisibility = new AvailabilityController(AppData.Instance).CustomerIdOverrideFor(treeId);
                    pictures = picDal.GetFolderContent((parentNode.Tag as ShoppaTreeNode).ContentGuids, customerIdToOverrideVisibility);
                }

                foreach (var pic in pictures)
                {
                    ItemChangeInfo infoPic = new ItemChangeInfo();
                    infoPic.ChangeType = ItemChangeType.ItemMoved;
                    infoPic.ParentGuid = (parentNode.Tag as ShoppaTreeNode).ContentGuid;
                    infoPic.ItemGuid = pic.Guid;
                    args.Add(infoPic);
                }
            }

            foreach (TreeNodeAdv child in parentNode.Children)
            {
                ItemChangeInfo info = new ItemChangeInfo();
                info.ChangeType = ItemChangeType.ItemMoved;
                info.ParentGuid = (parentNode.Tag as ShoppaTreeNode).ContentGuid;
                info.ItemGuid = (child.Tag as ShoppaTreeNode).ContentGuid;
                info.IsFolder = !child.IsLeaf;
                args.Add(info);

                if (info.IsFolder)
                    markChildrenAsMoved(child, args, treeId);
            }

            if (shouldCollapseAfter)
                parentNode.Collapse();
        }            

        /// <summary>
        /// Changes the visibility of tree nodes and any parents if need be
        /// </summary>
        void setTreeNodeVisibility(
            TreeIDEnumaration treeId, Guid nodeGuid,
            DateTime startDate, DateTime stopDate,
            bool isPublic)
        {
            var treeController = new TreeController2(AppData.Instance);

            var tree = GetTreeFromTreeId(treeId);
            TreeNodeAdv selectedFolder = null;
            foreach (var node in tree.Root.Children)
            {
                if ((node.Tag as ShoppaTreeNode).ContentGuids.Contains(nodeGuid))
                {
                    selectedFolder = node;
                    break;
                }
                else
                {
                    var guidList = new List<GuidParentGuid>();
                    findNodeParents(nodeGuid, guidList);
                    tree.SuspendLayout();
                    if (!node.IsExpandedOnce)
                        node.Expand();
                    getTreeNode(node, guidList, nodeGuid, out selectedFolder);
                    tree.ResumeLayout();

                    if (selectedFolder != null)
                        break;
                }
            }

            List<ItemChangeInfo> changes = new List<ItemChangeInfo>();

            // If we made it visible, we need to make sure the entire path is visible
            if (isPublic && startDate <= DateTime.Today && stopDate >= DateTime.Today)
            {
                object[] pathElements = tree.GetPath(selectedFolder).FullPath;
                // get the folders between the changed material (which in this case is not a tree node) and the root
                if (pathElements.Length > 1)
                {
                    for (int walker = pathElements.Length - 1; walker > 0; walker--)
                    {
                        var parentNode = (ShoppaTreeNode)pathElements[walker - 1];
                        var shoppaNode = (ShoppaTreeNode)pathElements[walker];

                        if (!shoppaNode.IsPublic || !shoppaNode.IsAvailable)
                        {
                            var change = new ItemChangeInfo
                            {
                                ChangeType = ItemChangeType.VisibilityChangedNoKids,
                                StartDate = startDate,
                                StopDate = stopDate,
                                IsPublic = isPublic,
                                IsFolder = true,
                                ItemGuid = shoppaNode.ContentGuid,
                                ParentGuid = parentNode.ContentGuid,
                                PublicOrDateRangeChanged = true,
                            };
                            changes.Add(change);
                        }
                    }
                }
            }

            // Update database
            treeController.UpdateLocalDB(treeId, changes);

            // Rebuild tree to reflect changes in database
            // This ensures that visibility and customer group icons get updated
            RefreshTreesAndContentViews();
        }

        void printView_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete)
                isNewKeyPress = true;
        }

        void contentView_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && isNewKeyPress)
            {
                isNewKeyPress = false;
                if (tabControlContent.SelectedTab == tabPageTemplates)
                {
                    if (!this.templatesImageListView.Focused)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    else if (this.templatesImageListView.SelectedItems.Count == 0)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    else if (!buttonDeleteTemplate.Enabled)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }

                    deleteTemplateOrFolderInContentView(TreeIDEnumaration.Template);
                }
                else if (tabControlContent.SelectedTab == tabPagePictures)
                {
                    if (dockingPanel.ActiveContent is FormTemplateBase)
                    {
                        var doc = (FormTemplateBase)dockingPanel.ActiveContent;
                        var elemList = doc.GetGDIElements();
                        if (elemList.Any(el => el is GDI_Picture && el.IsSelected))
                        {
                            isNewKeyPress = true;
                            e.SuppressKeyPress = true;
                            return;
                        }
                    }

                    if (!this.pictureSearch.PicturesImageListView.Focused)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    else if (this.pictureSearch.PicturesImageListView.SelectedItems.Count == 0)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    else if (!buttonDeletePicture.Enabled)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }

                    deletePictureInContentView(TreeIDEnumaration.Picture);
                }
                else if (tabControlContent.SelectedTab == tabPageOutputs)
                {
                    if (!this.templatesImageListView.Focused)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    else if (this.templatesImageListView.SelectedItems.Count == 0)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }
                    else if (!buttonDeleteOutput.Enabled)
                    {
                        isNewKeyPress = true;
                        e.SuppressKeyPress = true;
                        return;
                    }

                    //var campaignRoot = printTree.GetCampaignQueue();
                    var topNode = (printTree.SelectedNodes[0].Tag as PrintNode);
                    // TODO: Move or remove
                    //if (topNode.NodeType == PrintNodeType.CampaignRoot)
                    //{
                    //    if (MessageBox.Show(MLS.Instance.TranslateText("CRT_DeleteMessage"), MLS.Instance.TranslateText("CRT_Delete"), MessageBoxButtons.YesNo) == System.Windows.Forms.DialogResult.Yes)
                    //        deleteFromPrintQueue();
                    //}
                    //else
                    DeleteFromPrintQueue();
                }
            }
        }

        void contentView_SelectionChanged(object sender, EventArgs e)
        {
            if (tabControlContent.SelectedTab == tabPageTemplates)
            {
                // Only enable delete button if the selection contains at least one item which is not the currently open folder.
                // Also enable delete button if we have a selected tree node but nothing selected in the content view.
                if (!templateSearch.TemplateTree.Focused)
                {
                    if (templatesImageListView.SelectedItems.Count == 1 && templatesImageListView.SelectedItems[0].Text == "..")
                        buttonDeleteTemplate.Enabled = false;
                    else if (templatesImageListView.SelectedItems.Count == 0 && templateSearch.TemplateTree.SelectedNodes.Count == 0)
                        buttonDeleteTemplate.Enabled = false;
                    else if (templatesImageListView.SelectedItems.Count == 0 && templateSearch.TemplateTree.SelectedNodes.Count > 0)
                    {
                        templateSearch.TemplateTree.Focus();
                        EnsureGuiVisibilityBasedOnNode(GetTreeFromTreeId(TreeIDEnumaration.Template), templateSearch.TemplateTree.SelectedNode);
                    }
                    else if (templateSearch.CampaignTree.SelectedNode != null && templateSearch.CampaignTree.SelectedNode.Tag is CampaignNode)
                    {
                        var campaignTree = templateSearch.CampaignTree;
                        var curentNode = campaignTree.SelectedNode;
                        if (curentNode != campaignTree.Root)
                            toggleCanQueue(true);
                        else if (!campaignTree.Root.Children.Contains(campaignTree.SelectedNode))
                            toggleCanQueue(true);
                        else
                            toggleCanQueue(false);

                        if (campaignTree.SelectedNode == campaignTree.Root)
                            buttonDeleteOutput.Enabled = false;
                        else if ((campaignTree.SelectedNode.Tag as CampaignNode).NodeType == CampaignNodeType.CampaignRoot)
                        {
                            var nodesFromContentView = new List<PrintNode>();
                            foreach (var item in this.templatesImageListView.SelectedItems)
                            {
                                if (item.Tag != null && item.Tag is PrintNode)
                                    nodesFromContentView.Add((item.Tag as PrintNode));
                            }
                            if (nodesFromContentView.Any(n => !n.IsEditable))
                                buttonDeleteOutput.Enabled = false;
                            else
                                buttonDeleteOutput.Enabled = true;
                        }
                    }
                    else
                    {
                        if (!(templateSearch.TemplateTree.Model as ModelBaseShoppa).NodeIsInEditableTree(templateSearch.TemplateTree.SelectedNode))
                            buttonDeleteTemplate.Enabled = false;
                        else
                            buttonDeleteTemplate.Enabled = true;
                    }
                }
            }
            else if (tabControlContent.SelectedTab == tabPagePictures)
            {
                if (!pictureSearch.PictureTree.Focused)
                {
                    if (!(dockingPanel.ActiveContent is FormTemplateBase) && templatesImageListView.SelectedItems.Count > 0)
                    {
                        // Make sure we are currently in view of the templatesImageListView
                        EnsureTreeVisibility(TreeIDEnumaration.Template); // switch to template
                        templatesImageListView.Focus();
                    }
                    else if (templatesImageListView.SelectedItems.Count == 0)
                    {
                        if (pictureSearch.PicturesImageListView.SelectedItems.Count == 0)
                        {
                            pictureSearch.PictureTree.Select();
                            EnsureGuiVisibilityBasedOnNode(GetTreeFromTreeId(TreeIDEnumaration.Picture), pictureSearch.PictureTree.SelectedNode);
                        }
                        else
                        {
                            pictureSearch.PicturesImageListView.Select();
                            if (!(pictureSearch.PictureTree.Model as ModelBaseShoppa).NodeIsInEditableTree(pictureSearch.PictureTree.SelectedNode))
                            {
                                buttonDeletePicture.Enabled = false;
                                UpdateModifyPictureButton(enabled: false);
                            }
                            else
                            {
                                buttonDeletePicture.Enabled = true;
                                UpdateModifyPictureButton(enabled: true);
                            }
                        }
                    }
                }
                StopVideoPlayback();
            }
            else if (tabControlContent.SelectedTab == tabPageOutputs)
            {
                if (this.templatesImageListView.Focused && this.templatesImageListView.SelectedItems.Count == 1 && this.templatesImageListView.SelectedItems[0].Text == "..")
                    buttonDeleteOutput.Enabled = false;
                else if (this.templatesImageListView.Focused && this.templatesImageListView.SelectedItems.Count > 0)
                {
                    if (printTree.SelectedNode != printTree.Root)
                        toggleCanQueue(true);
                    else if (!printTree.Root.Children.Contains(printTree.SelectedNode))
                        toggleCanQueue(true);
                    else
                        toggleCanQueue(false);

                    if (printTree.SelectedNode == printTree.Root)
                        buttonDeleteOutput.Enabled = false;
                    // TODO : Move or remove
                    //else if ((printTree.SelectedNode.Tag as PrintNode).NodeType == PrintNodeType.CampaignRoot)
                    //{
                    //    var nodesFromContentView = new List<PrintNode>();
                    //    foreach (var item in this.templatesImageListView.SelectedItems)
                    //    {
                    //        if (item.Tag != null && item.Tag is PrintNode)
                    //            nodesFromContentView.Add((item.Tag as PrintNode));
                    //    }
                    //    if (nodesFromContentView.Any(n => !n.IsEditable))
                    //        buttonDeleteOutput.Enabled = false;
                    //    else
                    //        buttonDeleteOutput.Enabled = true;
                    //}
                }
                else if (this.templatesImageListView.Focused && this.templatesImageListView.SelectedItems.Count == 0)
                    printTree.Select();
            }
        }

        private void UpdateModifyPictureButton(bool enabled)
        {
            if (enabled)
                enabled = !HasVideosInSelectedList();

            buttonModifyPicture.Enabled = enabled;
            var hasAdjustments = HasImageAdjustments();
            buttonModifyPicture.BackgroundImage = GetModifyPictureButtonIcon(hasAdjustments);
            toolTip.SetToolTip(buttonModifyPicture, GetModifyPictureButtonTooltip(hasAdjustments));
        }

        private bool HasVideosInSelectedList()
        {
            var videoSelected = pictureSearch.PicturesImageListView
                .SelectedItems
                .Select(i => (Guid)i.VirtualItemKey)
                .Any(PictureController.IsVideoElement);
            return videoSelected;
        }

        private bool HasImageAdjustments()
        {
            bool hasImageAdjustments = pictureSearch.PicturesImageListView
                .SelectedItems
                .Select(i => (Guid)i.VirtualItemKey)
                .Any(PictureController.HasImageAdjustments);
            return hasImageAdjustments;
        }
        private Bitmap GetModifyPictureButtonIcon(bool hasAdjustments)
        {
            return hasAdjustments && buttonModifyPicture.Enabled
                ? Properties.Resources.S3_Designbig_Imgedit_Open_Modify
                : Properties.Resources.S3_Designbig_Imgedit_Open_New;
        }

        private string GetModifyPictureButtonTooltip(bool hasAdjustments)
        {
            var mls = MLS.Instance;
            return hasAdjustments && buttonModifyPicture.Enabled
               ? mls.TranslateText("MF_ImageEdit_Modify")
               : mls.TranslateText("MF_ImageEdit_New");
        }

        void contentView_SelectionChangedFinished(object sender, EventArgs e)
        {
            // Make sure we close the search if a picture search was active
            if (pictureSearch.PictureSearchResult.IsSearching)
            {
                if (pictureSearch.PicturesImageListView.SelectedItems.Count > 0)
                {
                    Guid selectedImageGuid = (Guid)pictureSearch.PicturesImageListView.SelectedItems[0].VirtualItemKey;
                    Guid parentGuid = Guid.Empty;
                    List<GuidParentGuid> parentFolderGuids;
                    using (var picDal = Embedded.Factory.Picture)
                        parentFolderGuids = picDal.GetFolderGuids(new List<Guid>() { selectedImageGuid });

                    if (parentFolderGuids.Count > 0)
                    {
                        var itemGpg = parentFolderGuids.Where(gpg => gpg.ItemGuid == selectedImageGuid).FirstOrDefault();
                        parentGuid = itemGpg.ParentGuid;
                        parentFolderGuids.Clear();
                        findNodeParents(parentGuid, parentFolderGuids);
                        parentFolderGuids.Add(itemGpg);
                    }
                    var tree = GetTreeFromTreeId(TreeIDEnumaration.Picture);
                    TreeNodeAdv parentNode = null;
                    foreach (var node in tree.Root.Children)
                    {
                        if ((node.Tag as ShoppaTreeNode).ContentGuids.Contains(parentGuid))
                            parentNode = node;
                        else
                        {
                            tree.SuspendLayout();
                            getTreeNode(node, parentFolderGuids, parentGuid, out parentNode);
                            tree.ResumeLayout();
                        }

                        if (parentNode != null)
                            break;
                    }

                    if (parentNode != null)
                        pictureSearch.PictureSearchResult.EndPictureSearch(selectedImageGuid, (parentNode.Tag as ShoppaTreeNode).ContentGuids);
                    else
                        pictureSearch.PictureSearchResult.EndPictureSearch(Guid.Empty, new List<Guid>());
                }
                else
                    pictureSearch.PictureSearchResult.EndPictureSearch(Guid.Empty, new List<Guid>());
            }
        }

        private void Messenger_SetProductLinkSettings(TemplateProductLinkSettings settings)
        {
            if (dockingPanel.ActiveContent is FormTemplateDesign form)
                form.SetProductLinkSettings(settings);
        }

        void SetVideoUrlAnimationForVideoFile(object sender, SetAnimationEventArgs animationEventArgs)
            => dsDocker.SelectAnimations(animationEventArgs.dSObjectAnimation.Yield().ToList());

        private void Messenger_SetAnimation(object sender, SetAnimationEventArgs animationEventArgs)
        {
            if (dockingPanel.ActiveContent is FormTemplateDesign formTemplateDesign)
            {
                if (!sender.IsSameOrDescendantOf(formTemplateDesign) && sender != dsDocker)
                    return;

                formTemplateDesign.SetAnimation(animationEventArgs.dSObjectAnimation);
                var slots = formTemplateDesign.GetUsedFlowTimeslots();
                dsDocker.SetTimeslotUsage(slots);
            }
        }

        void dsDocker_SetSlideAnimation(object sender, SetSlideAnimationEventArgs e)
        {
            FormTemplateDesign form = (FormTemplateDesign)dockingPanel.ActiveContent;
            form.SetSlideAnimation(e);
        }

        void dsDocker_NeedSynchronize(object sender, EventArgs e)
        {
            _syncmanager.BeginSynchronize();
        }


        /// <summary>
        /// Checks if there is updates available for Shoppa and installs update if found.
        /// Exist in AppRunner and MainForm
        /// </summary>
        /// <returns>returns true if update is found and program should exit.</returns>
        private bool PerformAutoUpdateIfNeeded(int customerId)
        {
            bool skipUpdate = Properties.Settings.Default.SkipAutoUpdate;
#if DEBUG
            skipUpdate = true;
#endif
            if (!skipUpdate)
                return CustomerController.UpdateProgramIfNeeded2(customerId);

            return false;
        }

        void idleSpy_LongIdleTimeDetected(object sender, EventArgs e)
        {
            if (InvokeRequired)
                Invoke(new MethodInvoker(longIdleCleanUp));
            else
                longIdleCleanUp();
        }

        void longIdleCleanUp()
        {
            var queueController = new QueueController(AppData.Instance);
            var storeInfoController = new StoreInfoController(AppData.Instance);

            var gotUpdate = PerformAutoUpdateIfNeeded(AppData.Instance.CurrentUser.CustomerId);
            if (gotUpdate)
                this.Close(); // Exit the program

            printshopTeaserCounter = 0;
            // Check for new remoting proxies to find a closer one
            //TODO: Is this correctly implemented? Does this not FORCE Remoting as datalayer and circumvent the Server factory builder?
            Shoppa.DataLayer.Server.Remoting.Factory f = new DataLayer.Server.Remoting.Factory(AppData.Instance);
            f.RetryConections();

            // cleaning print archive
            queueController.CleanPrinterArchive(30, 50, 3, 5);

            // Printshop specifics
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
            {
                queueController.PurgeOldPrintshopContent(DateTime.Now.AddDays(-14));
            }

            // update store information
            storeInfoController.TryUpdateAvailabilities();
            List<EncapsulatedStoreInfo> storeInfos;
            using (var siDal = Embedded.Factory.StoreInfo)
                storeInfos = siDal.GetPackages(true);

            Messenger.Send(Messenger.Events.StoreInfosChanged, storeInfos);

            // Re-check printers
            new PrinterSettingsController(AppData.Instance).FilterPrinters();

            Trace.WriteLineIf(traceSwitch.TraceInfo, "DatabaseSync.TrySendDatabase Started", "I");
            Embedded.Factory.DatabaseSync(
                AppData.Instance.CurrentUser.UserName,
                AppData.Instance.CurrentUser.CustomerId
            ).TrySendDatabase();
            Trace.WriteLineIf(traceSwitch.TraceInfo, "DatabaseSync.TrySendDatabase Done", "I");
        }

        void idleSpy_IdleTimeDetected(object sender, EventArgs e)
        {
            if (InvokeRequired)
                Invoke(new MethodInvoker(ShowStartPage));
            else
                ShowStartPage();
        }

        /// <summary>
        /// Update title on main windows. Also check config-file if using test-system and add that too.
        /// </summary>
        void updateTitleText()
        {
            EncapsulatedCustomer customerCapsule = CustomerController.GetLocalCustomer(AppData.Instance.CurrentUser.CustomerId);
            var baseText = string.Format(MLS.Instance.TranslateText("MF_Text"), AppData.Instance.DisplayName, customerCapsule.Name);
            var connectedSystem = CustomerController.GetConfigedSystemIdentifier();
            if (string.IsNullOrEmpty(connectedSystem))
                this.Text = baseText;
            else
                this.Text = string.Format("{0}  -> ({1})", baseText, connectedSystem);
        }

        #region Search

        void searchResult_BeginProductDrag(object sender, EventArgs e)
        {
            if (dockingPanel.ActiveContent is FormTemplateBase)
            {
                FormTemplateBase form = (FormTemplateBase)dockingPanel.ActiveContent;
                form.BeginProductDrag();
            }
        }

        void searchResult_EndProductDrag(object sender, EventArgs e)
        {
            if (dockingPanel.ActiveContent is FormTemplateBase)
            {
                FormTemplateBase form = (FormTemplateBase)dockingPanel.ActiveContent;
                form.EndProductDrag();
                if (infoDocker.Visible)
                    infoDocker.UpdateDockerInfoFromProduct(form.GetTemplate().Products);
                OutputBarMode = OutputBarQueueModes.Template;
            }
        }

        void searchResult_QueryProductImage(object sender, QueryProductImageEventArgs e)
        {
            System.Threading.ThreadPool.QueueUserWorkItem(new System.Threading.WaitCallback(asyncFillProductPictures), new object[] { e.Picture, e.Quality });
        }


        void searchResult_BeginProductSelection(object sender, EventArgs e)
        {
            CheckOutputBarMode();

            // We're killing AddToQueue for the printTree as long as we are in product selection mode and the selected node is not a sign node
            if (currentTree == printTree && printTree.SelectedNodes.Count > 0)
            {
                var node = printTree.SelectedNode;
                if ((node.Tag != null && node.Tag is PrintNode) && ((node.Tag as PrintNode).NodeType != PrintNodeType.Sign))
                    toggleCanQueue(false);
            }
        }

        void searchResult_EndProductSelection(object sender, EventArgs e)
        {
            CheckOutputBarMode();

            // Re-enable AddToQueue for the printTree if queueing should indeed be enabled
            if (currentTree == printTree && printTree.SelectedNodes.Count > 0)
            {
                var node = printTree.SelectedNode;
                if ((!printTree.Root.Children.Contains(node) && node != printTree.Root && node.Children.Count > 0) || node.IsLeaf)
                    toggleCanQueue(true);
            }
        }

        private void PrintCampaign(Guid campaignGuid)
        {
            if (this.InvokeRequired)
            {
                this.invoke(new Action(() => PrintCampaign(campaignGuid)));
            }
            else
            {
                Guid recordingGuid = StoreInfoController.GetRecordingGuidFromStoreInfo(campaignGuid);
                using (var printDialog = new PrintCampaignDialog(recordingGuid))
                {
                    if (printDialog.TotalNumberOfCopies == 0)
                        return;
                    var diagResults = printDialog.ShowDialog();
                    if (diagResults == DialogResult.OK)
                    {
                        // Select PrintTree and find the root of this campaign and select that node
                        currentTree = printTree;
                        // Need to get the RecordingId from the package Guid so that we can select it in the tree
                        int? recordingId = StoreInfoController.GetCampaignRecordingId(campaignGuid);
                        var recordingCapsule = CampaignRecordingController.GetCampaignRecording(recordingGuid, false);

                        // Print it, keyboard cat!
                        QueueController.HidePrintQueue();
                        var options = new CampaignPrintOptions(printDialog.UseCampaignPrices, printDialog.Campaigns.ConvertAll(c => c.Guid), printDialog.PrintThemeSigns, printDialog.ExcludedCampaigns.ConvertAll(c => c.Guid));

                        if (!options.UseCampaignPrices)
                        {
                            var storeInfosInPrintCampaign = StoreInfoController.GetPackagesForRecording(recordingGuid);
                            var lastStopDate = storeInfosInPrintCampaign.Max(sInfo => sInfo.StopDate); // get the max StopDate from all the storInfos.
                            options.StartDate = lastStopDate;
                            options.CampaignGuids = null;
                        }

                        AddToQueue(QueuedOutputStateEnumaration.PrinterQueue, 1, null, options, true);
                        printQueue(QueuedOutputStateEnumaration.PrinterQueue, true);
                        QueueController.UnHidePrintQueue();
                    }
                }
            }
        }

        private void CheckOutputBarMode()
        {
            OutputBarMode = GetProductSearchResultControl().SelectionCount > 0
                ? OutputBarQueueModes.Selection
                : OutputBarQueueModes.Template;
        }

        void GetTemplateInformation(object sender, QueryTemplateInformationEventArgs e)
        {
            FormTemplateBase form = GetActiveFormTemplateBase();
            if (form == null)
                return;
            var template = form.GetTemplateCapsule();
            if (template == null)
                return;
            var opformat = form.GetTemplate().GetCurrentOutputFormat();
            e.TemplateGuid = template.Guid;
            e.OutputFormatGuid = opformat.OutputFormatGuid;
            e.LayoutGuid = opformat.LayoutGuid;
            e.OutputFormatName = opformat.OutputFormatCapsule.Name;
            e.OutputFormatUnit = opformat.OutputFormatCapsule.Unit;
            e.LayoutName = opformat.Layout.Name;

        }

        void asyncFillProductPictures(object stateObject)
        {
            object[] states = (object[])stateObject;
            ProductPicture picture = (ProductPicture)states[0];
            ImageQuality quality = (ImageQuality)states[1];
            // Fill full image one by one
            if (quality != ImageQuality.Thumbnail)
            {
                try
                {
                    ProductPicturesController.Instance.FillPictures(new List<ProductPicture>() { picture }, quality);
                }
                catch (Exception ex)
                {
                    Trace.WriteLineIf(traceSwitch.TraceWarning, "asyncFillProductPictures " + ex.ToString(), "W");
                }

                return;
            }

            // Batch fill thumbnails
            var startNewWork = true;
            lock (fillPictureWaitingListLock)
            {
                if (fillPictureWaitingList == null)
                    fillPictureWaitingList = new List<ProductPicture>();

                fillPictureWaitingList.Add(picture);
                // Check working inside lock but start new work outside
                if (isWorkingFillPictureWaitingList)
                    startNewWork = false;
            }

            if (startNewWork)
                workFillProductPictureWaitingList();
        }

        /// <summary>
        /// Batch Fill ProductPicture using a waiting list.
        /// <remarks>Thumbnails ONLY for now</remarks>
        /// </summary>
        static void workFillProductPictureWaitingList()
        {
            List<ProductPicture> fillPictureWorkList;

            while (true)
            {
                try
                {
                    lock (fillPictureWaitingListLock)
                    {
                        if (fillPictureWaitingList != null && fillPictureWaitingList.Count() > 0)
                        {
                            isWorkingFillPictureWaitingList = true;
                            fillPictureWorkList = fillPictureWaitingList;
                            // Reset waiting list so it can be filled in async while working the work list
                            fillPictureWaitingList = null;
                        }
                        else
                        {
                            isWorkingFillPictureWaitingList = false;
                            // Done. Exit while loop
                            break;
                        }
                    }

                    ProductPicturesController.Instance.FillPictures(fillPictureWorkList, ImageQuality.Thumbnail);
                }
                catch (Exception ex)
                {
                    Trace.WriteLineIf(traceSwitch.TraceWarning, "workFillProductPictureWaitingList " + ex.ToString(), "W");
                }
            }
        }

        #endregion

        #region MainForm
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var templatesOpenInDesign = new List<FormTemplateDesign>();
            foreach (var content in dockingPanel.DockedContent)
                if (content is FormTemplateDesign)
                    templatesOpenInDesign.Add(content as FormTemplateDesign);

            _shouldAbortMainFormClosing = false;

            foreach (var template in templatesOpenInDesign)
            {
                template.Close();

                if (_shouldAbortMainFormClosing)
                {
                    e.Cancel = true;
                    _shouldAbortMainFormClosing = false;
                    return;
                }
            }

            _formIsClosing = true;
            StopPlayerProcess();
            templatesImageListView.Dispose(); // stops backgroundWorkers in ImageListView from throwing exceptions.
            //Obs! If we ever cancel the closing event, make sure the logoutRequestedOnClose flag is also cleared

            try
            {
                if (!Server.Factory.OfflineMode && AppData.Instance.CurrentUser != null && AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.FlowPlanner))
                {
                    using (var flowPlannerContainer = new FlowPlannerContainer(DSController.GetFlowPlannerUrl()))
                    {
                        flowPlannerContainer.Logout();
                    }
                }
            }
            catch (Exception ex) { Trace.WriteLineIf(traceSwitch.TraceInfo, "FlowPlannerLogout " + ex.ToString(), "I"); }

            try
            {
                FlowDirectorController.AbortFlowDirectorWorkThread();
            }
            catch (Exception ex) { Trace.WriteLineIf(traceSwitch.TraceWarning, "AbortFlowDirectorWorkThread " + ex.ToString(), "W"); }

            try
            {
                Shoppa.DataLayer.FlowDirector.FlowDirector.Factory.StopDiscovery();
            }
            catch (Exception ex) { Trace.WriteLineIf(traceSwitch.TraceWarning, "StopDiscovery " + ex.ToString(), "W"); }

        }

        /// <summary>
        /// Aborts the main form from closing if a closing event is currently in progress.
        /// This call will be ignored if the form is not currently closing.
        /// </summary>
        private void AbortMainFormClosing()
        {
            _shouldAbortMainFormClosing = true;
        }

        void MainForm_FormClosed(object sender, System.Windows.Forms.FormClosedEventArgs e)
        {
            _syncmanager.Dispose();
            MLS.Instance.ExitMLS();
        }
        #endregion

        #region Tags

        void tags_SetTag(SetTagEventArgs e)
        {
            FormTemplateDesign form = (FormTemplateDesign)dockingPanel.ActiveContent;
            form.BeginSetTag(e);
        }

        void designChildWindow_UnStackComplete(object sender, EventArgs e)
        {
            FormTemplateDesign form = (FormTemplateDesign)sender;
            stack.ClearStack();
            stack.AddElementsToStack(form.GetStacked());
        }

        void stack_UnStack(object sender, UnStackEventArgs e)
        {
            FormTemplateDesign form = (FormTemplateDesign)dockingPanel.ActiveContent;
            if (e.Delete)
                form.DeleteFromStack(e.ElementsToUnStack);
            else if (e.BringBack)
                form.UnStackNow(e.ElementsToUnStack);
            else
                form.BeginUnStack(e.ElementsToUnStack);
        }

        void stack_ElementsDropped(object sender, EventArgs e)
        {
            FormTemplateDesign form = (FormTemplateDesign)dockingPanel.ActiveContent;
            stack.AddElementsToStack(form.StackSelected());
        }

        #endregion

        private void MenuStripItemsClicked(object sender, EventArgs e)
        {
            var payload = sender as MessagePayload;
            var menustrip = payload.Get<MenuItemClickInfo>();
            if (menustrip.Origin == MenuItemOrigin.File && !titleBarStrip1.MenuItemEnabled(menustrip.MenuItemName))
                return;

            switch (menustrip.MenuItemName)
            {
                case MenuItemNames.manual:
                    manualToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.exit:
                    exitToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.recoverMaterial:
                    undeleteItemsToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.bindPrinters:
                    bindPrintersToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.managePrinters:
                    managePrintersToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.selectPrinter:
                    selectPrinterToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.saveAs:
                    saveAsToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.saveAsPicture:
                    saveAsPictureToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.exportTemplate:
                    exportTemplateToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.exportTemplateLocally:
                    exportTemplateLocallyToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.importTemplateLocally:
                    importTemplateLocallyToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.showBindings:
                    toolStripMenuItemShowBindings_Click(sender, e);
                    break;
                case MenuItemNames.autoSignage:
                    ShowFormAutoSignage();
                    break;
                case MenuItemNames.queue:
                    buttonAddOutputToQueue_Click_1(sender, e);
                    break;
                case MenuItemNames.print:
                    buttonPrint_Click(sender, e);
                    break;
                case MenuItemNames.manageAddresses:
                    manageAddressesToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.manageAddressLists:
                    manageAddressListsToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.manageCustomerGroups:
                    manageCustomerGroupsToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.manageMetaValues:
                    manageMetaValuesToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.aboutShoppa:
                    aboutShoppaToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.startPage:
                    ShowStartPage(true);
                    break;
                case MenuItemNames.printshop:
                    ShowPrintshop();
                    break;
                case MenuItemNames.filterStoreGroups:
                    FilterStoreGroupsMenuItem_Click();
                    break;
                case MenuItemNames.changePassword:
                    ChangePasswordToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.logout:
                    logoutToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.sync:
                    syncToolStripMenuItem_Click(sender, e);
                    break;
                case MenuItemNames.changeLanguage:
                    menustrip.Arguments.TryGetValue("languageCode", out object languageValue);
                    changeLanguageMenuItem_Click(languageValue, e);
                    break;
                case MenuItemNames.previewPlaySettings:
                    Shoppa.PL.Windows.Forms.FormPreviewPlaySettings settings = new Forms.FormPreviewPlaySettings();
                    settings.InitFromSettings();
                    settings.ShowDialog();
                    break;
                case MenuItemNames.saveToFileSettings:
                    using (var form = new VideoFileSettingsForm())
                    {
                        form.ShowDialog();
                    }
                    break;
                case MenuItemNames.printClientStatus:
                    using (var form = new FormPrintClientStatus())
                    {
                        form.InitWith(printClient);
                        form.ShowDialog();
                    }
                    break;
                case MenuItemNames.lunaLogin:
                    using (var form = new LunaQRLogin())
                    {
                        form.ShowDialog();
                    }
                    break;
                case MenuItemNames.printClientName:
                    using (var form = new EditPrintClientName())
                    {
                        form.InitWith(printClient);
                        form.ShowDialog();
                    }
                    titleBarStrip1?.UpdatePrintClientNameText();
                    break;
            }
        }

        private void ShowStoreGroupFilterDialog()
        {
            var recordingGuid = CampaignRecordingController.GetGuidFromId(CampaignModel.EditedRecordingId);
            if (recordingGuid.HasValue)
            {
                var confirmAbort = MessageBoxes.ConfirmAbortEditCampaignRecordingEnterFilter(this);
                if (!TryAbortEditCampaign(confirmAbort == DialogResult.Yes))
                    return;
            }

            var filterStoreGroups = new FormFilterStoreGroups { StartPosition = FormStartPosition.CenterParent };
            filterStoreGroups.ShowDialog();

            if (filterStoreGroups.DialogResult == DialogResult.OK)
            {
                Messenger.Send(this, Messenger.Events.StoreGroupFilterChanged);
                Messenger.Send(this, Messenger.Events.ReloadTrees);
            }
        }

        private void ClearStoreGroupFilter()
        {
            CustomerGroupController.SetFilter(Enumerable.Empty<CustomerGroup>());
            Messenger.Send(this, Messenger.Events.StoreGroupFilterChanged);
            Messenger.Send(this, Messenger.Events.ReloadTrees);
        }

        private void RefreshTreesAndContentViews()
        {
            if (InvokeRequired)
            {
                Invoke(new MethodInvoker(RefreshTreesAndContentViews));
                return;
            }

            templateSearch.ClearCampaignTreeCollapsedExpanded();
            templateSearch.ClearTemplateTreeCollapsedExpanded();

            var priorTree = currentTree;

            SetupRootGroupsBasedOnCustomer(
                out List<TreeGroupInformation> rootGroupsTemplateToAdd,
                out List<TreeGroupInformation> rootGroupsPicturesToAdd,
                out List<TreeGroupInformation> rootGroupsCampaignRecToAdd,
                out _,
                out _
            );

            var campaignTreeHasSelectedNode = templateSearch.CampaignTree.HasSelectedNodes;
            RefreshTemplateTree(rootGroupsTemplateToAdd, !campaignTreeHasSelectedNode);

            var templateTreeHasSelectedNode = templateSearch.TemplateTree.HasSelectedNodes;
            var campaignRecordingInEditMode = CampaignRecordingController.GetCampaignIdInEditMode() ?? 0;
            RefreshCampaignRecordingTree(rootGroupsCampaignRecToAdd, !templateTreeHasSelectedNode, campaignRecordingInEditMode);
            RefreshPictureTree(rootGroupsPicturesToAdd);

            currentTree = priorTree;
            currentNode = priorTree?.SelectedNode;

            EnsureGuiVisibilityBasedOnNode(currentTree, currentNode);           

            templateSearch.SetCampaignTreeCollapsedExpanded();
            templateSearch.SetTemplateTreeCollapsedExpanded();

            templateSearch.FindVisibleCampaignTreeNodeCount();
            templateSearch.FindVisibleTemplateTreeNodeCount();
            templateSearch.AdjustTreeView();
        }

        private void RefreshTemplateTree(List<TreeGroupInformation> rootGroupsTemplateToAdd, bool selectDefaultNode)
        {
            var tree = templateSearch.TemplateTree;
            ContentView listView = templatesImageListView;

            var path = tree.GetPathToSelectedNode();
            var expandInfo = tree.ExpandInfo;
            var curVisibleRow = tree.FirstVisibleRow;

            var curScroll = listView.VScroll;
            forceUpdateAfterBrowsing = true;
            tree.ApplyModel(
                rootGroupsTemplateToAdd,
                path,
                expandInfo);

            if (selectDefaultNode && !tree.HasSelectedNodes && tree.Root.Children.FirstOrDefault() is TreeNodeAdv firstNode)
            {
                firstNode.IsSelected = true;
            }

            listView.VScroll = curScroll;
            tree.FirstVisibleRow = curVisibleRow;
        }

        private void RefreshCampaignRecordingTree(List<TreeGroupInformation> rootGroupsCampaignRecordingsToAdd, bool selectDefaultNode, int editedCampaignRecordingId)
        {
            var tree = templateSearch.CampaignTree;
            ContentView listView = templatesImageListView;

            var path = tree.GetPathToSelectedNode();
            var expandInfo = tree.ExpandInfo;
            var curVisibleRow = tree.FirstVisibleRow;

            var curScroll = listView.VScroll;
            forceUpdateAfterBrowsing = true;
            tree.ApplyModel(
                rootGroupsCampaignRecordingsToAdd,
                path,
                expandInfo,
                editedCampaignRecordingId);

            if (selectDefaultNode && !tree.HasSelectedNodes && tree.Root.Children.FirstOrDefault() is TreeNodeAdv firstNode)
            {
                firstNode.IsSelected = true;
            }

            if (editedCampaignRecordingId != 0)
            {
                ReApplyCampaignEditMode(editedCampaignRecordingId, tree);
            }

            listView.VScroll = curScroll;
            tree.FirstVisibleRow = curVisibleRow;           
        }

        private void ReApplyCampaignEditMode(int editedCampaignRecordingId, CampaignRecordingTree tree)
        {
            if (tree.HasSelectedNodes && (tree.SelectedNode.Tag as CampaignNode).CampaignId == editedCampaignRecordingId)
            {
                CampaignModel.EditedNodePath = tree.GetPathToSelectedNode();
                ChangeUIForCampaignRecordingEdit(tree.SelectedNode);
            }
            else
            {
                var editedCampaignGuid = CampaignRecordingController.GetGuidFromId(editedCampaignRecordingId);
                var pathToCampaignRecording = TreeController2.GetPathToItem(TreeIDEnumaration.CampaignRecording, editedCampaignGuid.Value);
                CampaignModel.EditedNodePath = pathToCampaignRecording;
                ChangeUIForCampaignRecordingEdit(null);
            }
        }

        private void RefreshPictureTree(List<TreeGroupInformation> rootGroupsPicturesToAdd)
        {
            var tree = pictureSearch.PictureTree;
            ContentView listView = pictureSearch.PicturesImageListView;

            var path = tree.GetPathToSelectedNode();
            var expandInfo = tree.ExpandInfo;
            var curVisibleRow = tree.FirstVisibleRow;

            Guid? selectedItem = null;
            if (listView.SelectedItems.Any() && listView.SelectedItems[0] is Manina.Windows.Forms.ImageListViewItem listViewItem)
            {
                selectedItem = (Guid)listViewItem.VirtualItemKey;
            }

            var curScroll = listView.VScroll;
            forceUpdateAfterBrowsing = true;
            tree.ApplyModel(
                rootGroupsPicturesToAdd,
                path,
                expandInfo);

            tree.FirstVisibleRow = curVisibleRow;

            if (!tree.HasSelectedNodes && tree.Root.Children.FirstOrDefault() is TreeNodeAdv firstNode)
            {
                firstNode.IsSelected = true;
            }

            if (selectedItem.HasValue)
            {
                var pictureListViewItem = listView.Items.FirstOrDefault(p => (Guid)p.VirtualItemKey == selectedItem);
                if (pictureListViewItem != null)
                {
                    pictureListViewItem.Selected = true;
                    listView.Focus();

                    if (tree.Model is ModelBaseShoppa shoppaTreeModel
                        && shoppaTreeModel.NodeIsInEditableTree(tree.SelectedNode))
                    {
                        buttonDeletePicture.Enabled = true;
                        UpdateModifyPictureButton(enabled: listView.SelectedItems.Any());
                    }
                }
            }
            listView.VScroll = curScroll;
        }

        private void SystemEvents_SessionSwitch(object sender, Microsoft.Win32.SessionSwitchEventArgs e)
        {
            if (e.Reason == Microsoft.Win32.SessionSwitchReason.SessionUnlock && (_startpageFanout?.IsShowing ?? false))
            {
                Thread.Sleep(500);
                _startpageFanout.Refresh();
            }
        }

        private void messenger_OnContentActivation(MessagePayload messagePayload)
        {
            if (_startpageFanout?.IsShowing ?? false)
                _startpageFanout.Hide();
        }

        internal void ShowStartPage() => ShowStartPage(false);

        internal void ShowStartPage(bool forceShow)
        {
            if (_startpageFanout?.IsShowing ?? false || (!forceShow && AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer)))
                return;

            var location = this.PointToScreen(dockingPanel.Location);
            var availableHeight = SystemInformation.WorkingArea.Height - location.Y;
            var startBoxHeight = availableHeight < dockingPanel.Height ? availableHeight : dockingPanel.Height;

            _startpageFanout = new StartPageBox
            {
                Parent = dockingPanel,
                Width = dockingPanel.Width + 6,
                Height = startBoxHeight,
                ControlsToIgnoreCloseOnClick = titleBarStrip1.GetControlsToBeIgnoredByFanOutControlOnClick()
            };
            _startpageFanout.SetRelativeLocation(-3, 0);
            _startpageFanout.Show();
            _startpageFanout.IsShowingChanged += fanout_IsShowingChanged;
        }

        private void fanout_IsShowingChanged(object sender, EventArgs e)
        {
            if (sender is FanOutControl fanoutControl)
            {
                if (!fanoutControl.IsShowing)
                {
                    fanoutControl.IsShowingChanged -= fanout_IsShowingChanged;
                    fanoutControl.Dispose();
                }
            }
        }

        internal void ShowPrintshop()
        {
            if (_printshopFanout?.IsShowing ?? false)
                return;

            var welcomeUri = new CustomerController(AppData.Instance).GetPrintshopUri();

            var location = this.PointToScreen(dockingPanel.Location);
            var availableHeight = SystemInformation.WorkingArea.Height - location.Y;
            var startBoxHeight = availableHeight < dockingPanel.Height ? availableHeight : dockingPanel.Height;

            _printshopFanout = new WebBrowserFanout
            {
                Parent = dockingPanel,
                Width = dockingPanel.Width,
                Height = startBoxHeight,
                Location = location,
                Uri = welcomeUri
            };
            _printshopFanout.Show();
            _printshopFanout.IsShowingChanged += fanout_IsShowingChanged;
        }

        private void undeleteItemsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer))
            {
                using (var form = new RecoverPrintshopOrders(_syncmanager))
                {
                    form.ShowDialog();
                }
                _syncmanager.BeginSynchronize(0);
            }
            else
            {
                Cursor = Cursors.WaitCursor;
                _syncmanager.Syncronize();
                Cursor = Cursors.Arrow;
                using (RecoverForm form = new RecoverForm())
                {
                    form.ShowDialog();
                }
                _syncmanager.BeginSynchronize(0);
            }
        }

        #region Multilanguage

        /// <summary>
        /// The event method for handling language change buttons clicks.
        /// The method updates the MLS language to the chosen one
        /// The method starts the form language update.
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void changeLanguageMenuItem_Click(object sender, EventArgs e)
        {
            var languageCode = (string)sender;
            MLS.Instance.SetMLSLanguage(languageCode);
            LanguageController.SaveGuiLanguage(languageCode);
            Translate();
            Messenger.Send(this, Messenger.Events.LanguageChanged);
        }

        #endregion

        #region Picture Controllers Events

        private void buttonAddPicture_Click(object sender, EventArgs e)
        {
            AddMediaFiles(MediaImportType.PictureAndVideo);
        }

        private void buttonAddPictureFolder_Click(object sender, EventArgs e)
        {
            AddNewFolder(TreeIDEnumaration.Picture);
        }

        private void buttonDeletePicture_Click(object sender, EventArgs e)
        {
            var treeId = TreeIDEnumaration.Picture;
            if (lastFocusedControl is ShoppaTree2)
                DeleteContentOrFolder(treeId);
            else if (lastFocusedControl is ContentView)
            {
                if (lastFocusedControl == pictureSearch.PicturesImageListView && pictureSearch.PicturesImageListView.SelectedItems.Count > 0)
                    deletePictureInContentView(treeId);
                else if (pictureSearch.PictureTree.SelectedNodes.Count > 0)
                {
                    lastFocusedControl = pictureSearch.PictureTree;
                    DeleteContentOrFolder(treeId);
                }
            }
            else
            {
                // lastFocusedControl might be PrintTree, check if pictureTree or picturesImageListView has any selected content and ask to delete that instead
                if (pictureSearch.PictureTree.SelectedNodes.Count > 0)
                {
                    lastFocusedControl = pictureSearch.PictureTree;
                    DeleteContentOrFolder(treeId);
                }
            }

            lastFocusedControl.Focus();
        }

        private void buttonModifyPicture_Click(object sender, EventArgs e)
        {
            if (pictureSearch.PicturesImageListView is ContentView cv && cv.SelectedItems.Any())
            {
                cv.ChangeImageAdjustmentsOnSelectedPictures();
                cv.Focus();
                UpdateModifyPictureButton(enabled: true);
            }
        }

        #endregion

        #region Template Controllers Evens

        private void buttonAddTemplateFolder_Click(object sender, EventArgs e)
        {
            AddNewFolder(TreeIDEnumaration.Template);
        }

        private void buttonNewTemplate_Click(object sender, EventArgs e)
        {
            AddNewTemplate();
        }

        /// <summary>
        /// Delete Template or Template Folder
        /// </summary>
        private void buttonDelete_Click(object sender, EventArgs e)
        {
            var treeId = TreeIDEnumaration.Template;
            if (lastFocusedControl is ShoppaTree2)
                DeleteContentOrFolder(treeId);
            else if (lastFocusedControl is ContentView)
            {
                if (lastFocusedControl == this.templatesImageListView && this.templatesImageListView.SelectedItems.Count > 0)
                    deleteTemplateOrFolderInContentView(treeId);
                else if (templateSearch.TemplateTree.SelectedNodes.Count > 0)
                {
                    lastFocusedControl = templateSearch.TemplateTree;
                    DeleteContentOrFolder(treeId);
                }
            }
            else
            {
                // lastFocusedControl might be PrintTree, check if templateTree or templatesImageListView has any selected content and ask to delete that instead
                if (templateSearch.TemplateTree.SelectedNodes.Count > 0)
                {
                    lastFocusedControl = templateSearch.TemplateTree;
                    DeleteContentOrFolder(treeId);
                }
            }

            lastFocusedControl.Focus();
        }

        private void buttonDeleteCampaign_Click(object sender, EventArgs e)
        {
            DeleteFromCampaignTree();
        }

        /// <summary>
        /// Delete Printer Queue
        /// </summary>
        private void buttonDeleteOutput_Click(object sender, EventArgs e)
        {
            if (printTree.SelectedNodes.Count > 0)
            {
                //var campaignRoot = currentTree.GetCampaignQueue();
                var topNode = (currentTree.SelectedNodes[0].Tag as CampaignNode);
                DeleteFromPrintQueue();
            }
        }


        private void recoverDeletedSignsDuringEdit()
        {
            var recordingGuid = CampaignRecordingController.GetGuidFromId(CampaignModel.EditedRecordingId);
            var recording = CampaignRecordingController.GetCampaignRecording(recordingGuid.Value, true);
            var signs = CampaignRecordingSerializer.ExtractCampaignData(recording);
            // Should not even need 30 days back but better safe than sorry
            var deprecatedSignItems = QueueController.GetDeprecatedCampaignSigns(30);

            if (deprecatedSignItems.Count > 0)
            {
                List<Guid> signsToRecover = new List<Guid>();
                foreach (var sign in signs.Where(s => !s.IsDeleted))
                {
                    var correspondingDeprecatedSign = deprecatedSignItems.FirstOrDefault(s => s.Guid == sign.Guid);
                    if (correspondingDeprecatedSign != null)
                        signsToRecover.Add(sign.Guid);
                }
                if (signsToRecover.Count > 0)
                    QueueController.RecoverCampaignSigns(signsToRecover);
            }
        }

        private bool CreateCampaignRecording()
        {
            var campaignTree = templateSearch.CampaignTree;
            var selectedNode = campaignTree.SelectedNode;

            var campaignCreated = false;
            var newCampaignRecording = new EncapsulatedCampaignRecording();
            using (var publishDialog = new PublishCampaignDialog(newCampaignRecording, new List<int>()))
            {
                Guid parentGuid = (selectedNode.Tag as CampaignNode).ContentGuid;

                if (publishDialog.ShowDialog() == DialogResult.OK)
                {
                    var cgGuids = publishDialog.SelectedCustomerGroups.Select(g => g.Guid).ToList();
                    if (publishDialog.HasCustomerGroupsChanged)
                    {
                        CustomerGroupController.ReplaceMaterialCustomerGroups(
                            CapsuleType.CampaignRecording,
                            material: new[] { newCampaignRecording.Guid },
                            customerGroups: cgGuids,
                            changedLocally: false);
                    }

                    newCampaignRecording.CountryCode = AppData.Instance.CurrentUser.CountryCode;
                    newCampaignRecording.UserID = AppData.Instance.CurrentUser.Id;
                    newCampaignRecording.CustomerID = AppData.Instance.CurrentUser.CustomerId;
                    newCampaignRecording.Data = null;
                    newCampaignRecording.IsActive = CampaignRecordingController.TodayIsWithinRange(newCampaignRecording.StartDate, newCampaignRecording.StopDate);
                    newCampaignRecording.CustomerGroups = cgGuids;
                    newCampaignRecording.ParentGuid = parentGuid;
                    using (var localDal = Embedded.Factory.CampaignRecording)
                    {
                        localDal.SaveItem(newCampaignRecording, false);
                        newCampaignRecording.Id = localDal.GetCampaignId(newCampaignRecording.Guid);
                        localDal.SetEditMode(newCampaignRecording.Id, true);
                    }

                    new CampaignRecordingController(AppData.Instance).UpdateAvailabilities();

                    List<EncapsulatedStoreInfo> storeInfos;
                    using (var storeInfoDal = Embedded.Factory.StoreInfo)
                        storeInfos = storeInfoDal.GetPackages(true);

                    Messenger.Send(Messenger.Events.StoreInfosChanged, storeInfos);

                    var model = campaignTree.Model as CampaignRecordingTreeModel;
                    model.EditedRecordingId = newCampaignRecording.Id;
                    model.AddNewCampaignRecording(selectedNode, newCampaignRecording, false);                    

                    var newCampaignNode = selectedNode.Children.Where(c => (c.Tag as CampaignNode).CampaignId.Equals(newCampaignRecording.Id)).First();
                    campaignTree.SelectedNode = newCampaignNode;
                    newCampaignNode.Expand();
                    CampaignModel.EditedNodePath = campaignTree.GetPathToSelectedNode();
                    UpdateQueueCountBoxes();

                    templateSearch.SetScrollToSelectedItem();
                    campaignCreated = true;
                }
            }
            if (campaignCreated)
            {
                templateSearch.FindVisibleCampaignTreeNodeCount();
                templateSearch.AdjustTreeView();
            }
            return campaignCreated;
        }
       
        private void PublishCampaignRecording(bool editing, int? recordingId)
        {
            if (editing && !recordingId.HasValue)
                throw new ArgumentException("Do not call this method with editing set to true without providing a campaign recording id!");

            var usedProductIds = new List<int>();
            var signCapsules = QueueController.GetOutputQueue(QueuedOutputStateEnumaration.CampaignQueue, true);
            foreach (EncapsulatedQueuedSign sign in signCapsules)
            {
                var template = TemplateController.GetTemplate(sign.Data);
                template.SetOutputFormat(sign.OutputFormatQueued, sign.LayoutGuid);
                foreach (var product in template.Products.GetLegacy())
                {
                    if (product.ID.Type != ProductID.IDType.Virtual
                        && product.IdAsNullableInt.HasValue 
                        && !usedProductIds.Contains(product.IdAsNullableInt.Value))
                            usedProductIds.Add(product.IdAsNullableInt.Value);
                }
                sign.Data = TemplateController.SerializeTemplate(template);

                var allProductIds = template.Products.GetProductInstances().ConvertAll(pr => pr.ID.ID);
                sign.UsedProductIds = allProductIds;

                // Reset for new use in CampaignRecording
                if (!editing)
                    sign.Version = 0;
            }

            EncapsulatedCampaignRecording ecr = null;
            if (editing)
            {
                var guid = CampaignRecordingController.GetGuidFromId(recordingId.Value);
                ecr = CampaignRecordingController.GetCampaignRecording(guid.Value, true);
                //CampaignRecordingController.RemoveEditMode(recordingId.Value);
            }
            else
                ecr = new EncapsulatedCampaignRecording();

            using (var publishDialog = new PublishCampaignDialog(ecr, usedProductIds))
            {
                if (publishDialog.ShowDialog() == DialogResult.OK)
                {
                    if (editing)
                        CampaignRecordingController.RemoveEditMode(recordingId.Value);

                    var customerGroups = publishDialog.SelectedCustomerGroups.Select(g => g.Guid).ToList();

                    if (publishDialog.HasCustomerGroupsChanged)
                    {
                        CustomerGroupController.ReplaceMaterialCustomerGroups(
                            CapsuleType.CampaignRecording,
                            material: new[] { ecr.Guid },
                            customerGroups: customerGroups,
                            changedLocally: true);
                    }

                    ecr.CountryCode = AppData.Instance.CurrentUser.CountryCode;
                    ecr.UserID = AppData.Instance.CurrentUser.Id;
                    ecr.CustomerID = AppData.Instance.CurrentUser.CustomerId;
                    signCapsules.Sort((x, y) => y.TimeStamp.CompareTo(x.TimeStamp));
                    ecr.Data = CampaignRecordingController.PackCampaignData(signCapsules, EncapsulatedCampaignRecording.VERSION);
                    ecr.IsActive = CampaignRecordingController.TodayIsWithinRange(ecr.StartDate, ecr.StopDate);
                    ecr.CustomerGroups = customerGroups;

                    using (var localDal = Embedded.Factory.CampaignRecording)
                    {
                        localDal.SaveItem(ecr);
                        if (!editing)
                            ecr.Id = localDal.GetCampaignId(ecr.Guid);
                    }

                    new CampaignRecordingController(AppData.Instance).UpdateAvailabilities();

                    List<EncapsulatedStoreInfo> storeInfos;
                    using (var storeInfoDal = Embedded.Factory.StoreInfo)
                        storeInfos = storeInfoDal.GetPackages(true);

                    Messenger.Send(Messenger.Events.StoreInfosChanged, storeInfos);

                    // Change the state of all the signs back to Campaign
                    var signs = CampaignRecordingSerializer.ExtractCampaignData(ecr);
                    CampaignRecordingController.SetOriginalContentHash(ecr.Guid, signs);
                    QueueController.UpdateSignState(signs.ConvertAll(s => s.Guid), QueuedOutputStateEnumaration.Campaign, true);
                    // Set the recording ID on any sign without it
                    foreach (var sign in signs.Where(s => !s.CampaignID.HasValue || s.CampaignID.Value != ecr.Id))
                        QueueController.SetCampaignRecordingId(sign.Guid, ecr.Id);

                    // Restore / create tree node under Campaign
                    templateSearch.CampaignTree.SuspendLayout();
                    var editedCampaignNode = StopEditCampaignRecording(ecr);
                    templateSearch.CampaignTree.ResumeLayout();
                    if (editedCampaignNode != null)
                    {
                        UpdateCampaignNodeProperties(editedCampaignNode, ecr);
                        SelectAndExpandCampaignInTree(editedCampaignNode);
                    }

                    Messenger.Send(Messenger.Events.ActivateCampaignbuttons, CampaignModel.InEditMode);
                    EnsureGuiVisibilityBasedOnNode(templateSearch.CampaignTree, templateSearch.CampaignTree.SelectedNode);

                    UpdateQueueCountBoxes();
                    _syncmanager.BeginSynchronize();
                }
            }
        }

        private void UpdateCampaignNodeProperties(TreeNodeAdv editedCampaignNode, EncapsulatedCampaignRecording ecr)
        {
            var isActive = ecr.IsActive;
            var isPublic = ecr.IsPublic;
            var hasCustomerGroups = ecr.CustomerGroups.Any();

            var campaignNode = editedCampaignNode.Tag as CampaignNode;
            campaignNode.Text = ecr.Name;
            campaignNode.IsActive = isActive;
            campaignNode.IsPublic = isPublic;
            campaignNode.HasCustomerGroups = hasCustomerGroups;

            foreach (var outputFormatTreeNode in editedCampaignNode.Children)
            {
                var outputFormatNode = outputFormatTreeNode.Tag as CampaignNode;
                outputFormatNode.IsActive = isActive;
                outputFormatNode.IsPublic = isPublic;
                outputFormatNode.HasCustomerGroups = hasCustomerGroups;
                foreach (var signTreeNode in outputFormatTreeNode.Children)
                {
                    var signNode = signTreeNode.Tag as CampaignNode;
                    signNode.IsActive = isActive;
                    signNode.IsPublic = isPublic;
                    signNode.HasCustomerGroups = hasCustomerGroups;
                }
            }
        }

        private void StartEditCampaignRecording(CampaignRecordingTreeModel campaignModel, int campaignRecordingId)
        {
            CampaignRecordingController.SetEditMode(campaignRecordingId);
            QueueController.UpdateCampaignRecordingSignState(campaignRecordingId, QueuedOutputStateEnumaration.Campaign, QueuedOutputStateEnumaration.CampaignQueue);
            campaignModel.EditedRecordingId = campaignRecordingId;
            campaignModel.EditedNodePath = templateSearch.CampaignTree.GetPathToSelectedNode(); 
            UpdateQueueCountBoxes();
            outputBar.AddDeleteCampaignRecordingPrimaryEnabled = false;
        }

        private void ChangeUIForCampaignRecordingEdit(TreeNodeAdv treeNode)
        {
            buttonEditCampaign.BackgroundImage = Properties.Resources.S3_Campaign_Abort;
            toolTip.SetToolTip(this.buttonEditCampaign, MLS.Instance.TranslateText("CRT_AbortEdit"));
            outputBar.EnableAddDeleteCampaignRecording = true;
            if (treeNode != null && treeNode.IsExpanded) treeNode.Collapse();
            treeNode?.Expand();
            Messenger.Send(Messenger.Events.ActivateCampaignbuttons, true);
        }

        #endregion

        #region PRINTER QUEUE

        private void bindPrintersToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult res = System.Windows.Forms.DialogResult.Retry;
            while (res == System.Windows.Forms.DialogResult.Retry)
            {
                using (ChoseFinalDestination2 cfd = new ChoseFinalDestination2())
                {
                    res = cfd.ShowDialog();
                }
            }

            _syncmanager.BeginSynchronize();
        }

        private void UpdateFontPicker()
        {
            foreach (var tab in dockingPanel.DockedContent)
                if (tab is FormTemplateDesign formTemplateDesign)
                    formTemplateDesign.ReloadFonts();
        }

        private void UpdateQueueCountBoxes()
        {
            if (InvokeRequired)
            {
                invoke(new Action(() => UpdateQueueCountBoxes()));
                return;
            }

            UpdateQueueEslMode();

            UpdateQueueCountBox_Print();
            UpdateQueueCountBox_PrintOrder();
            UpdateQueueCountBox_DS();
            UpdateQueueCountBox_Campaign();
        }

        private void UpdateQueueEslMode()
        {
            this.outputBar.PrintQueueContainsEsl = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ESL)
                ? QueueController.PrinterQueueContainsEsl()
                : false;
        }

        private void UpdateQueueCountBox_Print()
        {
            ushort totCopies = QueueController.GetTotalCopies(QueuedOutputStateEnumaration.PrinterQueue);
            this.outputBar.PreSendCopiesSumPrint = totCopies;
            titleBarStrip1.ChangeActiveState(MenuItemNames.print, totCopies > 0 || titleBarStrip1.MenuItemEnabled(MenuItemNames.queue));
        }

        private void UpdateQueueCountBox_PrintOrder()
        {
            var totCopies = QueueController.GetTotalCopies(QueuedOutputStateEnumaration.PrintshopQueue);
            this.outputBar.PreSendCopiesSumOrder = totCopies;
        }

        private void UpdateQueueCountBox_DS()
        {
            this.outputBar.PreSendCopiesSumDS = dsContentBar1.GetSignsCount();
            //dsSplitter.Visible = panelDSQueue.Visible = dsContentBar1.GetSignsCount() > 0;
        }

        private void UpdateQueueCountBox_Campaign()
        {
            var totCopies = QueueController.GetTotalCopies(QueuedOutputStateEnumaration.CampaignQueue);
            this.outputBar.PreSendCopiesSumCampaign = totCopies;
        }

        private void outputBar_QueuedButtonClicked(object sender, OutputPanelEventArgs e)
        {
            var form = GetActiveFormTemplateBase();
            if (form != null)
            {
                // do NOT allow update of GUI if layout is print to file and currentUser does NOT have PDFPrinter credentials

                if ((form.GetTemplate() != null) && form.GetTemplate().GetCurrentLayout().PrintTofile && !AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PDFPrinter))
                    return;
                //Print using the selected template
                switch (e.Type)
                {
                    case OutputPanelEventTypes.AddToPrint:

                        if (OutputBarMode == OutputBarQueueModes.Selection)
                        {
                            var productSearchResult = GetProductSearchResultControl();
                            if (productSearchResult.SelectionCount > 0 && form.Template != null)
                            {
                                this.outputBar.PreSendPendingPrint++;
                                outputBar.AddValueToPreSendCopiesSumPrint(productSearchResult.SelectionCount);
                            }
                            return;
                        }
                        else if (outputBarMode == OutputBarQueueModes.Template)
                        {
                            var treeIDEnumaration = FindEffectiveTreeId(currentTree, form);
                            var count = GetSelectedTemplatesFromTree(treeIDEnumaration).Count();
                            if (count > 0)
                            {
                                this.outputBar.PreSendPendingPrint++;
                                outputBar.AddValueToPreSendCopiesSumPrint(count);
                            }
                        }
                        else
                        {
                            this.outputBar.PreSendPendingPrint++;
                            outputBar.AddValueToPreSendCopiesSumPrint(1);
                        }
                        break;

                    case OutputPanelEventTypes.AddToOrder:

                        if (OutputBarMode == OutputBarQueueModes.Selection)
                        {
                            var count = GetProductSearchResultControl().SelectionCount;
                            if (count > 0)
                            {
                                this.outputBar.PreSendPendingOrder++;
                                outputBar.AddValueToPreSendCopiesSumOrder(count);
                            }
                            return;
                        }
                        else if (outputBarMode == OutputBarQueueModes.Template)
                        {
                            var treeIDEnumaration = FindEffectiveTreeId(currentTree, form);
                            var count = GetSelectedTemplatesFromTree(treeIDEnumaration).Count();
                            if (count > 0)
                            {
                                this.outputBar.PreSendPendingOrder++;
                                outputBar.AddValueToPreSendCopiesSumOrder(count);
                            }
                        }
                        else
                        {
                            this.outputBar.PreSendPendingOrder++;
                            outputBar.AddValueToPreSendCopiesSumOrder(1);
                        }
                        break;
                    case OutputPanelEventTypes.AddToCampaignQueue:

                        if (OutputBarMode == OutputBarQueueModes.Selection)
                        {
                            var count = GetProductSearchResultControl().SelectionCount;
                            if (count > 0)
                            {
                                //outputBar.PreSendCopiesSumCampaign += (ushort)productSearchResult.SelectionCount;
                                this.outputBar.PreSendPendingCampaign++;
                                outputBar.AddValueToPreSendCopiesSumCampaign(count);
                            }
                            return;
                        }
                        else if (outputBarMode == OutputBarQueueModes.Template)
                        {
                            var treeIDEnumaration = FindEffectiveTreeId(currentTree, form);
                            var count = GetSelectedTemplatesFromTree(treeIDEnumaration).Count;
                            if (count > 0)
                            {
                                //outputBar.PreSendCopiesSumCampaign += (ushort)count;
                                this.outputBar.PreSendPendingCampaign++;
                                outputBar.AddValueToPreSendCopiesSumCampaign(count);
                            }
                        }
                        else
                        {
                            //outputBar.PreSendCopiesSumCampaign += 1;
                            outputBar.PreSendPendingCampaign++;
                            outputBar.AddValueToPreSendCopiesSumCampaign(1);
                        }
                        break;
                }
            }
            else if (OutputBarMode == OutputBarQueueModes.Selection)
            {
                //No preselected template but selected products, so print using the defined Display Groups
                // only add products that contains DisplayGroup Text
                var selection = GetProductSearchResultControl()
                    .SelectedProducts
                    .Where(p => p.VisibleTexts.Any(d => d.Name == "DisplayGroup"));

                if (selection.Count() > 0)
                {
                    var allDisplayGroups = DisplayGroupController.GetLegacyDisplayGroups();
                    int count = 0;
                    foreach (var prod in selection)
                    {
                        var displayGroupGuid = new Guid(prod.VisibleTexts.Where(d => d.Name == "DisplayGroup").First().Value);
                        int copies;
                        if (!Int32.TryParse(prod.VisibleTexts.Where(d => d.Name == "DisplayGroupQty").First().Value, out copies))
                            copies = 1;
                        var matchedGroup = allDisplayGroups.Where(edg => edg.TriggerType == EncapsulatedDisplayGroup.TriggerTypeEnum.LegacyDisplayGroup && edg.Guid == displayGroupGuid).First();
                        if (matchedGroup != null)
                        {
                            foreach (IDisplayGroupRow row in matchedGroup.Rows)
                            {
                                if (row is DisplayGroupLegacyRow legacyRow)
                                    count += legacyRow.Copies * copies;
                            }
                        }

                    }
                    if (count > 0)
                    {
                        switch (e.Type)
                        {
                            case OutputPanelEventTypes.AddToPrint:
                                this.outputBar.PreSendPendingPrint++;
                                outputBar.AddValueToPreSendCopiesSumPrint(count);
                                break;
                            case OutputPanelEventTypes.AddToOrder:
                                this.outputBar.PreSendPendingOrder++;
                                outputBar.AddValueToPreSendCopiesSumOrder(count);
                                break;
                        }
                    }
                }
            }
            // Enabling queueing of folder with its templates
            else if (OutputBarMode == OutputBarQueueModes.Template)
            {
                if (e.Type == OutputPanelEventTypes.AddToPrint)
                {
                    bool queuedFromContentView = false;
                    TreeIDEnumaration treeId = TreeIDEnumaration.Unknown;
                    if (tabControlContent.SelectedTab == tabPageTemplates)
                        treeId = TreeIDEnumaration.Template;
                    else if (tabControlContent.SelectedTab == tabPageOutputs)
                        treeId = TreeIDEnumaration.PrinterQueue;

                    if (this.templatesImageListView.Focused)
                        queuedFromContentView = true;

                    // Unknown means startpage is active.
                    if (treeId != TreeIDEnumaration.Unknown)
                    {
                        var templateCount = !queuedFromContentView ? GetSelectedTemplatesFromTree(treeId).Count() : GetSelectedTemplatesFromContentView(treeId).Count();
                        if (templateCount > 0)
                        {
                            this.outputBar.PreSendPendingPrint++;
                            outputBar.AddValueToPreSendCopiesSumPrint(templateCount);
                        }
                    }
                }
                else if (e.Type == OutputPanelEventTypes.AddToOrder)
                {
                    bool queuedFromContentView = false;
                    TreeIDEnumaration treeId = TreeIDEnumaration.Unknown;
                    if (tabControlContent.SelectedTab == tabPageTemplates)
                        treeId = TreeIDEnumaration.Template;
                    else if (tabControlContent.SelectedTab == tabPageOutputs)
                        treeId = TreeIDEnumaration.PrinterQueue;

                    if (this.templatesImageListView.Focused)
                        queuedFromContentView = true;

                    // Unknown means startpage is active.
                    if (treeId != TreeIDEnumaration.Unknown)
                    {
                        var templateCount = !queuedFromContentView ? GetSelectedTemplatesFromTree(treeId).Count() : GetSelectedTemplatesFromContentView(treeId).Count();
                        if (templateCount > 0)
                        {
                            this.outputBar.PreSendPendingOrder++;
                            outputBar.AddValueToPreSendCopiesSumOrder(templateCount);
                        }
                    }
                }
            }
        }

        private void outputBar_ButtonClicked(object sender, OutputPanelEventArgs e)
        {
            List<Template> thumbsTemplates = null; // Used f�r Printshop Popup
            switch (e.Type)
            {
                case OutputPanelEventTypes.AddSheetToPrint:
                    var form = GetActiveFormTemplateBase();
                    if (form != null && form.GetTemplate() != null)
                    {
                        e.Count = form.GetTemplate().GetCurrentOutputFormat().OutputFormatCapsule.LabelCount;
                        if (OutputBarMode == OutputBarQueueModes.Selection)
                            AddToQueueFromSelection(QueuedOutputStateEnumaration.PrinterQueue, e.Count);
                        else
                            AddToQueue(QueuedOutputStateEnumaration.PrinterQueue, e.Count);
                    }
                    break;

                case OutputPanelEventTypes.AddToPrint:
                    if (OutputBarMode == OutputBarQueueModes.Selection)
                        AddToQueueFromSelection(QueuedOutputStateEnumaration.PrinterQueue, e.Count);
                    else
                        AddToQueue(QueuedOutputStateEnumaration.PrinterQueue, e.Count);
                    break;
                case OutputPanelEventTypes.SendPrint:
                    SaveCheckCurrentQueuedSign();
                    bool printingWithoutQueue = false;
                    if (titleBarStrip1.MenuItemEnabled(MenuItemNames.queue))
                    {
                        var queue = QueueController.GetOutputQueue(QueuedOutputStateEnumaration.PrinterQueue);
                        if (queue.Count == 0) // if we can queue add to queue.
                        {
                            AddToQueue(QueuedOutputStateEnumaration.PrinterQueue, 1);
                            printingWithoutQueue = true;
                        }
                    }
                    thumbsTemplates = printQueue(QueuedOutputStateEnumaration.PrinterQueue, printingWithoutQueue);
                    break;
                case OutputPanelEventTypes.AddToOrder:
                    if (OutputBarMode == OutputBarQueueModes.Selection)
                        AddToQueueFromSelection(QueuedOutputStateEnumaration.PrintshopQueue, e.Count);
                    else
                        AddToQueue(QueuedOutputStateEnumaration.PrintshopQueue, e.Count);
                    break;
                case OutputPanelEventTypes.SendOrder:
                    sendOrder();
                    break;
                case OutputPanelEventTypes.AddToDS:
                    var continueAddToDsQueue = true;
                    if (dockingPanel.ActiveContent is FormTemplateDesign formTemplateDesign)
                    {
                        continueAddToDsQueue = formTemplateDesign.ValidateVideoUrlBeforeAddToDsQueue();
                    }
                    if (continueAddToDsQueue)
                    {
                        SaveCheckCurrentQueuedFlowSign();
                        AddToDSQueue();
                    }
                    break;
                case OutputPanelEventTypes.SendDS:
                    SaveCheckCurrentQueuedFlowSign();
                    SendDSQueue();
                    break;
                case OutputPanelEventTypes.ClearQueue:
                    ClearPrintQueue();
                    break;
                case OutputPanelEventTypes.ClearOrder:
                    ClearPrintshopOrderQueue();
                    this.outputBar.PreSendCopiesSumOrder = 0;
                    break;
                case OutputPanelEventTypes.ClearDS:
                    ClearFlowQueue();
                    this.outputBar.PreSendCopiesSumDS = 0;
                    break;
                case OutputPanelEventTypes.AddToCampaignQueue:
                    if (OutputBarMode == OutputBarQueueModes.Selection)
                        AddToQueueFromSelection(QueuedOutputStateEnumaration.CampaignQueue, e.Count);
                    else
                        AddToQueue(QueuedOutputStateEnumaration.CampaignQueue, e.Count);
                    templateSearch.FindVisibleCampaignTreeNodeCount();
                    templateSearch.AdjustTreeView();
                    break;
            }
            UpdateQueueCountBoxes();

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopClient) &&
            thumbsTemplates != null && thumbsTemplates.Count > 0 && printshopTeaserCounter < 2)
            {
                float cif = 100f / 25.4f;
                float bigSignArea = 500 * 700 * cif;
                bool printedBig = false;
                foreach (var t in thumbsTemplates)
                    printedBig |= t.GetCurrentOutputFormat().OutputFormatCapsule.OutputAreaInSqrCentiInch >= bigSignArea;

                Random r = new Random();
                var chance = 0.92;
                //#if DEBUG
                // chance = 0;
                //#endif
                if (!printedBig && r.NextDouble() > chance)
                {
                    List<Bitmap> maps = new List<Bitmap>();
                    // only show if there is a format big enough in the template
                    foreach (var t in thumbsTemplates)
                    {
                        int walker = -1;
                        for (int i = 0; i < t.OutputFormats.Count; i++)
                        {
                            if (t.OutputFormats[i].OutputFormatCapsule.OutputAreaInSqrCentiInch >= bigSignArea)
                                walker = i;
                        }
                        if (walker >= 0)
                        {
                            t.SetOutputFormat(t.OutputFormats[walker]);
                            maps.Add(BaseSurface.RenderTemplate(t, 150));
                        }
                    }
                    if (maps.Count > 0)
                    {
                        using (PrintshopTeaser pst = new PrintshopTeaser())
                        {
                            pst.SetSignThumbs(maps);
                            pst.ShowDialog();
                        }
                        using (var dal = Embedded.Factory.Counter)
                            dal.IncreaseCounter(CounterNames.PrintshopTeaserShown);
                        printshopTeaserCounter++;
                    }
                }
            }
        }
        private void signChildWindow_TemplateSetDirty(object sender, EventArgs e)
        {
            var templateActive = signChildWindow.GetTemplate();
            var capsuleGuid = signChildWindow.GetTemplateCapsule().Guid;
            if ((signChildWindow as FormSignProduction).ShowingFlowQueueTemplate && signChildWindow.SaveOnExit)
            {
                regenerateFlowThumb(templateActive, capsuleGuid);
                queueTemplateLayoutSizes.SetFormats(templateActive, false);
                return;
            }

            //Triggers async redrawing of all thumbnails
            currentTemplateLayoutSizes.SetFormats(templateActive, false);
        }

        private bool SaveCheckCurrentQueuedFlowSign(bool blockExecution = false)
        {
            if (signChildWindow != null && signChildWindow.SaveOnExit)
            {
                // the window is already open and needs saving before we replace it with new content
                // or we've changed tree.
                var templateActive = signChildWindow.GetTemplate();
                var capsuleGuid = signChildWindow.GetTemplateCapsule().Guid;

                resaveFlowSignAndRegenerate(templateActive, capsuleGuid, blockExecution);
                return true;
            }
            return false;
        }

        private void SaveCheckCurrentQueuedSign()
        {
            if (signChildWindow != null && signChildWindow.SaveOnExit)
            {
                // the window is already open and needs saving before we replace it with new content
                // or we've changed tree.
                var templateActive = signChildWindow.GetTemplate();
                var capsuleGuid = signChildWindow.GetTemplateCapsule().Guid;
                new QueueController(AppData.Instance).SaveItemAlreadyInQueue(capsuleGuid, templateActive);
                templateActive.Dirty = false;
            }
        }


        private void ClearPrintshopOrderQueue()
        {
            var res = MessageBoxes.ClearPrintshopOrderQueue(this);
            if (res == DialogResult.Yes)
            {
                new QueueController(AppData.Instance).ClearOutputQueue(QueuedOutputStateEnumaration.PrintshopQueue);
                _syncmanager.BeginSynchronize();
            }
        }

        private void ClearFlowQueue()
        {
            var dialogResult = DialogResult.Yes;
            if (this.outputBar.PreSendCopiesSumDS > 0)
                dialogResult = MessageBoxes.ClearFlowQueue(this, this.outputBar.PreSendCopiesSumDS);

            if (dialogResult == DialogResult.Yes)
            {
                if (previewModeSource == PreviewModeSource.Playlist)
                    StopPlayerAndResetUiStatus();

                var signsToClose = dsContentBar1.Playlist.Signs;

                dsContentBar1.SetPlayList(null);
                dsContentBar1.ClearAll();

                CloseDSSigns(signsToClose);
                toggleFlowContentBar(false);
                flowContentViewNamed.UnselectAll();
            }
        }

        private FormTemplateBase GetActiveFormTemplateBase()
        {
            FormTemplateBase activeForm = null;
            if (dockingPanel.ActiveContent is FormTemplateBase)
                activeForm = (FormTemplateBase)dockingPanel.ActiveContent;
            else
                activeForm = signChildWindow;
            return activeForm;
        }

        private Template ActiveTemplate
        {
            get
            {
                FormTemplateBase activeForm = GetActiveFormTemplateBase();
                if (activeForm == null)
                    return null;
                return activeForm.GetTemplate();
            }
        }

        private List<Guid> GetSelectedTemplatesFromContentView(TreeIDEnumaration treeId)
        {
            var selectedContent = new List<Guid>();
            foreach (var item in this.templatesImageListView.SelectedItems)
            {
                if (treeId == TreeIDEnumaration.Template || treeId == TreeIDEnumaration.CampaignRecording)
                {
                    var treeNodeAdv = templateSearch.TemplateTree.AllNodes.Where(n => (n.Tag as ShoppaTreeNode).ContentGuids.Contains((Guid)item.VirtualItemKey)).FirstOrDefault();
                    var treeNode = (treeNodeAdv.Tag as ShoppaTreeNode);
                    if (treeNode != null)
                    {
                        if (!treeNode.IsFolder)
                            selectedContent.Add(treeNode.ContentGuid);
                        else
                        {
                            var nodeFromTree = templateSearch.TemplateTree.AllNodes.Where(n => (n.Tag as ShoppaTreeNode).ContentGuids.Contains(treeNode.ContentGuid)).FirstOrDefault();
                            if (nodeFromTree != null)
                            {
                                templateSearch.TemplateTree.SuspendLayout();
                                EnsureChildrenLoaded(nodeFromTree);
                                templateSearch.TemplateTree.ResumeLayout();
                            }

                            foreach (var child in nodeFromTree.Children)
                            {
                                var guid = (child.Tag as ShoppaTreeNode).ContentGuid;
                                if (child.IsLeaf)
                                    selectedContent.Add(guid);
                            }
                        }
                    }
                }
                else if (treeId == TreeIDEnumaration.PrinterQueue)
                {
                    PrintNode treeNode = (item.Tag as PrintNode);
                    if (treeNode != null)
                    {
                        if (treeNode.NodeType == PrintNodeType.Sign)
                            selectedContent.Add(treeNode.SignGuid);
                        else if (treeNode.NodeType == PrintNodeType.OutputFormat)
                        {
                            var nodeFromTree = printTree.AllNodes.Where(n => (n.Tag as PrintNode).FormatGuid == treeNode.FormatGuid).FirstOrDefault();
                            if (nodeFromTree != null)
                            {
                                printTree.SuspendLayout();
                                EnsureChildrenLoaded(nodeFromTree);
                                printTree.ResumeLayout();
                            }

                            foreach (var child in nodeFromTree.Children)
                            {
                                var guid = (child.Tag as PrintNode).SignGuid;
                                if (child.IsLeaf)
                                    selectedContent.Add(guid);
                            }
                        }
                        else if (treeNode.NodeType == PrintNodeType.DateArchive)
                        {
                            var nodeFromTree = printTree.AllNodes.Where(n => (n.Tag as PrintNode).Text == treeNode.Text).FirstOrDefault();
                            if (nodeFromTree != null)
                            {
                                printTree.SuspendLayout();
                                EnsureChildrenLoaded(nodeFromTree);
                                printTree.ResumeLayout();
                            }
                            foreach (var folder in nodeFromTree.Children)
                            {
                                if (folder.CanExpand)
                                {
                                    printTree.SuspendLayout();
                                    EnsureChildrenLoaded(folder);
                                    printTree.ResumeLayout();
                                }

                                foreach (var child in folder.Children)
                                {
                                    var guid2 = (child.Tag as PrintNode).SignGuid;
                                    if (child.IsLeaf)
                                        selectedContent.Add(guid2);
                                }
                            }
                        }
                    }
                }
            }

            return selectedContent;
        }

        /// <summary>
        /// Get selected Template in tree with treeId
        /// </summary>
        /// <returns>Guids of selected templates. If not selection exist return empty List</returns>
        private List<Guid> GetSelectedTemplatesFromTree(TreeIDEnumaration treeId)
        {
            if (treeId == TreeIDEnumaration.Template)
            {
                return GetSelectedTemplatesFromTemplateTree().ToList();
            }
            else if (treeId == TreeIDEnumaration.CampaignRecording)
            {
                return GetSelectedTemplatesFromCampaignTree().ToList();
            }
            else if (treeId == TreeIDEnumaration.PrinterQueue)
            {
                return GetSelectedTemplatesFromPrintTree().ToList();
            }
            else
            {
                throw new NotImplementedException("Unknown treeId");
            }
        }

        private IEnumerable<Guid> GetSelectedTemplatesFromTemplateTree()
        {
            var selectedNodes = templateSearch.TemplateTree.SelectedNodes;
            if (selectedNodes == null)
                return new Guid[0];

            var selectedContent = new List<Guid>();
            foreach (var item in selectedNodes)
            {
                if (!item.IsLeaf)
                {
                    // Add templates under selected folder. No further.
                    EnsureChildrenLoaded(item);
                    foreach (var child in item.Children)
                    {
                        var templateTemplates = GetSelectedTemplatesFromTemplateNode(child);
                        selectedContent.AddRange(templateTemplates);
                    }
                }
                else
                {
                    if (item.Tag is ShoppaTreeNode shoppaTreeNode)
                        selectedContent.Add(shoppaTreeNode.ContentGuid);
                    else
                    {
                        //Silently ignore templates from unknown node types; Should not be any though...
                    }
                }
            }

            return selectedContent;
        }

        private IEnumerable<Guid> GetSelectedTemplatesFromCampaignTree()
        {
            var selectedNodes = templateSearch.CampaignTree.SelectedNodes;
            if (selectedNodes == null)
                return new Guid[0];

            var selectedContent = new List<Guid>();

            foreach (var item in selectedNodes)
            {
                var campaignNode = item.Tag as CampaignNode;
                switch (campaignNode.NodeType)
                {
                    case CampaignNodeType.Folder:
                        currentTree.SuspendLayout();
                        EnsureChildrenLoaded(item);
                        currentTree.ResumeLayout();
                        foreach (var child in item.Children)
                        {
                            var childNode = child.Tag as CampaignNode;
                            if (childNode.NodeType == CampaignNodeType.Campaign)
                            {
                                FindSignsInCampaignRecording(currentTree, selectedContent, child);
                            }
                        }
                        break;
                    case CampaignNodeType.Campaign:
                        FindSignsInCampaignRecording(currentTree, selectedContent, item);
                        break;
                    case CampaignNodeType.OutputFormat:
                        FindSignsInOutputFormatFolder(currentTree, selectedContent, item);
                        break;
                    case CampaignNodeType.Sign:
                        selectedContent.Add(campaignNode.SignGuid);
                        break;
                }
            }

            return selectedContent;
        }

        private static void FindSignsInCampaignRecording(TreeViewAdv tree, List<Guid> selectedContent, TreeNodeAdv item)
        {
            tree.SuspendLayout();
            EnsureChildrenLoaded(item);
            tree.ResumeLayout();
            foreach (var outputFormat in item.Children)
            {
                FindSignsInOutputFormatFolder(tree, selectedContent, outputFormat);
            }
        }

        private static void FindSignsInOutputFormatFolder(TreeViewAdv tree, List<Guid> selectedContent, TreeNodeAdv item)
        {
            tree.SuspendLayout();
            EnsureChildrenLoaded(item);
            tree.ResumeLayout();
            foreach (var sign in item.Children)
            {
                var signNode = sign.Tag as CampaignNode;
                selectedContent.Add(signNode.SignGuid);
            }
        }

        private IEnumerable<Guid> GetSelectedTemplatesFromTemplateNode(TreeNodeAdv child)
        {
            if (child.IsLeaf)
            {
                return new Guid[] { (child.Tag as ShoppaTreeNode).ContentGuid };
            }
            else
            {
                return new Guid[0];
            }
        }
               
        private IEnumerable<Guid> GetSelectedTemplatesFromPrintTree()
        {
            if (printTree.SelectedNodes == null)
                return new Guid[0];

            var selectedContent = new List<Guid>();

            foreach (var item in printTree.SelectedNodes)
            {
                var printTemplates = GetSelectedTemplatesFromPrintNode(item);
                selectedContent.AddRange(printTemplates);
            }

            return selectedContent;
        }

        private IEnumerable<Guid> GetSelectedTemplatesFromPrintNode(TreeNodeAdv item)
        {
            if (item.IsLeaf)
            {
                return new Guid[] { (item.Tag as PrintNode).SignGuid };
            }
            else
            {
                EnsureChildrenLoaded(item);

                var selectedContent = new List<Guid>();

                if ((item.Tag as PrintNode).NodeType != PrintNodeType.DateArchive)
                {
                    // Add templates under selected folder. No further.
                    foreach (var child in item.Children)
                    {
                        if (child.IsLeaf)
                        {
                            selectedContent.Add((child.Tag as PrintNode).SignGuid);
                        }
                    }
                }
                else
                {
                    // Add templates under each child as DateArchive only contains folders
                    foreach (var folder in item.Children)
                    {
                        // Make sure to load the children of each child node of selected DateArchive if not already loaded
                        if (folder.CanExpand)
                        {
                            printTree.SuspendLayout();
                            EnsureChildrenLoaded(folder);
                            printTree.ResumeLayout();
                        }
                        foreach (var child in folder.Children)
                        {
                            if (child.IsLeaf)
                            {
                                selectedContent.Add((child.Tag as PrintNode).SignGuid);
                            }
                        }
                    }
                }

                return selectedContent;
            }
        }

        private static void EnsureChildrenLoaded(TreeNodeAdv node)
        {
            // Load the children if they are not alredy loaded
            if (!node.IsExpandedOnce)
            {
                node.Expand();
                node.Collapse();
            }
        }

        /// <summary>
        /// Print everything in Queue of type queueType
        /// </summary>
        /// <param name="queueType"></param>
        private List<Template> printQueue(QueuedOutputStateEnumaration queueType, bool printingWithoutQueue = false, bool printingOnly = false)
        {
            var queueController = new QueueController(AppData.Instance);
            new PrinterSettingsController(AppData.Instance).FilterPrinters();

            var templates = new List<Template>();
            var destinationQueue = new DestinationQueue(Properties.Settings.Default.DocumentNameProductField, AppData.Instance);
            EslDestinationQueue eslDestinationQueue = new EslDestinationQueue();
            List<EncapsulatedQueuedSign> queueList = QueueController.GetOutputQueue(queueType);

            queueList.Sort((x, y) => y.TimeStamp.CompareTo(x.TimeStamp));
            if (queueList.Count > 0)
            {
                using (var progressForm = new AsyncProgressBarForm())
                {
                    ThemeUtil.ApplyTo(progressForm);
                    if (queueList.Count > 15)
                    {
                        var myAsyncAction = new LoadSignsForPrintDelegate(LoadSignsForPrintAsync);

                        var result = myAsyncAction.BeginInvoke(destinationQueue, queueList, progressForm.GetReporter(), progressForm.GetCallback(), null);
                        int x = this.Location.X + this.Width / 2 - progressForm.Width / 2;
                        int y = this.Location.Y + this.Height / 2 - progressForm.Height / 2;
                        progressForm.StartPosition = FormStartPosition.Manual;
                        progressForm.Location = new Point(x, y);

                        progressForm.ShowDialog();

                        var invokeResult = myAsyncAction.EndInvoke(result);

                        destinationQueue = invokeResult.DestinationQueue;
                        eslDestinationQueue = invokeResult.EslDestinationQueue;
                        templates = invokeResult.Templates;
                    }
                    else
                    {
                        var result = LoadSignsForPrintAsync(destinationQueue, queueList, progressForm.GetReporter());
                        destinationQueue = result.DestinationQueue;
                        eslDestinationQueue = result.EslDestinationQueue;
                        templates = result.Templates;
                    }
                }
            }

            DateTime printTime = DateTime.Now;

            Printing.Print p = new Shoppa.PL.Windows.Printing.Print();
            p.PrintingPDF += hotfolderWatcher_HotFolderPrintingPDF;

            PrintErrorCollection errorCollection = null;
            bool shouldTry = true;
            while (shouldTry)
            {
                try
                {
                    errorCollection = p.StartPrint(destinationQueue, PrinterPrerequisitesTasks);
                    shouldTry = false;
                }
                catch (OperationCanceledException)
                {
                    if (MessageBoxes.PDFFailedTryAgain(this) == System.Windows.Forms.DialogResult.Yes)
                        shouldTry = true;
                    else
                        throw;
                }
            }

            if (!SendEslDestinationQueue(eslDestinationQueue))
            {
                throw new OperationCanceledException();
            }

            if (!printingOnly)
            {
                queueController.ArchivePrinterQueue(errorCollection.AllAffectedSignGuids, printTime);
                // Changes in printTree can fire SelectionChanged and switch "currentTree" to printTree. We do not what that.
                List<Guid> newNodeGuid = null;
                if (printTree != null) // Autoprint may try to print before GUI, there is no tree then
                {
                    printTree.SelectionChanged -= new EventHandler(printTree_SelectionChanged);
                    newNodeGuid = printTree.MoveCurrentQueueToArchive(errorCollection.AllAffectedSignGuids, printTime);
                    printTree.SelectionChanged += new EventHandler(printTree_SelectionChanged);
                }

                // we do not need to change your selection if you are printing without queue.
                if (!printingWithoutQueue)
                {
                    var selectedTab = tabControlContent.SelectedTab;
                    if (selectedTab == tabPageOutputs)
                    {
                        forceUpdateAfterBrowsing = true;
                        if (newNodeGuid != null)
                            printTree.BrowseToContent(newNodeGuid);
                        else
                            printTree.SelectedNode = printTree.Root.Children[0];
                    }

                    UpdateQueueCountBoxes();
                }
            }

            if (errorCollection.Errors.Count > 0)
            {
                ShowPrintErrorMessage(errorCollection.Errors);
                if (printingWithoutQueue) // if we fail when printing directly we need to clearOutputQueue
                {
                    queueController.ClearOutputQueue(QueuedOutputStateEnumaration.PrinterQueue);
                    if (printTree != null) // Autoprint may try to print before GUI, there is no tree then
                    {
                        // InvalidateCurrentQueue will fire SelectionChanged and switch "currentTree" to printTree. We do not want that.
                        printTree.SelectionChanged -= new EventHandler(printTree_SelectionChanged);
                        printTree.InvalidateCurrentQueue();
                        printTree.SelectionChanged += new EventHandler(printTree_SelectionChanged);
                    }
                }
            }

            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.OEM_OKI))
            {
                foreach (var usedPrinter in errorCollection.Printers)
                {
                    if (errorCollection.Errors.Find(e => e.affectedPrinter == usedPrinter) == null)
                    {
                        var printer = PrinterDriverScanner.Instance.Find(usedPrinter);
                        if (!printer.PrinterIsOnConsumableReplacementContract())
                        {
                            PrinterConsumableController.GetWarningNeedAsync(printer).ContinueWith(async t =>
                            {
                                if (t.Status == TaskStatus.RanToCompletion && t.Result.types != PrinterConsumableController.ConsumableTypes.None)
                                {
                                    oneDialogAtATime.Wait();
                                    try
                                    {
                                        Invoke((MethodInvoker)delegate
                                        {
                                            FormConsumablesPopup pop = new FormConsumablesPopup { Owner = this };
                                            pop.SetConsumableLevels(printer.DriverName, printer.PrinterName, printer.ModelName, t.Result.levels, t.Result.types);
                                            pop.ShowDialog();
                                        });
                                    }
                                    finally
                                    {
                                        oneDialogAtATime.Release();
                                    }
                                }
                            });
                        }
                    }
                }
            }
            _syncmanager.BeginSynchronize();

            // Application insights event - using centralized service
            PrintQueueTelemetryService.TrackMultipleTemplatesEvent(
                "ShoppaDesktop_PrintQueue",
                templates,
                queueList
            );

            return templates;
        }


        SemaphoreSlim oneDialogAtATime = new SemaphoreSlim(1);


        public class LoadedSignsReturnObject
        {
            public DestinationQueue DestinationQueue { get; set; }
            public EslDestinationQueue EslDestinationQueue { get; set; }
            public List<Template> Templates { get; set; }
        }

        private delegate LoadedSignsReturnObject LoadSignsForPrintDelegate(
            DestinationQueue destinationQueue,
            IReadOnlyList<EncapsulatedQueuedSign> queueList,
            AsyncProgressBarForm.ProgressReporter reporter
        );
        
        private LoadedSignsReturnObject LoadSignsForPrintAsync(
            DestinationQueue destinationQueue,
            IReadOnlyList<EncapsulatedQueuedSign> queueList,
            AsyncProgressBarForm.ProgressReporter reporter)
        {
            reporter.Report(MLS.Instance.TranslateText("MF_SplashPrepareSignsforprint"));

            var queueController = new QueueController(AppData.Instance);
            var outputController = new OutputController(AppData.Instance);

            var templates = new List<Template>();
            EslDestinationQueue eslDestinationQueue = new EslDestinationQueue();
            FinalDestination destination = null;
            Dictionary<Guid, FinalDestination> usedDestination = new Dictionary<Guid, FinalDestination>();
            int sortOrder = 0;

            var printedProducts = new List<Product>();

            bool doEsl = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ESL);
            IEslProvider api = null;
            try
            {
                for (int i = 0; i < queueList.Count; i++)
                {
                    var queuedSign = queueList[i];

                    DateTime priceDate = _productSearchStateFilter?.PriceDate ?? DateTime.Today;
                    var templatePrintInfo = queueController.GetTemplatePrintInfo(queuedSign.Guid, priceDate);

                    if (templatePrintInfo?.IsReadyForPrint != true)
                        continue;

                    if (templates.Count < 10)
                        templates.Add(templatePrintInfo.GetOrLoadTemplate());
                    
                    Guid destinationBindingGuid = templatePrintInfo.CurrentOutputFormat.OutputFormatGuid;
                    Guid destGuid = Guid.Empty;

                    bool useBleed = false;

                    if (templatePrintInfo.CurrentOutputFormat.Layout.PrintTofile)
                    {
                        destinationBindingGuid = OutputController.GetGuidForDefaultFormats(DefaultFormats.FilePrinter);
                        useBleed = templatePrintInfo.CurrentOutputFormat.Layout.UseBleed;
                    }
                    if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.OEM_OKI))
                    {
                        destinationBindingGuid = queuedSign.Guid; // in this case, we use the signs guid instead
                        destGuid = outputController.GetFormatDestinationBinding(destinationBindingGuid);

                        if (usedDestination.ContainsKey(destGuid))
                            destination = usedDestination[destGuid];
                        else destGuid = Guid.Empty;
                    }

                    if (destGuid == Guid.Empty)
                    {
                        if (usedDestination.ContainsKey(destinationBindingGuid))
                            destination = usedDestination[destinationBindingGuid];
                        else
                        {
                            if (doEsl && templatePrintInfo.CurrentOutputFormat.OutputFormatCapsule.Unit == OutputUnits.Pixel)
                            {
                                if (api == null)
                                    api = LoadEslPlugin();

                                var product = templatePrintInfo.ProductForEsl; // this code is doing stuff that is specific to pricer. what can we do?
                                var productId = ProductController.GetProductId(product);
                                var productCode = product.GetProductCodeForEsl(api.ProductCodeUsed);
                                var eslInfo = EslController.GetEslInfoForProduct(api, productCode);

                                if (!productId.HasValue || !eslInfo.IsValid)
                                {
                                    MessageBoxes.SendToEslError(this);
                                    throw new OperationCanceledException("Failed to find product Id.");
                                }

                                var eslDestination = new DestinationEsl()
                                {
                                    EslRenderInfo = eslInfo,
                                    TargetProductCode = productCode,
                                    TargetProductId = (int)productId
                                };

                                destination = eslDestination;
                            }
                            else
                            {
                                try
                                {
                                    new DestinationQueue(Properties.Settings.Default.DocumentNameProductField, AppData.Instance).GetFinalDestination(destinationBindingGuid, out destination);
                                }
                                catch (Exception)
                                {
                                    CMessageBox.Show("Default printer required to use this function.");
                                    throw new NotImplementedException("Need to more gracefully handle missing printer");
                                }
                                if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.OEM_OKI))
                                    usedDestination.Add(destination.EncapsulatedFinalDestination.Guid, destination);
                                else
                                    usedDestination.Add(destinationBindingGuid, destination);
                            }
                        }
                    }

                    if (destination is DestinationEsl destinationEsl)
                    {
                        // TODO: This is slow and does not use the optimizations made for the regular destination queue.
                        var template = templatePrintInfo.GetOrLoadTemplate();
                        eslDestinationQueue.AddToQueue(template, queuedSign.Guid, destinationEsl);
                    }
                    else
                    {
                        destinationQueue.AddToQueue(templatePrintInfo, destination, queuedSign.Copies, queuedSign.Name, sortOrder++, useBleed);
                        printedProducts.AddRange(templatePrintInfo.Products.GetLegacy());

                        // If we're starting to retain a lot of product references it is time to release them.
                        // We only keep them because we run SetLastAccessed on them when the queue is finished building.
                        // The method has expensive overhead so we want to avoid running it too many times,
                        // but retaining tens of thousands of products uses a lot of memory.
                        if (printedProducts.Count >= MAX_IN_MEMORY_PRODUCTS_DURING_PRINT)
                        {
                            ProductController.SetLastAccessed(printedProducts);
                            printedProducts.Clear();
                        }
                    }
                    
                    // After this point we don't need the template's products anymore but it can use up a lot of memory
                    // when there's thousands of signs to print, so we null it to let GC eat it up.
                    templatePrintInfo.Products = null;

                    reporter.Report((i * 100) / queueList.Count);
                }
            }
            finally
            {
                api?.Dispose();
            }
            
            ProductController.SetLastAccessed(printedProducts);

            return new LoadedSignsReturnObject()
            {
                DestinationQueue = destinationQueue,
                EslDestinationQueue = eslDestinationQueue,
                Templates = templates
            };
        }

        private IEslProvider LoadEslPlugin()
        {
            var api = new Controllers.Plugins.PluginController(AppData.Instance).GetPlugin<IEslProvider>();
            if (!EslController.IsReady(api))
            {
                Trace.WriteLineIf(traceSwitch.TraceWarning, "EslPlugin is not ready.", "W");
                ShowEslNotReadyMessageBox();
                throw new OperationCanceledException();
            }

            return api;
        }

        private void ShowEslNotReadyMessageBox()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => ShowEslNotReadyMessageBox()));
                return;
            }

            MessageBoxes.EslPluginNotReadyError(this);
        }

        private void ShowPrintErrorMessage(List<PrintError> errors)
        {
            List<PrintErrorReason> distinctErrorReasons = new List<PrintErrorReason>();
            foreach (PrintError error in errors)
            {
                if (!distinctErrorReasons.Contains(error.errorReason)) distinctErrorReasons.Add(error.errorReason);
            }
            string errorText = "";
            foreach (PrintErrorReason err in distinctErrorReasons)
            {
                switch (err)
                {
                    case PrintErrorReason.PrinterUnreachable:
                        errorText = MLS.Instance.TranslateText("MF_printingErrorNotAccessed");
                        break;
                    case PrintErrorReason.OutputFormatDoesNotFit:
                        errorText = MLS.Instance.TranslateText("MF_printingErrorNoFit");
                        break;
                    case PrintErrorReason.UnKnown:
                        errorText = MLS.Instance.TranslateText("MF_printingErrorUnKnown");
                        break;
                    case PrintErrorReason.CouldNotAdjustPrinterSettings:
                        errorText = MLS.Instance.TranslateText("MF_printingErrorCouldNotAdjustSettings");
                        break;
                    case PrintErrorReason.CouldNotInitializePrinter:
                        errorText = MLS.Instance.TranslateText("MF_printingCouldNotInitialize");
                        break;
                    default:
                        break;
                }
            }
            if (errorText != "")
                CMessageBox.Show(errorText, MLS.Instance.TranslateText("MF_printingErrorCaption"), MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Mimic flow of 
        /// OutputBar2.outputBarInnerStrip2_SendButtonClicked
        /// </summary>
        private void buttonPrint_Click(object sender, EventArgs e)
        {
            OutputPanelEventArgs ee = new OutputPanelEventArgs { Type = OutputPanelEventTypes.SendPrint };
            // OnButtonClicked(OutputPanelEventArgs e)
            outputBar.AllowUpdatePreSendCopiesSumPrint = true;
            outputBar.AllowUpdatePreSendCopiesSumOrder = true;
            outputBar.AllowUpdatePreSendCopiesSumCampaign = true;
            outputBar_ButtonClicked(sender, ee);
        }

        private void buttonOrderFromOutputs_Click(object sender, EventArgs e)
        {
            sendOrder();
        }

        private bool SendEslDestinationQueue(EslDestinationQueue queue)
        {
            var failedTargetProducts = new List<int>() { };
            var succeededTargetProducts = new HashSet<int>() { };
            using (var api = new Controllers.Plugins.PluginController(AppData.Instance).GetPlugin<IEslProvider>())
            {
                foreach (var queuedEsl in queue.QueuedOutputFormats)
                {
                    var template = queuedEsl.Value.Template;
                    Bitmap renderedEsl = Esl.EslRenderer.RenderForESL(template);

                    Esl.EslRenderer.DitherImage(ref renderedEsl, queuedEsl.Value.Destination.EslRenderInfo);
                    var success = EslController.SendToEsl(api, renderedEsl, queuedEsl.Value.Destination.TargetProductCode);
                    if (success)
                    {
                        succeededTargetProducts.Add(queuedEsl.Value.Destination.TargetProductId);
                    }
                    else
                    {
                        failedTargetProducts.Add(queuedEsl.Value.Destination.TargetProductId);
                    }
                }
            }
            ProductController.SetLastEslRender(succeededTargetProducts.Distinct().ToList());

            var allSignsSent = failedTargetProducts.Count == 0;
            if (!allSignsSent)
            {
                MessageBoxes.SendToEslError(this);
            }
            return allSignsSent;
        }

        private void sendOrder()
        {
            if (Server.Factory.OfflineMode)
            {
                CMessageBox.Show("Can't find an internet connection. a connection to internet is required for printshop to work. Your order has not been sent", "No internet connection was found", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var queueController = new QueueController(AppData.Instance);
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);
            var printshopController = new PrintshopController(AppData.Instance);

            //TODO: Make sure there are signs to order?
            //QueueController qc = new QueueController();
            // List<EncapsulatedQueuedSign> queueList = qc.GetOrderQueue();
            //  if (queueList.Count == 0) return;
            if (this.outputBar.PreSendCopiesSumOrder == 0) return;

            //Make sure the order queue is synchronized
            if (!_syncmanager.Syncronize().Success) return;

            //Display the order confirmation dialog
            FormPrintshopOrder orderDialog;
            try
            {
                orderDialog = new FormPrintshopOrder();
                orderDialog.PrintOrderIsChanged += new EventHandler<EventArgs>(orderDialog_PrintOrderIsChanged);
                if (orderDialog.ShowDialog() == DialogResult.Cancel)
                    return;
            }
            catch (ApplicationException ex)
            {
                CMessageBox.Show(ex.Message, "Printshop Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            catch (Exception)
            {
                CMessageBox.Show("An unexpected exception occurred during ordering.\nThe order has NOT been placed.\nPlease try again later.", "Printshop Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            ///OBS!!!! temporary solution of generating thumbs on Remote. 
            /// The code will be moved to PrintshopController.CreateOrder() when BaseSurface is be moved to BL
            ///Generate thumbs for PrintShop signs and send them to remote
            List<EncapsulatedQueuedSign> queuedSigns = QueueController.GetOutputQueue(QueuedOutputStateEnumaration.PrintshopQueue, true);
            List<Product> usedProducts = new List<Product>();
            foreach (EncapsulatedQueuedSign sign in queuedSigns)
            {
                Template template = queueController.GetTemplate(sign.Guid);
                usedProducts.AddRange(template.Products.GetLegacy());
                Bitmap bitmap = Shoppa.PL.Windows.Controls.BaseSurface.RenderTemplate(template, PictureConstants.thumbForPrintshop);
                byte[] data = ImageController.GetThumbTemplateImage(bitmap, PictureConstants.thumbForPrintshop);
                sign.Thumb = data;
                templateController.IncreaseTemplateCounter(TemplateCounter.PrintshopOrder, sign.TemplateGuid, sign.LayoutGuid, sign.OutputFormatQueued, sign.Copies);
            }
            //Create a printshop order on the server from the current order queue
            EncapsulatedPrintshopOrder receiptOrder;
            try
            {
                receiptOrder = printshopController.CreateOrder(
                    orderDialog.PrintshopId,
                    orderDialog.SelectedAddresses,
                    orderDialog.AvailableCoupons,
                    orderDialog.TotalCost,
                    orderDialog.SelectedAdditionalCosts
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.WriteLineIf(traceSwitch.TraceWarning, ex.ToString(), "W");
                CMessageBox.Show("Failed to place order with Printshop Service at this time.\nPlease try again later.", "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            PrintshopController.GenerateOrderSigns(receiptOrder.Id, orderDialog.PrintshopId, queuedSigns);
            UpdateQueueCountBoxes();

            ProductController.SetLastAccessed(usedProducts);

            using (FormPrintshopOrderConfirmation newform = new FormPrintshopOrderConfirmation(receiptOrder))
            {
                newform.ShowDialog();
            }
        }

        void orderDialog_PrintOrderIsChanged(object sender, EventArgs e)
        {
            _syncmanager.SyncronizeOutputQueue();
            (sender as FormPrintshopOrder).UppdateOffer();
        }

        #endregion PRINTER QUEUE

        #region OutputSizes
        private void TemplateLayoutSizes_OutputFormatChanged(object sender, OutputFormatChangedEventArgs e)
        {
            if (!(dockingPanel.ActiveContent is FormTemplateBase))
            {
                lock (_activeDocumentChangedSubscriptionLock)
                {
                    dockingPanel.ActiveContentChanged -= new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                    TreeIDEnumaration id = TreeIDEnumaration.Template;
                    Guid templateGuid = Guid.Empty;

                    if (sender == currentTemplateLayoutSizes && templateSearch.TemplateTree.SelectedNode != null)
                    {
                        templateGuid = (templateSearch.TemplateTree.SelectedNode.Tag as ShoppaTreeNode).ContentGuid;
                    }
                    else if (sender == queueTemplateLayoutSizes && printTree.SelectedNode != null)
                    {
                        templateGuid = (printTree.SelectedNode.Tag as PrintNode).SignGuid;
                        id = TreeIDEnumaration.PrinterQueue;
                    }
                    if (templateGuid != Guid.Empty)
                    {
                        OpenTemplate(templateGuid, id, true);
                        toggleCanQueue(true);
                    }
                    dockingPanel.ActiveContentChanged += new EventHandler<EventArgs>(dockingPanel_ActiveContentChanged);
                }
            }

            var activeForm = (FormTemplateBase)dockingPanel.ActiveContent;
            var template = activeForm.GetTemplate();

            activeForm.SetOutputFormat(e.Format, e.ActiveLinkGroup, e.AllLinkGroups, e.SuperLinked, e.HasSetLinkedLayout);

            if (AppData.Instance.CurrentUser.IsInAnyRole(QueueSheetAddOns))
                toggleCanQueueSheet(true);

            if (sender == currentTemplateLayoutSizes)
            {
                if (e.HasSetLinkedLayout && activeForm is FormTemplateDesign)
                    template.Dirty = true;

                stack?.ClearStack();
                stack?.AddElementsToStack(activeForm.GetStacked());
            }

            ChangeFormatInQueue(template.GetCurrentOutputFormat());
            StopPlayingDesignModePreview();
        }

        private void Messenger_AddOrEditLayout()
        {
            if (!(dockingPanel.ActiveContent is FormTemplateBase)) return;
            FormTemplateBase activeForm = (FormTemplateBase)dockingPanel.ActiveContent;
            using (Add_Edit_OutputFormat aeof = new Add_Edit_OutputFormat())
            {
                Template template = activeForm.GetTemplate();

                aeof.SelectedFormats = template.OutputFormats;

                if (aeof.ShowDialog() == DialogResult.OK && aeof.IsChanged)
                {
                    //TODO: Consider refactoring this
                    template.OutputFormats.AddRange(aeof.NewFormats);
                    template.SetUpFormatsAfterAddEdit(aeof.NewFormats, aeof.GetAllFormatsSorted(), aeof.NewFormatsWithMatchedBaseLayout);

                    template.RemoveOutputFormats(aeof.RemovedFormats);
                    currentTemplateLayoutSizes.UpdateLayoutNames(aeof.RenamedLayouts);
                    currentTemplateLayoutSizes.SetFormats(template, true);
                    currentTemplateLayoutSizes.FireOutputFormatChanged(); // TODO: No idea whether this is required in new UI

                    Messenger.Send(Messenger.Events.DesignTemplateIsDirty, activeForm);
                }
            }
        }

        private void currentOutputSizes_MouseUp(object sender, MouseEventArgs e)
        {
            if (dockingPanel.ActiveContent is FormTemplateDesign activeForm && activeForm.ShouldMaintainFocusInDesignWhenChangingLayout())
                activeForm.RefocusDesignMode();
        }

        #endregion

        private void logoutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            CustomerController.LogoutUser();
            //By NOT clearing AppData.Instance.CurrentUser, the current user is still available during application closing
            this.Close();
        }

        private void ExportTemplate(string path)
        {
            if (string.IsNullOrEmpty(path))
                throw new ArgumentException("Path cannot be null or empty.");

            if (dockingPanel.ActiveContent is FormTemplateBase)
            {
                var tree = GetTreeFromTreeId(TreeIDEnumaration.Template);
                var selected = tree.SelectedNode;

                if (!selected.IsLeaf)
                    return;

                var ftb = dockingPanel.ActiveContent as FormTemplateBase;
                var templateCapsule = ftb.GetTemplateCapsule();
                Template template = ftb.GetTemplate();
                templateCapsule.Data = TemplateController.SerializeTemplate(template);

                byte[] data = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance).ExportTemplate(templateCapsule);
                System.IO.File.WriteAllBytes(path, data);
            }
        }

        private void ImportTemplate(string path)
        {
            if (string.IsNullOrEmpty(path))
                throw new ArgumentException("Path cannot be null or empty.");

            TemplateDataImporter templateImporter = new TemplateDataImporter(path, GetTreeFolderNode(TreeIDEnumaration.Template, TreeGroupInformation.GroupType.MyMaterial));

            templateImporter.BeforeOverwrite += (s, args) =>
            {
                ImportTemplateConflictForm cf = new ImportTemplateConflictForm(args.TemplateAlreadyExists, args.ConflictingPictures.Any(), args.ConflictingColors.Any());
                DialogResult dialogResult = cf.ShowDialog();
                if (dialogResult == DialogResult.OK)
                {
                    args.OverwriteRule = cf.ImportAsCopy ? ImportOverwriteRule.AppendAsNewData : ImportOverwriteRule.OverwriteExistingData;
                }
                else
                    args.OverwriteRule = ImportOverwriteRule.CancelImport;
            };

            SyncronizedEventArgs syncronizedEventArgs = templateImporter.Import(
                defaultTemplateFolder: () => GetTreeFolderGuid(TreeIDEnumaration.Template, TreeGroupInformation.GroupType.MyMaterial),
                defaultPictureFolder: () => GetTreeFolderGuid(TreeIDEnumaration.Picture, TreeGroupInformation.GroupType.MyMaterial));

            if (syncronizedEventArgs != null)
            {
                // Update gui using sync event
                _syncmanager_Syncronized(this, syncronizedEventArgs);
            }
        }

        private Guid GetTreeFolderGuid(TreeIDEnumaration treeID, TreeGroupInformation.GroupType groupType)
        {
            return ((ShoppaTreeNode)GetTreeFolderNode(treeID, groupType).Tag).ContentGuid;
        }

        private TreeNodeAdv GetTreeFolderNode(TreeIDEnumaration treeID, TreeGroupInformation.GroupType groupType)
        {
            var tTree = GetTreeFromTreeId(treeID);
            return tTree.GetRoot(groupType);
        }

        private void saveAsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dockingPanel.ActiveContent is FormTemplateBase)
            {
                var tree = GetTreeFromTreeId(TreeIDEnumaration.Template);
                var selected = tree.SelectedNode;

                if (!selected.IsLeaf)
                    return;

                var ftb = dockingPanel.ActiveContent as FormTemplateBase;
                if (ftb != null && selected != null)
                {
                    var parent = selected.Parent;

                    if (parent != null && parent.Tag != null && parent.Tag is ShoppaTreeNode)
                    {
                        Guid folderGuid = (parent.Tag as ShoppaTreeNode).ContentGuid;
                        if (!new Permission.Folder(AppData.Instance).CanAddOrRemoveContent(folderGuid, TreeIDEnumaration.Template))
                        {
                            // Unable to add under same folder, add to MyMaterial instead.
                            parent = tree.GetRoot(TreeGroupInformation.GroupType.MyMaterial);
                            if (parent != null)
                                folderGuid = (parent.Tag as ShoppaTreeNode).ContentGuid;
                        }

                        var templateCapsule = ftb.GetTemplateCapsule();
                        using (var nif = new NameInputForm(templateCapsule.Name, NameInputForm.NameInputTypeEnum.Template))
                        {
                            foreach (var item in parent.Children)
                                nif.AvailableNames.Add((item.Tag as NodeBase).Text);

                            if (nif.ShowDialog() != DialogResult.OK)
                                return;

                            ftb.NameForSaveAs = nif.GetText();
                            ftb.ParentFolderForSaveAs = folderGuid;
                            ftb.saveTemplateAs();

                            templateCapsule = ftb.GetTemplateCapsule(); // get capsule once more as its name was changed
                            AddTemplateAndSelectIt(templateCapsule);
                        }
                        templateSearch.FindVisibleTemplateTreeNodeCount();
                        templateSearch.AdjustTreeView();
                    }
                }
            }
        }

        private void AddTemplateAndSelectIt(EncapsulatedTemplate template)
        {
            var tree = GetTreeFromTreeId(TreeIDEnumaration.Template);
            var selected = tree.SelectedNode;

            if (new TemplateController(AppData.Instance, TemplateCacheInstance.Instance).Add(template, templateSearch.TemplateTree))
            {
                selected.IsSelected = false; // Deselect previous node

                var path = TreeController2.GetPathToItem(TreeIDEnumaration.Template, template.Guid);
                path.Add(template.Guid);    // Add own node last
                tree.BrowseToContent(path); // Select new Node

                _syncmanager.BeginSynchronize();
            }
        }

        private void aboutShoppaToolStripMenuItem_Click(object sender, EventArgs e)
        {
#if DEBUG
            // Graphical profile inspector intended for developers working with colours.
            // This form should never be shown to customers and will not be included if shoppa is not built in DEBUG mode.
            if (ModifierKeys.HasFlag(Keys.Control) && Keyboard.IsKeyDown(Key.G))
            {
                using (var inspector = new GraphicalProfileInspector())
                {
                    inspector.ShowDialog();
                }
                return;
            }
#endif

            using (var about = new FormLoadingScreen())
            {
                about.MakeAboutBox();
                about.ShowDialog();
            }
        }

        AutoSignage.DisplayGroupAddPanel _displayGroupAddPanel;
        private void ShowLegacyDisplayGroupAddPanel(AutoSignage.Objects.LegacyDisplayGroupEditEventArgs e)
        {
            if (_legacyDisplayGroupAddPanel != null || _displayGroupAddPanel != null)
                return;

            var triggerAddPanelHeight = 77;
            var dockingPanelInitialWidth = dockingPanel.Width;
            var dockingPanelInitialY = dockingPanel.Location.Y;

            dockingPanel.Dock = DockStyle.None;
            dockingPanel.Width = dockingPanelInitialWidth;
            dockingPanel.Height = this.Height - dockingPanelInitialY - triggerAddPanelHeight;
            dockingPanel.Location = new Point(splitter1.Location.X + splitter1.Width, dockingPanelInitialY + triggerAddPanelHeight);
            dockingPanel.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;

            _legacyDisplayGroupAddPanel = new AutoSignage.LegacyDisplayGroupAddPanel(e.Format, e.DisplayGroup)
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top,
                Width = dockingPanelInitialWidth,
                Height = triggerAddPanelHeight,
                Location = new Point(splitter1.Location.X + splitter1.Width, dockingPanelInitialY)
            };

            _legacyDisplayGroupAddPanel.SaveRequested += LegacyDisplayGroupAddPanel_SaveRequested;
            _legacyDisplayGroupAddPanel.Cancelled += DisplayGroupAddPanel_Cancelled;
            _legacyDisplayGroupAddPanel.QueryTemplateInformation += GetTemplateInformation;
            this.Controls.Add(_legacyDisplayGroupAddPanel);

            if (_startpageFanout?.IsShowing ?? false)
                _startpageFanout.Hide();
        }


        AutoSignage.LegacyDisplayGroupAddPanel _legacyDisplayGroupAddPanel;
        private void ShowDisplayerGroupAddPanel(AutoSignage.Objects.DisplayGroupAddEventArgs e)
        {
            if (_displayGroupAddPanel != null)
                return;
            var triggerAddPanelHeight = 77;
            var dockingPanelInitialWidth = dockingPanel.Width;
            var dockingPanelInitialY = dockingPanel.Location.Y;

            dockingPanel.Dock = DockStyle.None;
            dockingPanel.Width = dockingPanelInitialWidth;
            dockingPanel.Height = this.Height - dockingPanelInitialY - triggerAddPanelHeight;
            dockingPanel.Location = new Point(splitter1.Location.X + splitter1.Width, dockingPanelInitialY + triggerAddPanelHeight);
            dockingPanel.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;

            _displayGroupAddPanel = new AutoSignage.DisplayGroupAddPanel(e)
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top,
                Width = dockingPanelInitialWidth,
                Height = triggerAddPanelHeight,
                Location = new Point(splitter1.Location.X + splitter1.Width, dockingPanelInitialY)
            };

            _displayGroupAddPanel.SaveRequested += DisplayGroupAddPanel_SaveRequested;
            _displayGroupAddPanel.Cancelled += DisplayGroupAddPanel_Cancelled;
            _displayGroupAddPanel.QueryTemplateInformation += GetTemplateInformation;
            this.Controls.Add(_displayGroupAddPanel);

            if (_startpageFanout?.IsShowing ?? false)
                _startpageFanout.Hide();
        }

        private void RemoveTriggerAddPanel()
        {
            if (_displayGroupAddPanel != null)
            {
                this.Controls.Remove(_displayGroupAddPanel);
                _displayGroupAddPanel.SaveRequested -= DisplayGroupAddPanel_SaveRequested;
                _displayGroupAddPanel.Cancelled -= DisplayGroupAddPanel_Cancelled;
                _displayGroupAddPanel.QueryTemplateInformation -= GetTemplateInformation;
                _displayGroupAddPanel.UnregisterMessanging();
                _displayGroupAddPanel.Dispose();
                _displayGroupAddPanel = null;
            }
            if (_legacyDisplayGroupAddPanel != null)
            {
                this.Controls.Remove(_legacyDisplayGroupAddPanel);
                _legacyDisplayGroupAddPanel.SaveRequested -= LegacyDisplayGroupAddPanel_SaveRequested;
                _legacyDisplayGroupAddPanel.Cancelled -= DisplayGroupAddPanel_Cancelled;
                _legacyDisplayGroupAddPanel.QueryTemplateInformation -= GetTemplateInformation;
                _legacyDisplayGroupAddPanel.UnregisterMessanging();
                _legacyDisplayGroupAddPanel.Dispose();
                _legacyDisplayGroupAddPanel = null;
            }
        }

        private void DisplayGroupAddPanel_Cancelled(object sender, EventArgs e)
        {
            ResetDockingPanel();
            RemoveTriggerAddPanel();
            _curFormAutoSignage.ShowDialog();
        }

        private void DisplayGroupAddPanel_SaveRequested(object sender, AutoSignage.Objects.DisplayGroupAddedEventArgs e)
        {
            ResetDockingPanel();
            RemoveTriggerAddPanel();
            _curFormAutoSignage.DisplayGroupAdded(e.DisplayGroup);
        }


        private void LegacyDisplayGroupAddPanel_SaveRequested(object sender, AutoSignage.Objects.DisplayGroupAddedEventArgs e)
        {
            ResetDockingPanel();
            RemoveTriggerAddPanel();
            _curFormAutoSignage.LegacyDisplayGroupChanged(e.DisplayGroup);
        }

        private void ResetDockingPanel()
        {
            dockingPanel.Anchor = AnchorStyles.None;
            dockingPanel.Dock = DockStyle.Fill;
        }

        private void saveAsPictureToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.OEM_OKI))
            {
                if (!PrinterDriverScanner.Instance.AllPrinters.Any())
                {
                    CMessageBox.Show(this, MLS.Instance.TranslateText("MF_DisabledNoOKI"), MLS.Instance.TranslateText("MF_FeatureDisabled"));
                    return;
                }
            }

            using (SaveFileDialog sfd = new SaveFileDialog())
            {
                FormTemplateBase templateForm = dockingPanel.ActiveContent as FormTemplateBase;
                if (templateForm == null)
                    return;

                bool userHasPdfPrinterAddon = AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PDFPrinter);

                Template template = templateForm.Template;
                OutputFormat outputFormat = template.GetCurrentOutputFormat();

                sfd.AddExtension = true;
                sfd.Filter = SAVE_AS_PICTURE_DEFAULT_FILTER;

                if (userHasPdfPrinterAddon)
                {
                    sfd.Filter = $"{sfd.Filter}|{SAVE_AS_PICTURE_PDF_FILTER}";
                }

                sfd.DefaultExt = ".png";
                sfd.FileName = template.GetCurrentLayout().Name + " " + outputFormat.ToString();

                if (sfd.ShowDialog() != DialogResult.OK)
                    return;

                string extension = Path.GetExtension(sfd.FileName);

                if (userHasPdfPrinterAddon && IsExtensionPDF(extension))
                {
                    ExportTemplateAsPdf(sfd.FileName, templateForm.GetTemplateCapsule().Guid, template);
                }
                else
                {
                    MemoryStream ms = new MemoryStream();
                    exportTemplateAsPicture(ms, GetImageFormatFromExtension(extension));

                    if (ms.Length > 0)
                    {
                        File.WriteAllBytes(sfd.FileName, ms.ToArray());
                    }
                }
            }
        }

        private static void ExportTemplateAsPdf(string savePath, Guid templateGuid, Template template)
        {
            FinalDestination destination = DestinationFactory.ConstructDestination(FinalDestinationType.FinalDestinationTypes.PDF, AppData.Instance.CurrentUser);
            destination.EncapsulatedFinalDestination.Guid = OutputController.GetGuidForDefaultFormats(DefaultFormats.FilePrinter);

            DestinationQueue queue = new DestinationQueue(Properties.Settings.Default.DocumentNameProductField, AppData.Instance);
            queue.AddToQueue(template, templateGuid, destination, 1, savePath); // Why is template guid passed as sign guid? #12768

            OutputFormat outputFormat = template.GetCurrentOutputFormat();
            ushort previousDpiChoice = outputFormat.DpiChoise;

            try
            {
                if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.ShoppaOkey))
                {
                    outputFormat.DpiChoise = OEM_OKI_DPI_CHOISE;
                }
                else
                {
                    outputFormat.DpiChoise = OPEN_DPI_SELECTION_DIALOGUE;
                }

                Print print = new Print();

                print.PrintingPDF += (s, args) =>
                {
                    args.SavePath = savePath;
                };

                print.StartPrint(queue);
            }
            finally
            {
                outputFormat.DpiChoise = previousDpiChoice;
            }
        }

        private static bool IsExtensionPDF(string extension)
        {
            extension = extension.ToLower();
            if (extension.StartsWith(".")) extension = extension.Substring(1, extension.Length - 1);
            return extension == "pdf";
        }

        private static ImageFormat GetImageFormatFromExtension(string extension)
        {
            extension = extension.ToLower();
            if (extension.StartsWith(".")) extension = extension.Substring(1, extension.Length - 1);
            switch (extension)
            {
                case "png":
                    return ImageFormat.Png;
                case "jpg":
                case "jpeg":
                    return ImageFormat.Jpeg;
                case "bmp":
                case "dib":
                    return ImageFormat.Bmp;
                case "tif":
                case "tiff":
                    return ImageFormat.Tiff;
                default:
                    throw new InvalidOperationException("Unsupported image format: " + extension);
            }
        }

        private void exportTemplateAsPicture(MemoryStream ms, ImageFormat imageFormat)
        {
            if (dockingPanel.ActiveContent is FormTemplateBase)
            {
                FormTemplateBase ftb = (FormTemplateBase)dockingPanel.ActiveContent;
                if (ftb != null)
                {
                    Template currentTemplate = ftb.GetTemplate();
                    TemplateConverter.ExportTemplateAsPicture(ms, imageFormat, currentTemplate);
                }
            }
        }

        private System.Drawing.Imaging.ImageCodecInfo GetEncoder(System.Drawing.Imaging.ImageFormat format)
        {
            System.Drawing.Imaging.ImageCodecInfo[] codecs = System.Drawing.Imaging.ImageCodecInfo.GetImageDecoders();
            foreach (System.Drawing.Imaging.ImageCodecInfo codec in codecs)
                if (codec.FormatID == format.Guid)
                    return codec;

            return null;
        }

        private void exitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ChangePasswordToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (ChangePasswordForm cpf = new ChangePasswordForm())
            {
                cpf.ShowDialog();
            }
        }

        private void FilterStoreGroupsMenuItem_Click()
        {
            Messenger.Send(Messenger.Events.InvokeStoreGroupFilterDialog);
        }

        private void manualToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                Process.Start(new CustomerController(AppData.Instance).GetManualUrl());
            }
            catch (System.ComponentModel.Win32Exception ex)
            {
                // Happens if no default browser could be detected.
                if (ex.ErrorCode == -2147467259)
                    MessageBoxes.CantFindDefaultBrowserManual(this);
                else
                    throw ex;
            }
        }

        private void manageAddressesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (FormAddressEditEnMasse faeem = new FormAddressEditEnMasse())
            {
                faeem.ShowDialog();
            }
        }

        private void manageAddressListsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (FormAddressLists formAddressLists = new FormAddressLists())
            {
                formAddressLists.ShowDialog();
            }
        }

        private void manageCustomerGroupsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (var mcg = new ManageCustomerGroups())
            {
                var result = mcg.ShowDialog();
                if (result == System.Windows.Forms.DialogResult.OK)
                    _syncmanager.BeginSynchronize();
            }
        }

        private void manageMetaValuesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (var metaValueForm = new FormFillMeta(CustomerController.GetCustomerName(AppData.Instance.CurrentUser.CustomerId)))
            {
                if (metaValueForm.ShowDialog() == DialogResult.OK)
                {
                    new MetaController(AppData.Instance).SaveMetaValues(metaValueForm.ValidatedFields, null);
                    
                    _eslRenderJobProcessor.SetEslProvider(new Controllers.Plugins.PluginController(AppData.Instance).GetPlugin<IEslProvider>());

                    if (metaValueForm.ValidatedFields.Any(vf => vf.FieldIdentifier == MetaValue.Fields.CampaignSortingRule))
                        dockerProductSearch?.RefreshProductList();

                    RefreshMetaProductOnOpenTemplates();
                    TemplateController.ClearTemplateThumbsContainingMetaValue(metaValueForm.ValidatedFields.Select(vf => vf.FieldIdentifier));
                    _syncmanager.BeginSynchronize();
                }
            }
        }

        private void workModeSwitched(WorkMode newMode, RibbonUiState.DesignMode designMode = RibbonUiState.DesignMode.General)
        {
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.PrintshopServer) || newMode == currentWorkMode)
                return;

            if (newMode == WorkMode.Production)
                DesignModeChanged(RibbonUiState.DesignMode.LeavingDesign);
            else if (newMode == WorkMode.Design) // always set when design to refresh ui state for newly selected template.
                DesignModeChanged(designMode);
            currentWorkMode = newMode;
            splitter1.Refresh();
            ShiftDesignModeHorizontalScrollbar(newMode == WorkMode.Design && dsContentBar1.Visible);
        }


        public void ShowFlowTab()
        {
            DesignModeChanged(RibbonUiState.DesignMode.Flow);
        }

        private void DesignModeInfoDocker()
        {
            var activeForm = GetActiveFormTemplateBase();
            if (activeForm is FormTemplateDesign ftd)
            {

                if (!ftd.ShowInfoDocker && infoDocker.Visible)
                    HideDesignInfo();
                else if (ftd.ShowInfoDocker && !infoDocker.Visible)
                    ShowDesignInfo();
            }
        }

        private void InfoDocker_Closing(object sender, EventArgs e)
        {
            if (GetActiveFormTemplateBase() is FormTemplateDesign ftd)
                ftd.SwapInfoDockerState();
            HideDesignInfo();
        }

        private void ShowDesignInfo()
        {
            if (infoDocker == null)
                return;
            var activeForm = GetActiveFormTemplateBase();
            if (activeForm is FormTemplateDesign ftd)
            {
                var template = ftd.GetTemplate();
                infoDocker.Location = new Point(dockingPanel.Left + 55, dockingPanel.Top + 105);
                infoDocker.LoadInformation(template.Products.Clone() as TemplateProducts, template.TemplateInformation, ftd.ProductIndexIsVisible, ftd.LanguageIndexIsVisible);
                infoDocker.SetMediaTypes(template.OutputFormat.RecomendedMediaTypes);
                infoDocker.BringToFront();
                infoDocker.Visible = true;
            }
        }

        private void HideDesignInfo()
        {
            if (infoDocker == null)
                return;
            infoDocker.UnloadInformation();
            infoDocker.Visible = false;
        }

        /// <summary>Updates the content of the <see cref="infoDocker"/> if it is visible.</summary>
        private void ReloadDesignInfo()
        {
            if (infoDocker == null)
                return;
            var activeForm = GetActiveFormTemplateBase();
            if (activeForm is FormTemplateDesign ftd)
            {
                if (!ftd.ShowInfoDocker && infoDocker.Visible)
                    HideDesignInfo();
                else if (ftd.ShowInfoDocker)
                {
                    ControlHelper.SuspendDrawing(infoDocker);
                    HideDesignInfo();
                    ShowDesignInfo();
                    ControlHelper.ResumeDrawing(infoDocker);
                }
            }
            else if (infoDocker.Visible)
                HideDesignInfo();
        }

        private void DesignModeChanged(RibbonUiState.DesignMode designMode)
        {
            var activeForm = GetActiveFormTemplateBase();
            if (activeForm is FormTemplateDesign ftd)
            {
                var template = ftd.GetTemplate();
                DesignDockerStates? stateToSet = null;
                switch (designMode)
                {
                    case Windows.Controls.TemplateRibbonPanel.RibbonUiState.DesignMode.General:
                        stateToSet = DesignDockerStates.GeneralNormal;
                        break;
                    case Windows.Controls.TemplateRibbonPanel.RibbonUiState.DesignMode.Arrange:
                        stateToSet = DesignDockerStates.Arrange;
                        break;
                    case Windows.Controls.TemplateRibbonPanel.RibbonUiState.DesignMode.Precision:
                        purposes.SetTemplateAndFillPurposes(template);
                        stateToSet = DesignDockerStates.Precision;
                        break;
                    case Windows.Controls.TemplateRibbonPanel.RibbonUiState.DesignMode.Tags:
                        stateToSet = DesignDockerStates.Tag;
                        break;
                    case Windows.Controls.TemplateRibbonPanel.RibbonUiState.DesignMode.Flow:
                        stateToSet = DesignDockerStates.Animation;
                        break;
                }
                if (stateToSet.HasValue)
                {
                    ftd.SetDesignStateWithEventsSupressed(stateToSet.Value);
                }

            }

            UpdatePanelDocker(designMode);
            if (designMode == RibbonUiState.DesignMode.LeavingDesign || designMode == RibbonUiState.DesignMode.Unknown)
                HideDesignInfo();

            // Splitter1 gets refreshed as part of design button color change
            //splitter1.Refresh();
            splitter2.Refresh();

            StopPlayingDesignModePreview();
        }

        private void StopPlayingDesignModePreview()
        {
            StopPlayerProcess();
            UpdateUiOnDesignModePreview(false);
        }

        private void UpdateUiOnDesignModePreview(bool isPlaying)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateUiOnDesignModePreview(isPlaying)));
                return;
            }

            if (dsDocker != null)
                dsDocker.IsProbablyPlaying = isPlaying;

            ToggleEnableDesignModeArea(!isPlaying);
        }

        private void UpdatePanelDocker(Windows.Controls.TemplateRibbonPanel.RibbonUiState.DesignMode designMode)
        {
            Docker dockerToUse = GetPanelDesignModeDockerToUse(designMode);
            if (dockerToUse == null)
            {
                // hide designMode docker and show productList
                panelDockers.Visible = true;
                panelDesignModes.Visible = false;
            }
            else
            {
                panelDockers.Visible = false;
                panelDesignModes.Visible = true;
                if (!panelDesignModes.Controls.Contains(dockerToUse))
                {
                    panelDesignModes.Controls.Clear();
                    panelDesignModes.Controls.Add(dockerToUse);
                }
            }
        }

        private Docker GetPanelDesignModeDockerToUse(RibbonUiState.DesignMode designMode)
        {
            switch (designMode)
            {
                case RibbonUiState.DesignMode.LeavingDesign:
                case RibbonUiState.DesignMode.Unknown:
                case RibbonUiState.DesignMode.General:
                    return null;
                case RibbonUiState.DesignMode.Arrange:
                    return stack;
                case RibbonUiState.DesignMode.Precision:
                    return purposes;
                case RibbonUiState.DesignMode.Tags:
                    return tags;
                case RibbonUiState.DesignMode.Flow:
                    return dsDocker;
                default:
                    throw new ArgumentException($"Recieved unexpected designMode value {designMode}");
            }
        }

        private IQueueableInOutputBar GetProductSearchResultControl()
        {
            return dockerProductSearch.GetSearchResultsControl();
        }

        private void menuStrip1_Resize(object sender, EventArgs e)
        {
            if (searchAndReplaceDocker != null)
                searchAndReplaceDocker.Location = new Point(this.ClientRectangle.Width - searchAndReplaceDocker.Width, searchAndReplaceDocker.Location.Y);
        }

        private void exportTemplateToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentTree != templateSearch.TemplateTree)
                throw new ArgumentException("should never be able to export templates unless in template tree");

            var tree = GetTreeFromTreeId(TreeIDEnumaration.Template);
            // Delete all selected.
            var nodes = tree.SelectedNodes;
            if (nodes != null && nodes.Count > 0)
            {
                if (nodes.Count == 1)
                {
                    var activeForm = GetActiveFormTemplateBase();
                    // sending instanceVersion is not really necessary!
                    using (var exportTemplate = new ExportTemplateForm(activeForm.TemplateGuid, activeForm.GetTemplateCapsule().Name, activeForm.GetTemplate().InstanceVersionNumber))
                    {
                        exportTemplate.SyncDelegate = new ExportTemplateForm.SyncResultDelegate(_syncmanager.Syncronize);
                        exportTemplate.ShowDialog();
                    }
                }
                else
                {
                    var parent = nodes.First().Parent;
                    var templatesToExport = new List<Guid>();
                    var guidsToName = new Dictionary<Guid, string>(); // not sure if this one is necessary!

                    // Read first as any other operations will change the NodeCollection
                    foreach (var node in nodes)
                    {
                        var shoppaNode = node.Tag as ShoppaTreeNode;

                        if (shoppaNode != null)
                        {
                            if (!shoppaNode.IsFolder)
                                templatesToExport.Add(shoppaNode.ContentGuid);

                            if (!guidsToName.ContainsKey(shoppaNode.ContentGuid))
                                guidsToName.Add(shoppaNode.ContentGuid, shoppaNode.Text);
                        }
                    }
                    // we got everything we need! be happy!

                    // need variant of exportTemplateFForm that accepts multiple guid
                    // its rename thingie should provide an example name (copy of {0}, or copy-number);

                    using (var exportTemplates = new ExportTemplatesForm(templatesToExport, guidsToName))
                    {
                        exportTemplates.SyncDelegate = new ExportTemplatesForm.SyncResultDelegate(_syncmanager.Syncronize);
                        exportTemplates.ShowDialog();
                    }
                }

            }
        }

        private void exportTemplateLocallyToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.OEM_OKI))
            {
                if (!PrinterDriverScanner.Instance.AllPrinters.Any())
                {
                    CMessageBox.Show(this, MLS.Instance.TranslateText("MF_DisabledNoOKI"), MLS.Instance.TranslateText("MF_FeatureDisabled"));
                    return;
                }
            }

            using (var dialog = new SaveFileDialog())
            {
                dialog.AddExtension = true;
                dialog.Filter = TEMPLATE_FILE_DIALOG_FILTER;
                dialog.OverwritePrompt = true;

                string fileName = (dockingPanel.ActiveContent as FormTemplateBase)?.GetTemplateCapsule()?.Name;
                if (!string.IsNullOrEmpty(fileName))
                    dialog.FileName = string.Join("_", fileName.Split(Path.GetInvalidFileNameChars()));

                if (dialog.ShowDialog() != DialogResult.OK)
                    return;

                ExportTemplate(dialog.FileName);
            }
        }

        private void importTemplateLocallyToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.AddExtension = true;
                dialog.Filter = TEMPLATE_FILE_DIALOG_FILTER;

                if (dialog.ShowDialog() != DialogResult.OK)
                    return;

                if (!dialog.CheckFileExists)
                    return;

                ImportTemplate(dialog.FileName);
            }
        }

        private void ShowFormAutoSignage()
        {
            if (dsContentBar1.Playlist != null)
                toggleFlowContentBar(false);

            _curFormAutoSignage = new FormAutoSignage(new Action(CloseFormAutoSignage));
            _curFormAutoSignage.ShowDisplayGroupPanel += FormAutoSignage_ShowDisplayGroupPanel;
            _curFormAutoSignage.ShowLegacyDisplayGroupPanel += FormAutoSignage_ShowLegacyDisplayGroupPanel;
            _curFormAutoSignage.GotoTemplate += FormAutoSignage_GotoTemplate;
            _curFormAutoSignage.ShowDialog();
        }

        private void FormAutoSignage_ShowLegacyDisplayGroupPanel(object sender, AutoSignage.Objects.LegacyDisplayGroupEditEventArgs e)
        {
            ShowLegacyDisplayGroupAddPanel(e);
        }

        private void FormAutoSignage_GotoTemplate(object sender, AutoSignage.Objects.DisplayGroupRowEventArgs e)
        {
            if (tabControlContent.SelectedTab != tabPageTemplates)
                tabControlContent.SelectedTab = tabPageTemplates;

            var templateIsSelected = templateSearch.TemplateTree.BrowseToContent(e.Format.TemplateGuid);
            if (!templateIsSelected) return;

            var activeForm = GetActiveFormTemplateBase();
            var template = activeForm.GetTemplate();
            var outputFormatsInLayout = template.OutputFormats.FindAll(o => o.LayoutGuid == e.Format.LayoutGuid);
            var outputFormatToSelect = outputFormatsInLayout.First(of => of.OutputFormatGuid == e.Format.OutputFormatGuid);
            template.SetOutputFormat(outputFormatToSelect);
            var isInDesign = activeForm is FormTemplateDesign;
            currentTemplateLayoutSizes.SetFormats(template, isInDesign, showOnlySelectedFormat: true);
        }

        private void CloseFormAutoSignage()
        {
            _curFormAutoSignage.ShowDisplayGroupPanel -= FormAutoSignage_ShowDisplayGroupPanel;
            _curFormAutoSignage.GotoTemplate -= FormAutoSignage_GotoTemplate;
            _curFormAutoSignage.Dispose();
            _curFormAutoSignage = null;

            if (dsContentBar1.Playlist != null)
                toggleFlowContentBar(true);

            _syncmanager.BeginSynchronize(0);
            if (AppData.Instance.CurrentUser.IsInRole(AddOnsEnumeration.LocalIntegration))
            {
                new HotfolderController(AppData.Instance).WriteDisplayGroupsXmlFile(PosFilter.ExpandedHotFolder);
            }
        }

        private void FormAutoSignage_ShowDisplayGroupPanel(object sender, AutoSignage.Objects.DisplayGroupAddEventArgs e)
        {
            ShowDisplayerGroupAddPanel(e);
        }

        private void tabControlContent_Selected(object sender, TabControlEventArgs e)
        {
            if (!(sender is TabControl))
                return;

            ensureQueueButtonAvailability();

            var treeId = TreeIDEnumaration.Unknown;
            if (tabControlContent.SelectedTab == tabPageTemplates)
            {
                templatesImageListView.KeyUp -= printView_KeyUp;
                if (lastFocusedControl is PrintTree)
                    lastFocusedControl = templateSearch.TemplateTree;
                treeId = TreeIDEnumaration.Template;
            }
            else if (tabControlContent.SelectedTab == tabPagePictures)
            {
                if (lastFocusedControl is PrintTree)
                    lastFocusedControl = pictureSearch.PictureTree;
                treeId = TreeIDEnumaration.Picture;
                // Make sure any folder/template is unselected in contentView
                // otherwise contentView_SelectionChanged will change tree to templateTree
                if (templatesImageListView != null && templatesImageListView.SelectedItems.Count > 0)
                    templatesImageListView.ClearSelection();
            }
            else if (tabControlContent.SelectedTab == tabPageOutputs)
            {
                templatesImageListView.KeyUp += printView_KeyUp;
                treeId = TreeIDEnumaration.PrinterQueue;
            }
            else if (tabControlContent.SelectedTab == tabPageDS)
            {
                treeId = TreeIDEnumaration.Flow;
            }

            if (treeId != TreeIDEnumaration.Unknown)
                EnsureTreeVisibility(treeId);

            if (_previousTreeId == TreeIDEnumaration.PrinterQueue && tabControlContent.SelectedTab != tabPageOutputs && (signChildWindow as FormSignProduction).ShowingPrinterQueueTemplate)
                SaveCheckCurrentQueuedSign();

            if (_previousTreeId == TreeIDEnumaration.Flow && tabControlContent.SelectedTab != tabPageDS && (signChildWindow as FormSignProduction).ShowingFlowQueueTemplate)
                SaveCheckCurrentQueuedFlowSign();

            // Ensure selectibility
            if (currentTree != null && currentNode != null && treeId != TreeIDEnumaration.Unknown)
            {
                if (currentTree is ShoppaTree2)
                {
                    var tree = GetTreeFromTreeId(treeId);
                    if (treeId == TreeIDEnumaration.Template)
                        TemplateTree_SelectionChangedAsync(tree, new EventArgs());
                    else if (treeId == TreeIDEnumaration.Picture)
                        pictureTree_SelectionChanged(tree, new EventArgs());
                }
                else if (currentTree is PrintTree)
                    printTree_SelectionChanged(currentTree, new EventArgs());
            }
            _previousTreeId = treeId;

            ToggleOutputFormatButtonPanelVisibility();

            splitter1.Refresh();
        }

        private void ToggleOutputFormatButtonPanelVisibility()
        {
            if (dockingPanel.ActiveContent is FormTemplateDesign formTemplateDesign)
            {
                var shouldVisible = tabControlContent.SelectedTab == tabPageTemplates;
                formTemplateDesign.SetOutputFormatButtonPanelVisibility(shouldVisible);
            }
        }

        private void ensureQueueButtonAvailability()
        {
            if (dockingPanel.ActiveContent == null)
            {
                return;
            }
            bool shouldBeEnabled;
            if (tabControlContent.SelectedTab == tabPageTemplates || tabControlContent.SelectedTab == tabPageOutputs)
                shouldBeEnabled = true;
            else if (tabControlContent.SelectedTab == tabPagePictures || tabControlContent.SelectedTab == tabPageDS)
                shouldBeEnabled = dockingPanel.ActiveContent.GetType().IsSubclassOf(typeof(FormTemplateBase));
            else
                shouldBeEnabled = false;
            toggleCanQueue(shouldBeEnabled);
        }

        private void toggleCanQueue(bool canQueue)
        {
            if (!canQueue)
                toggleCanQueueSheet(false);

            titleBarStrip1.ChangeActiveState(MenuItemNames.queue, outputBar.AddEnabled = canQueue);

            if (canQueue)
            {
                titleBarStrip1.ChangeActiveState(MenuItemNames.print, true);
            }
            else
            {
                ushort totCopies = QueueController.GetTotalCopies(QueuedOutputStateEnumaration.PrinterQueue);
                titleBarStrip1.ChangeActiveState(MenuItemNames.print, totCopies > 0);
            }
        }

        private void toggleCanQueueSheet(bool canQueue)
        {
            if (canQueue)
            {
                outputBar.AddSheetVisible = GetActiveFormTemplateBase()
                    ?.GetTemplate()
                    ?.GetCurrentOutputFormat()
                    ?.OutputFormatCapsule
                    ?.LabelCount > 0;
            }
            else
                outputBar.AddSheetVisible = false;
        }

        private void syncToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _syncmanager.BeginSynchronize(0);
        }

        void tsr_SearchDoneEvent(object sender, SearchDoneEventArgs e)
        {
            if (e.TreeId == TreeIDEnumaration.Unknown) return;
            var tree = GetTreeFromTreeId(e.TreeId);
            if (e.SelectedTreeNode != null)
            {
                if (e.SelectedTreeNode is ShoppaTreeNode)
                {
                    var node = e.SelectedTreeNode as ShoppaTreeNode;

                    if (node.IsFolder)
                        tree.BrowseToFolder(node.ContentGuid);
                    else
                        tree.BrowseToContent(node.ContentGuid);
                }
            }
            templateSearch.TemplateSearchResult.Collapse();
            tree.Focus();
            if (tree.SelectedNode != null)
                tree.EnsureVisible(tree.SelectedNode);

            templateSearch.SetScrollToSelectedItem();
        }

       

        private void PopulateTreeWithPath(TreeViewAdv tree, TreePath path)
        {
            IEnumerable<TreeNodeAdv> treeNodes = tree.AllNodes;
            foreach (var tag in path.FullPath)
            {
                var nodeFromTree = treeNodes.Where(n => n.Tag is CampaignNode && ((CampaignNode)n.Tag).Text.Equals(((CampaignNode)tag).Text)).FirstOrDefault();
                if (!tag.Equals(path.LastNode))
                {
                    nodeFromTree.Expand();
                    nodeFromTree.Collapse();
                }

                if (nodeFromTree.IsLeaf)
                {
                    break;
                }

                treeNodes = nodeFromTree.Children;
            }
        }

        private void pictureSearchResult_PictureSearchDone(object sender, PictureSearchResult.PictureSearchDoneEventArgs e)
        {
            var tree = GetTreeFromTreeId(e.TreeId);
            bool shouldFocusPictureTree = true;
            if (e.SelectedPictureFolderGuids != null && e.SelectedPictureFolderGuids.Count > 0)
            {
                // Since tree is invisble at this time we are not actually scrolled to the selected node here, this is just so that we are able to get the pictures inside
                tree.BrowseToFolder(e.SelectedPictureFolderGuids[0]);

                using (var picDal = Embedded.Factory.Picture)
                {
                    var pictureList = picDal.GetFolderContent(e.SelectedPictureFolderGuids, new AvailabilityController(AppData.Instance).CustomerIdOverrideFor(e.TreeId));
                    pictureList.Sort((x, y) => string.Compare(x.Name, y.Name));
                    pictureSearch.PicturesImageListView.SetupPictureContent(pictureList);
                }
                if (e.SelectedPicture.HasValue)
                {
                    // Select the picture in the content view
                    var pictureListViewItem = pictureSearch.PicturesImageListView.Items.FirstOrDefault(p => (Guid)p.VirtualItemKey == e.SelectedPicture);
                    if (pictureListViewItem != null)
                    {
                        pictureListViewItem.Selected = true;
                        shouldFocusPictureTree = false;
                    }
                }
            }
            else
            {
                // Closing the search without making selection, show previous selection
                if (tree.SelectedNode != null && tree.SelectedNode.Tag != null)
                {
                    var selectedContentGuids = (tree.SelectedNode.Tag as ShoppaTreeNode).ContentGuids;
                    using (var picDal = Embedded.Factory.Picture)
                    {
                        var pictureList = picDal.GetFolderContent(selectedContentGuids, new AvailabilityController(AppData.Instance).CustomerIdOverrideFor(tree.TreeId));
                        pictureList.Sort((x, y) => string.Compare(x.Name, y.Name));
                        pictureSearch.PicturesImageListView.SetupPictureContent(pictureList);
                    }
                }
            }

            pictureSearch.PictureSearchResult.Hide(raiseEvent: false);
            pictureSearch.PictureTree.Visible = true;

            pictureSearch.RestoreTableLayoutStyles();

            if (shouldFocusPictureTree)
            {
                // Now we can actually browse to the selected node since the tree is visible again
                if (e.SelectedPictureFolderGuids != null && e.SelectedPictureFolderGuids.Count > 0)
                {
                    // Need to deselect the selected node prior to browsing to it, the node will be reselected inside of BrowseToFolder. If we don't do this the scroll will fail for not obvious reasons.
                    if (tree.SelectedNode != null)
                        tree.SelectedNode.IsSelected = false;
                    tree.BrowseToFolder(e.SelectedPictureFolderGuids[0]);
                }
                pictureSearch.PictureTree.Focus();
            }
            if (e.SelectedPicture != null && e.SelectedPicture != Guid.Empty)
                NavigateToPicture(e.SelectedPicture.Value);
        }

        private void MainForm_LocationChanged(object sender, EventArgs e)
        {
            if (saveWindowState)
                WindowStateController.SaveSettings(this);
        }

        private void MainForm_Resize(object sender, EventArgs e)
        {
            UpdateRegionBasedOnWindowState();

            Messenger.Send(Messenger.Events.MainFormResized);
            ResizeRightDockerFanouts();
            ResizeStartpageBox();

            if (saveWindowState)
                WindowStateController.SaveSettings(this);
        }

        private void UpdateRegionBasedOnWindowState()
        {
            if (WindowState == FormWindowState.Maximized)
                Region = null;
            else
                Region = new Region(new Rectangle(0, 0, Width, Height));
        }

        private void ResizeStartpageBox()
        {
            if (_startpageFanout?.Visible ?? false)
            {
                _startpageFanout.Width = dockingPanel.Width + 6;
                _startpageFanout.Height = Math.Min(dockingPanel.Height, SystemInformation.WorkingArea.Height - PointToScreen(dockingPanel.Location).Y);
                //_startpageFanout.Location = dockingPanel.Location;
            }
        }

        public List<Guid> GetSelectedTemplates(int scope)
        {
            var templateController = new TemplateController(AppData.Instance, TemplateCacheInstance.Instance);

            List<Guid> templateGuids = new List<Guid>();

            // only selected
            if (scope == 0)
            {
                var activeForm = GetActiveFormTemplateBase();
                templateGuids.Add(activeForm.TemplateGuid);
            }

            if (scope == 1)
            {
                var tree = GetTreeFromTreeId(TreeIDEnumaration.Template);
                var nodes = tree.SelectedNodes;
                if (nodes != null && nodes.Count > 0)
                {
                    foreach (var node in nodes)
                    {
                        var shoppaNode = node.Tag as ShoppaTreeNode;
                        if (shoppaNode != null)
                        {
                            if (!shoppaNode.IsFolder)
                            {
                                templateGuids.Add(shoppaNode.ContentGuid);
                            }
                            else
                            {
                                templateGuids.AddRange(templateController.GetAllTemplateGuidsUnderFolders(new List<Guid>() { shoppaNode.ContentGuid }));
                            }
                        }
                    }
                }
            }

            if (scope == 2)
            {
                using (var templateDal = Embedded.Factory.Template)
                    templateGuids.AddRange(templateDal.GetAllGuids());
            }
            return templateGuids;
        }

        public Dictionary<Guid, FormTemplateBase> GetTemplateDesignForms(List<Guid> templateGuids)
        {
            Dictionary<Guid, FormTemplateBase> res = new Dictionary<Guid, FormTemplateBase>();

            bool alreadyOpen, openInDesign;
            FormTemplateBase baseForm;
            foreach (var g in templateGuids)
            {
                TryGetOpenTemplate(g, out baseForm, out alreadyOpen, out openInDesign);
                if (alreadyOpen && openInDesign)
                    res[g] = baseForm;
            }
            return res;
        }

        void searchAndReplaceDocker_Closed(object sender, EventArgs e)
        {
            // When searchAndReplaceDocker is hidden the next control will be selected
            // In this case it's TemplateSearchResult which will auto search on selection
            try
            {
                TemplateSearchResult.DisableAutoSemaphore.WaitOne();
                templateSearch.TemplateSearchResult.DisableAutoSearch();
                _scrollholderSearchAndReplace.Hide();
                templateSearch.TemplateSearchResult.EnableAutoSearch();
            }
            finally
            {
                TemplateSearchResult.DisableAutoSemaphore.Release();
            }

            var activeForm = GetActiveFormTemplateBase();
            if (activeForm != null)
                Messenger.Send(Messenger.Events.ActivateContentInDockPanel, activeForm);
        }

        private void managePrintersToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (ManageDestinations md = new ManageDestinations())
            {
                md.ShowDialog();
            }
            _syncmanager.BeginSynchronize();
        }

        private void selectPrinterToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DestinationMediaTyped destination = MediaTypedDestinationHolder.GetDestination();

            if (destination == null)
            {
                CMessageBox.Show(this, MLS.Instance.TranslateText("MF_DisabledNoOKI"), MLS.Instance.TranslateText("MF_FeatureDisabled"));
                return;
            }

            var form = new OkiOutputFormatInfo(PrinterDriverScanner.Instance.AllPrinters) { Parent = this };
            form.SetRelativeLocation((this.Width / 2) - (form.Width / 2), (this.Height / 2) - (form.Height / 2));

            // Get output format from the selected template if possible
            var outputFormat = GetActiveFormTemplateBase()?.GetTemplate()?.OutputFormat;
            if (outputFormat != null)
                form.SetOutputFormat(outputFormat);

            form.SetFinalDestination(MediaTypedDestinationHolder.GetDestination());
            form.Show();

            while (form.Visible)
            {
                Thread.Sleep(10);
                Application.DoEvents();
            }
        }

        /// <summary>
        /// Due to a weird interaction between using MdiContainer = true for MainForm and our DockPanel
        /// we have to set Splitter2.SplitPosition 3 times with non repeating value to make it
        /// work correctly.
        /// </summary>
        private void nudgeSplitter2ToMakeItResizable()
        {
            var initialPosition = splitter2.SplitPosition;

            // iterate 3 times in reverse order so we end up
            // restoring SplitPosition to its initial value on the last iteration.
            for (int i = 2; i >= 0; i--)
            {
                splitter2.SplitPosition = initialPosition + i;
                Application.DoEvents();
            }
        }

        private void SplitContainerTemplateTab_Resize(object sender, EventArgs e)
        {
            SplitContainerTemplateTab_Resize();
            splitter1.Refresh();
        }

        private void SplitContainerTemplateTab_Resize()
        {
            var spliterPanel = this.splitContainerTemplateTab;
            var panel2YCoordinte = spliterPanel.Panel2.Location.Y;
            var outputBarHeight = outputBar.Height;
            var outputFormatHelper = new OutputFormatButtonHelper
            {
                CoordinateY = panel2YCoordinte,
                PaddingBottom = outputBarHeight,
                FlowContentBarOffset = dsContentBar1.Visible ? dsContentBar1.Size.Height : 0
            };
            Messenger.Send(this, Messenger.Events.TemplateLayoutControlResize, outputFormatHelper);
        }

        private void tabControlContent_Resize(object sender, EventArgs e)
        {
            var trashButtons = new Control[] {
                buttonDeleteTemplate,
                buttonDeleteCampaign,
                buttonDeletePicture,
                buttonDeletePlaylists,
                buttonDeleteOutput };
            foreach (var button in trashButtons)
            {
                button.Left = tabControlContent.Width - (button.Width + 12);
            }
        }

        private void UpdateEslFailedPopUp(bool isFailed)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateEslFailedPopUp(isFailed)));
                return;
            }

            _eslSendFailedWarn.UpdatePopUp(isFailed);
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (msg.Msg == (int)User32.WindowMessage.WM_KEYDOWN)
            {
                if (keyData == (Keys.Control | Keys.Q))
                {
                    var clickInfo = new MenuItemClickInfo(MenuItemNames.queue, MenuItemOrigin.File);
                    Messenger.Send(Messenger.Events.MenuStripItemClicked, clickInfo);
                    return true;
                }
                else if (keyData == (Keys.Control | Keys.P))
                {
                    var clickInfo = new MenuItemClickInfo(MenuItemNames.print, MenuItemOrigin.File);
                    Messenger.Send(Messenger.Events.MenuStripItemClicked, clickInfo);
                    return true;
                }

                if (dockingPanel?.ActiveContent is FormTemplateDesign activeDesignForm)
                {
                    DesignDockerStates designMode = DesignDockerStates.Unknown;

                    switch (keyData)
                    {
                        case Keys.Control | Keys.Z:
                            activeDesignForm.DoUndo();
                            return true;
                        case Keys.Control | Keys.Shift | Keys.Z:
                            activeDesignForm.DoRedo();
                            return true;
                        case Keys.D1:
                            designMode = DesignDockerStates.GeneralNormal;
                            break;
                        case Keys.D2:
                            designMode = DesignDockerStates.Arrange;
                            break;
                        case Keys.D3:
                            designMode = DesignDockerStates.Precision;
                            break;
                        case Keys.D4:
                            designMode = DesignDockerStates.Tag;
                            break;
                        case Keys.D5:
                            designMode = DesignDockerStates.Animation;
                            break;
                        case Keys.Control | Keys.F:
                            ShowSearchAndReplace(activeDesignForm);
                            break;
                        case Keys.Control | Keys.S:
                            activeDesignForm.SaveTemplate();
                            break;
                    }

                    if (designMode != DesignDockerStates.Unknown)
                    {
                        activeDesignForm.ChangeDesignMode(designMode);
                        return true;
                    }
                }
            }

            return false;
        }

        protected override int GetBorderThickness()
        {
            return ThemeProperties.GlowThickness;
        }

        internal void ShowSearchAndReplace(FormTemplateDesign formTemplateDesign)
        {
            searchAndReplaceDocker.designWindow = formTemplateDesign;
            _scrollholderSearchAndReplace.Show();
        }

        internal void SetNewGdiElementFactory()
        {
            GDI_ElementFactory.Instance = new GDI_ElementFactory(new DpiHelper(), MLS.Instance.TranslateText("GDI_BraCode"), Themes.Current.Colors, MLS.Instance.TranslateText("GDI_WebError"));
        }
    }
}

